# Fixing Email Security Warnings

## Why You're Seeing "This message seems dangerous"

Email providers (Gmail, Outlook, etc.) show this warning when they detect:

1. **Unverified sender domain**
2. **Suspicious links** (like localhost URLs)
3. **New sender reputation**

## Solutions

### 1. For Development/Testing (Quick Fix)

Use <PERSON>send's test email feature:
- When signing up for Resend, they give you a verified test email
- Use this email as your `RESEND_FROM_EMAIL` temporarily:
  ```bash
  RESEND_FROM_EMAIL=<EMAIL>  # Resend's test email
  ```

### 2. For Production (Proper Fix)

#### A. Verify Your Domain with Resend

1. Go to https://resend.com/domains
2. Click "Add Domain"
3. Enter your domain (e.g., `beatgen.com`)
4. Add the DNS records they provide:
   - SPF record
   - DKIM records
   - (Optional) DMARC record

5. Wait for verification (usually takes a few minutes)

6. Update your `.env`:
   ```bash
   RESEND_FROM_EMAIL=<EMAIL>  # Your verified domain
   ```

#### B. Use Production URLs

For local testing with production-like URLs:

1. **Option 1: Use ngrok** (Recommended for testing)
   ```bash
   # Install ngrok
   brew install ngrok
   
   # Expose your frontend
   ngrok http 5173
   
   # Use the ngrok URL in your email redirect
   ```

2. **Option 2: Update your hosts file**
   ```bash
   # Add to /etc/hosts
   127.0.0.1 local.beatgen.com
   
   # Update your .env
   LOCAL_FRONTEND_BASE_URL=https://local.beatgen.com:5173
   ```

### 3. Email Best Practices

Update the email service to be less "spammy":

```python
# In email_service.py, update the subject line
"subject": "Reset Your BeatGen Password - Action Required",  # More formal

# Add a footer with unsubscribe info
<div class="footer">
    <p>© 2024 BeatGen. All rights reserved.</p>
    <p>You received this email because a password reset was requested for your account.</p>
    <p>BeatGen, Inc. | San Francisco, CA</p>
</div>
```

### 4. Temporary Workaround

While developing, you can:
1. Click "Report spam" → "Looks safe" in Gmail
2. Add the sender to your contacts
3. Check spam folder if emails don't appear in inbox

## Testing Email Deliverability

Use Resend's email testing tools:
1. Go to https://resend.com/emails
2. View your sent emails and their status
3. Check for bounce rates and spam scores

## Environment-Specific Configuration

```bash
# Development (.env.development)
RESEND_FROM_EMAIL=<EMAIL>
LOCAL_FRONTEND_BASE_URL=https://localhost:5173

# Production (.env.production)
RESEND_FROM_EMAIL=<EMAIL>  # After domain verification
PROD_FRONTEND_BASE_URL=https://app.beatgen.com
```

## Next Steps

1. **For immediate testing**: Use `<EMAIL>` as sender
2. **For production**: Verify your domain with Resend
3. **Monitor deliverability**: Check Resend dashboard for email stats

Remember: Email deliverability improves over time as your sender reputation builds!