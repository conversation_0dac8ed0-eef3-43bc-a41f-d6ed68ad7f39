# Supabase Email Rate Limits

Even on paid tiers, Supabase has email rate limits:

## Default Limits (even on Pro tier)
- **3 emails per hour** per email address (recipient)
- **30 emails per hour** total across all recipients

## To Fix This:

### Option 1: Increase Rate Limits
1. Go to Supabase Dashboard
2. Settings > Auth
3. Look for "Email Rate Limits"
4. Increase the limits:
   - Rate limit per email: 10-20 per hour
   - Overall rate limit: 100-500 per hour

### Option 2: Configure Custom SMTP (Recommended)
Since you already have Resend configured, use it with Supabase:

1. **In Supabase Dashboard > Settings > Auth**
2. **Scroll to "SMTP Settings"**
3. **Enable "Custom SMTP"**
4. **Add these settings:**
   ```
   Sender email: <EMAIL>
   Sender name: BeatGen
   Host: smtp.resend.com
   Port: 587
   Username: resend
   Password: [Your Resend API key - re_ZtbrcyDz_DNDsBLDb4BfVpWCZdgPsb5UX]
   ```
5. **Save changes**

### Option 3: Check Current Usage
1. Go to Authentication > Logs
2. Filter by "Password Recovery"
3. You'll see how many emails were sent in the past hour

## Benefits of Custom SMTP
- No Supabase rate limits
- Better deliverability
- Your own email reputation
- Custom email templates

## Testing Different Emails
For now, you can test with different email addresses to avoid the per-email rate limit:
- <EMAIL>
- <EMAIL>
- etc.

The error "email rate limit exceeded" specifically means you've sent too many password reset emails to the same email address (<EMAIL>) within an hour.