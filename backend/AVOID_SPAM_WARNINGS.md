# Avoiding Email Spam Warnings

Since your domain is verified with Resend, the spam warning is caused by the `localhost` URLs in development.

## Solutions:

### 1. Use ngrok for Testing (Recommended)
```bash
# Install ngrok
brew install ngrok

# In terminal 1: Run your frontend
cd frontend && npm run dev

# In terminal 2: Expose it via ngrok
ngrok http 5173

# You'll get a URL like: https://abc123.ngrok.io
# Update your .env temporarily:
LOCAL_FRONTEND_BASE_URL=https://abc123.ngrok.io
```

### 2. Test with Production URLs
Temporarily set your environment to production mode for testing:
```bash
APP_ENV=prod
```
This will use `https://app.beatgen.com` in emails instead of localhost.

### 3. Use a Staging Environment
Create a staging subdomain:
```bash
# Add to your .env
STAGING_FRONTEND_BASE_URL=https://staging.beatgen.com

# Update config.py to support staging
```

### 4. Override for Email Testing
Add this to your `.env` for testing:
```bash
# Force production URLs in emails even in dev mode
EMAIL_BASE_URL_OVERRIDE=https://app.beatgen.com
```

Then update the email service to use this override.

## Why Localhost URLs Trigger Warnings

Email providers flag emails containing:
- `localhost` URLs (obvious phishing indicator)
- `192.168.x.x` or `127.0.0.1` IPs
- Non-HTTPS links
- Unusual ports like `:5173`

## Best Practice for Development

1. **Local Development**: Accept the warning and click "Looks safe"
2. **Testing with Others**: Use ngrok or deploy to staging
3. **Production**: Always use proper domain URLs

## Quick Test

To verify emails work without warnings:
1. Temporarily change `APP_ENV=prod` in your `.env`
2. Restart the backend
3. Test password reset
4. The email should not show warnings (uses app.beatgen.com)
5. Change back to `APP_ENV=dev` after testing

The email system is working correctly - the warning is purely about the localhost URLs!