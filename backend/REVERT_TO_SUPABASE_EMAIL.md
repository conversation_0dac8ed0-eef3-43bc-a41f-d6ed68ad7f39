# Password Reset Email - Back to Supabase

We've reverted to using Supabase's built-in email functionality for password reset.

## Current Setup

The password reset flow now:
1. Uses Supabase's `reset_password_email()` method
2. Sends emails via Supabase's configured email provider
3. Includes the redirect URL to your frontend

## To Enable Emails in Supabase

1. Go to your Supabase Dashboard
2. Navigate to Authentication > Email Templates
3. Make sure "Enable email confirmations" is ON
4. Check the "Reset Password" template

## Email Provider Options in Supabase

Supabase supports multiple email providers:
- **Built-in** (default, limited to 3 emails/hour)
- **Custom SMTP** (your own email server)
- **Resend** (can be configured in Supabase)
- **SendGrid**
- **AWS SES**

## To Use Resend with Supabase

1. In Supabase Dashboard > Settings > Auth
2. Scroll to "SMTP Settings"
3. Enable "Custom SMTP"
4. Add Resend settings:
   - Host: `smtp.resend.com`
   - Port: `465` (SSL) or `587` (TLS)
   - Username: `resend`
   - Password: Your Resend API key
   - Sender email: `<EMAIL>`

## Testing

1. Go to login page
2. Click "Forgot password?"
3. Enter email
4. Check inbox (might be in spam if using localhost URLs)

## Note on Localhost URLs

The spam warning for localhost URLs is normal in development. It won't happen in production with real URLs.

## If Emails Still Don't Send

Check:
1. Supabase Dashboard > Logs > Auth Logs
2. Email templates are enabled
3. Your Supabase project has email sending enabled