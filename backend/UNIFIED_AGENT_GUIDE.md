# Unified Music Agent Implementation Guide

## Overview

The Unified Music Agent eliminates the need for manual mode switching by automatically detecting user intent and responding appropriately. Users can now interact with the AI assistant naturally, like a real chatbot, without having to specify whether they want to generate, edit, chat, or analyze.

## Key Features

### 🤖 Automatic Mode Detection
- **Chat Mode**: General conversation about music, questions, advice
- **Generate Mode**: Creating new MIDI compositions
- **Generate-Audio Mode**: Creating audio tracks using AI models
- **Edit Mode**: Modifying existing tracks (automatically triggered when tracks are mentioned)
- **Analyze Mode**: Analyzing musical content, providing feedback

### 🎯 Track Mention Detection
When users mention track names or IDs in their messages, the agent automatically switches to edit mode:
```
User: "Can you make my piano track more upbeat?"
Agent: → Automatically detects "piano track" → Switches to edit mode
```

### 💬 Conversational Flow
The agent asks clarifying questions when user intent is unclear:
```
User: "Um, maybe something musical?"
Agent: "I want to make sure I understand what you're looking for. What would you like me to help you with today?"
```

## API Usage

### Using the Unified Mode

Send requests to `/api/assistant/request` with `mode: "unified"`:

```json
{
  "mode": "unified",
  "prompt": "Create a happy jazz song in Bb major",
  "model": "gpt-4",
  "context": {
    "tracks": [
      {"id": "track-1", "name": "piano melody", "type": "MIDI"},
      {"id": "track-2", "name": "drum beat", "type": "DRUM"}
    ]
  }
}
```

### Legacy Mode Support

The existing modes still work for backward compatibility:
- `"generate"` - Direct music generation
- `"generate-audio"` - Direct audio generation  
- `"edit"` - Direct edit mode
- `"chat"` - Direct chat mode

## Example Conversations

### 1. Music Generation (Auto-Detected)
```
User: "Create a sad ballad in D minor"
Agent: Detected Intent: generate (confidence: 0.9)
→ "I'll create a new MIDI composition based on your request..."
```

### 2. Track Editing (Auto-Detected)
```
User: "Make my drum beat more aggressive"
Context: [{"name": "drum beat", "type": "DRUM"}]
Agent: Detected Intent: edit (confidence: 0.9) 
→ "I can see you're referring to: drum beat. What specific changes would you like to make?"
```

### 3. Audio Generation (Auto-Detected)
```
User: "Generate an audio file with a smooth saxophone melody"
Agent: Detected Intent: generate-audio (confidence: 0.8)
→ "I'll create audio content using AI audio generation..."
```

### 4. Analysis Request (Auto-Detected)
```
User: "What do you think about this chord progression?"
Agent: Detected Intent: analyze (confidence: 0.7)
→ "I'll analyze the musical content. What aspects would you like me to focus on?"
```

### 5. General Chat (Auto-Detected)
```
User: "What's the difference between major and minor scales?"
Agent: Detected Intent: chat (confidence: 0.8)
→ Provides educational explanation about scales
```

## Implementation Details

### Files Created/Modified

1. **`app2/llm/agents/unified_music_agent.py`**
   - Main unified agent implementation
   - Intent detection using LLM analysis
   - Automatic mode switching logic
   - Track mention detection

2. **`app2/api/routes/assistant_streaming.py`**
   - Added "unified" mode support
   - New `process_unified_request()` function
   - Updated request model to include unified mode

3. **`test_unified_agent.py`**
   - Test script demonstrating different conversation flows
   - Mock implementations for testing without database

### Architecture Components

#### UserIntent Detection
The agent uses LLM analysis to determine:
- **Mode**: Which mode should handle the request
- **Confidence**: How certain the detection is (0.0-1.0)
- **Reasoning**: Why this mode was chosen
- **Clarification Needed**: Whether to ask follow-up questions
- **Detected Tracks**: Any tracks mentioned in the message
- **Parameters**: Extracted parameters (tempo, key, etc.)

#### ConversationContext
Maintains state across conversations:
- **User ID**: Current user
- **History**: Previous messages in conversation
- **Current Mode**: Active mode
- **Active Tracks**: Currently referenced tracks
- **Session Parameters**: Persistent settings

#### Track Detection
Uses pattern matching and context analysis to find:
- Track names mentioned in user messages
- Track IDs (UUID format)
- Automatic edit mode triggering

## Error Handling

The implementation follows the codebase's "fail loudly" philosophy:
- No silent fallbacks
- Detailed error logging  
- Structured error messages via SSE
- Proper exception chaining
- Cancellation support

## Testing

Run the test script to see the unified agent in action:

```bash
cd /path/to/backend
python test_unified_agent.py
```

This demonstrates:
- ✅ Automatic intent detection for different request types
- ✅ Track mention detection and edit mode switching
- ✅ Clarification requests for unclear intents
- ✅ Conversation flow handling

## Benefits

1. **Improved UX**: Users don't need to know about modes - they just talk naturally
2. **Reduced Cognitive Load**: No need to remember which mode to use
3. **Automatic Context**: Track mentions automatically trigger appropriate responses
4. **Flexible**: Still supports existing explicit modes for advanced users
5. **Conversational**: Feels like chatting with a knowledgeable music assistant

## Future Enhancements

- **Multi-turn Conversations**: Better context preservation across multiple exchanges
- **Smart Suggestions**: Proactive suggestions based on conversation history
- **Voice Integration**: Natural language processing for voice inputs
- **Personalization**: Learning user preferences over time
- **Project Context**: Automatic project-aware suggestions and actions