#!/usr/bin/env python3
"""
Migration script to multiply all existing user credit balances by 10x.

This script updates existing UserCredits records to match the new 10x granular credit system.
It multiplies balance, monthly_allocation, total_allocated, and total_used by 10.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import List
import uuid

from dotenv import load_dotenv
from sqlmodel import select
from sqlalchemy import text

# Load environment variables
load_dotenv()

from app2.models.user import User
from app2.models.credits import UserCredits, CreditTransaction, CreditTransactionType
from app2.infrastructure.database.sqlmodel_client import get_async_session

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def get_all_user_credits() -> List[UserCredits]:
    """Get all UserCredits records that need to be migrated."""
    async with get_async_session() as session:
        stmt = select(UserCredits)
        result = await session.execute(stmt)
        all_credits = result.scalars().all()
        
        logger.info(f"Found {len(all_credits)} UserCredits records to migrate")
        return list(all_credits)


async def migrate_user_credits_10x(user_credits_list: List[UserCredits]) -> int:
    """Multiply all credit amounts by 10 for granular pricing."""
    async with get_async_session() as session:
        migrated_count = 0
        
        for user_credits in user_credits_list:
            try:
                # Store original values for logging
                original_balance = user_credits.balance
                original_monthly = user_credits.monthly_allocation
                original_allocated = user_credits.total_allocated
                original_used = user_credits.total_used
                
                # Skip pro users with unlimited credits (999999 or 9999990)
                if user_credits.subscription_tier == "pro" and user_credits.balance >= 999999:
                    # Update pro users to new unlimited amount
                    user_credits.balance = 9999990
                    logger.info(f"Updated pro user {user_credits.user_id} to new unlimited balance")
                else:
                    # Multiply all amounts by 10
                    user_credits.balance = user_credits.balance * 10
                
                # Update monthly allocation (except for unlimited pro = -1)
                if user_credits.monthly_allocation != -1:
                    user_credits.monthly_allocation = user_credits.monthly_allocation * 10
                
                # Update totals
                user_credits.total_allocated = user_credits.total_allocated * 10
                user_credits.total_used = user_credits.total_used * 10
                user_credits.updated_at = datetime.now(timezone.utc)
                
                # Create audit transaction
                transaction = CreditTransaction(
                    user_id=user_credits.user_id,
                    user_credits_id=user_credits.id,
                    transaction_type=CreditTransactionType.ADMIN_ADJUSTMENT,
                    amount=user_credits.balance - (original_balance * 10 if original_balance < 999999 else 0),
                    balance_after=user_credits.balance,
                    description="10x credit granularity migration",
                    transaction_metadata={
                        "migration_type": "10x_granularity",
                        "original_balance": original_balance,
                        "original_monthly": original_monthly,
                        "original_allocated": original_allocated,
                        "original_used": original_used
                    }
                )
                
                session.add(user_credits)
                session.add(transaction)
                migrated_count += 1
                
                logger.info(f"Migrated user {user_credits.user_id}: "
                           f"balance {original_balance} → {user_credits.balance}, "
                           f"monthly {original_monthly} → {user_credits.monthly_allocation}")
                
            except Exception as e:
                logger.error(f"Failed to migrate user_credits {user_credits.id}: {e}")
                continue
        
        await session.commit()
        return migrated_count


async def verify_migration() -> bool:
    """Verify that the migration was successful."""
    async with get_async_session() as session:
        # Check a few sample records
        stmt = select(UserCredits).limit(5)
        result = await session.execute(stmt)
        sample_records = result.scalars().all()
        
        logger.info("Sample migrated records:")
        for record in sample_records:
            logger.info(f"User {record.user_id}: balance={record.balance}, "
                       f"monthly={record.monthly_allocation}, tier={record.subscription_tier}")
        
        # Check for any records that might not have been migrated properly
        # Free tier should have balance of 100+ (was 10, now 100+)
        stmt_free = select(UserCredits).where(
            UserCredits.subscription_tier == "free",
            UserCredits.balance < 50  # Should be at least 100 now
        )
        result_free = await session.execute(stmt_free)
        unmigrated_free = result_free.scalars().all()
        
        if unmigrated_free:
            logger.warning(f"Found {len(unmigrated_free)} free tier users with suspiciously low balances")
            return False
        
        logger.info("Migration verification passed ✅")
        return True


async def run_migration():
    """Run the complete 10x credit granularity migration."""
    logger.info("Starting 10x credit granularity migration...")
    
    try:
        # Get all user credits
        user_credits_list = await get_all_user_credits()
        
        if not user_credits_list:
            logger.info("No UserCredits records found. Nothing to migrate.")
            return
        
        logger.info(f"Migrating {len(user_credits_list)} UserCredits records...")
        
        # Run migration
        migrated_count = await migrate_user_credits_10x(user_credits_list)
        
        logger.info(f"Migration completed! {migrated_count} UserCredits records migrated.")
        
        # Verify migration
        if await verify_migration():
            logger.info("✅ Migration successful and verified!")
        else:
            logger.error("❌ Migration verification failed!")
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(run_migration())