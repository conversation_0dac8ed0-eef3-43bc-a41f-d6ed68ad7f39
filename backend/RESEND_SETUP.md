# Resend Email Setup

## Overview
This document explains how to set up Resen<PERSON> for sending password reset emails.

## Required Environment Variables

Add the following to your `.env` file:

```bash
# Resend Configuration
RESEND_API_KEY=your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>  # Or your verified domain
RESEND_FROM_NAME=BeatGen
```

## Getting Started with Resend

1. **Sign up for Resend**
   - Go to https://resend.com and create an account
   - You get 3,000 emails/month for free

2. **Get your API Key**
   - After signing up, go to https://resend.com/api-keys
   - Create a new API key
   - Copy the key and add it to your `.env` file

3. **Verify your domain (Optional but recommended)**
   - Go to https://resend.com/domains
   - Add your domain (e.g., beatgen.com)
   - Follow the DNS verification steps
   - Once verified, you can send from any email address on that domain

4. **Test sending**
   - If you haven't verified a domain, you can only send to your own email
   - For testing, use the email you signed up with

## Implementation Details

The password reset flow now works as follows:

1. User requests password reset
2. Back<PERSON> generates a recovery token via Supabase Admin API
3. Backend sends email via Resend API with the reset link
4. User clicks link and is redirected to the reset password page
5. User submits new password with the recovery token

## Troubleshooting

### Email not sending
- Check that RESEND_API_KEY is set in your `.env` file
- Check backend logs for any error messages
- Verify your domain if sending to other email addresses

### Invalid API Key
- Make sure you're using the correct API key from Resend dashboard
- API keys start with `re_` 

### Rate Limits
- Free tier: 100 emails/day, 3,000 emails/month
- If you hit limits, consider upgrading your plan

## Testing the Password Reset Flow

1. Start the backend server
2. Navigate to the login page
3. Click "Forgot password?"
4. Enter your email address
5. Check your inbox for the reset email
6. Click the link and reset your password

## Security Notes

- Never commit your RESEND_API_KEY to version control
- The reset tokens expire after 1 hour for security
- Always use HTTPS in production for the reset links