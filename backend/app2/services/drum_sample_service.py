"""
Service for managing public drum samples.
"""

from typing import Dict, Any, List, Optional
import uuid
import traceback

from app2.core.logging import get_service_logger
from app2.core.exceptions import ServiceException, NotFoundException, StorageException
from app2.repositories.drum_sample_public_repository import DrumSamplePublicRepository
from app2.models.public_models.drum_samples import (
    DrumSamplePublicRead,
)

logger = get_service_logger("drum_sample_service")


class DrumSampleService:
    """Service layer for operations on public drum samples."""

    def __init__(self, drum_sample_repository: DrumSamplePublicRepository):
        """
        Initialize the service with the drum sample repository.

        Args:
            drum_sample_repository: The repository for drum sample operations.
        """
        self.drum_sample_repository = drum_sample_repository

    async def get_sample_by_id(self, sample_id: uuid.UUID) -> DrumSamplePublicRead:
        """
        Get a specific drum sample by its ID.

        Args:
            sample_id: The ID of the drum sample.

        Returns:
            The drum sample data (read model).

        Raises:
            NotFoundException: If the drum sample is not found.
            ServiceException: If there's an operational failure.
        """
        logger.info(f"Getting drum sample with ID: {sample_id}")
        try:
            sample = await self.drum_sample_repository.get_by_id(sample_id)
            return DrumSamplePublicRead.model_validate(sample)
        except NotFoundException:
            logger.warning(f"Drum sample with ID {sample_id} not found.")
            raise
        except Exception as e:
            logger.error(f"Error getting drum sample by ID {sample_id}: {str(e)}")
            logger.error(traceback.format_exc())
            raise ServiceException(f"Failed to get drum sample: {str(e)}")

    async def get_all_samples(self, **filters) -> List[DrumSamplePublicRead]:
        """
        Get all public drum samples, with optional filters.

        Args:
            **filters: Optional filter criteria (e.g., name="kick").

        Returns:
            A list of drum samples (read models).

        Raises:
            ServiceException: If there's an operational failure.
        """
        filter_str = ", ".join(f"{k}={v}" for k, v in filters.items())
        logger.info(f"Getting all drum samples with filters: {filter_str}")
        try:
            samples = await self.drum_sample_repository.get_all(**filters)
            return [DrumSamplePublicRead.model_validate(sample) for sample in samples]
        except Exception as e:
            logger.error(f"Error getting all drum samples: {str(e)}")
            logger.error(traceback.format_exc())
            raise ServiceException(f"Failed to get drum samples: {str(e)}")

    async def find_sample_by_name(self, name: str) -> Optional[DrumSamplePublicRead]:
        """
        Find a drum sample by its name.

        Args:
            name: The name of the drum sample to find.

        Returns:
            The drum sample data (read model) if found, otherwise None.

        Raises:
            ServiceException: If there's an operational failure.
        """
        logger.info(f"Finding drum sample by name: {name}")
        try:
            sample = await self.drum_sample_repository.find_by_name(name)
            if sample:
                return DrumSamplePublicRead.model_validate(sample)
            return None
        except Exception as e:
            logger.error(f"Error finding drum sample by name '{name}': {str(e)}")
            logger.error(traceback.format_exc())
            raise ServiceException(f"Failed to find drum sample by name: {str(e)}")

    # Note: These create/update/delete methods assume administrative actions
    # or a process where file upload happens separately. Access control might
    # need to be added depending on the application's requirements.

    async def create_sample_record(
        self, sample_data: Dict[str, Any]
    ) -> DrumSamplePublicRead:
        """
        Create a new drum sample database record.
        Assumes the actual file is uploaded separately using the URL generated by create_sample_upload_url.

        Args:
            sample_data: The data for the new drum sample record (e.g., name, description).
                         Should conform to DrumSamplePublicCreate model.

        Returns:
            The created drum sample data (read model).

        Raises:
            ServiceException: If the creation fails.
        """
        logger.info(f"Creating new drum sample record with data: {sample_data}")
        try:
            # Potentially validate sample_data against a Pydantic Create model here
            created_sample = await self.drum_sample_repository.create(sample_data)
            return DrumSamplePublicRead.model_validate(created_sample)
        except Exception as e:
            logger.error(f"Error creating drum sample record: {str(e)}")
            logger.error(traceback.format_exc())
            raise ServiceException(f"Failed to create drum sample record: {str(e)}")

    async def update_sample_record(
        self, sample_id: uuid.UUID, sample_data: Dict[str, Any]
    ) -> DrumSamplePublicRead:
        """
        Update an existing drum sample database record.

        Args:
            sample_id: The ID of the drum sample record to update.
            sample_data: The updated data for the drum sample record.
                         Should conform to a Pydantic Update model.

        Returns:
            The updated drum sample data (read model).

        Raises:
            NotFoundException: If the drum sample record is not found.
            ServiceException: If the update fails.
        """
        logger.info(
            f"Updating drum sample record ID {sample_id} with data: {sample_data}"
        )
        try:
            # Potentially validate sample_data against a Pydantic Update model here
            updated_sample = await self.drum_sample_repository.update(
                sample_id, sample_data
            )
            return DrumSamplePublicRead.model_validate(updated_sample)
        except NotFoundException:
            logger.warning(
                f"Drum sample record with ID {sample_id} not found for update."
            )
            raise
        except Exception as e:
            logger.error(f"Error updating drum sample record {sample_id}: {str(e)}")
            logger.error(traceback.format_exc())
            raise ServiceException(f"Failed to update drum sample record: {str(e)}")

    async def delete_sample_record(self, sample_id: uuid.UUID) -> bool:
        """
        Delete a drum sample database record.
        Note: This typically does NOT delete the file from storage. Separate cleanup might be needed.

        Args:
            sample_id: The ID of the drum sample record to delete.

        Returns:
            True if the deletion was successful.

        Raises:
            NotFoundException: If the drum sample record is not found.
            ServiceException: If the deletion fails.
        """
        logger.info(f"Deleting drum sample record with ID: {sample_id}")
        try:
            deleted = await self.drum_sample_repository.delete(sample_id)
            logger.info(
                f"Deletion result for drum sample record {sample_id}: {deleted}"
            )
            return deleted
        except NotFoundException:
            logger.warning(
                f"Drum sample record with ID {sample_id} not found for deletion."
            )
            raise
        except Exception as e:
            logger.error(f"Error deleting drum sample record {sample_id}: {str(e)}")
            logger.error(traceback.format_exc())
            raise ServiceException(f"Failed to delete drum sample record: {str(e)}")

    async def create_sample_upload_url(
        self, file_name: str, file_id: uuid.UUID
    ) -> Dict[str, str]:
        """
        Create a signed URL for uploading a drum sample file to storage.

        Args:
            file_name: The intended name of the file in storage (used for extension).
            file_id: The ID of the drum sample record this file corresponds to.

        Returns:
            A dictionary containing the signed upload URL and the storage path ('id' key holds file_id).

        Raises:
            StorageException: If URL creation fails in the repository layer.
            ServiceException: For other operational failures.
        """
        logger.info(
            f"Creating upload URL for drum sample file: {file_name} (ID: {file_id})"
        )
        try:
            # The repository handles the specific bucket/path logic now
            upload_info = await self.drum_sample_repository.create_upload_url(
                file_name=file_name, file_id=file_id
            )
            logger.info(f"Successfully created upload URL for drum sample {file_id}")
            return upload_info
        except StorageException as e:
            logger.error(
                f"Storage error creating upload URL for drum sample {file_id}: {str(e)}"
            )
            raise  # Re-raise specific storage exceptions
        except Exception as e:
            logger.error(
                f"Error creating upload URL for drum sample {file_id}: {str(e)}"
            )
            logger.error(traceback.format_exc())
            raise ServiceException(
                f"Failed to create upload URL for drum sample: {str(e)}"
            )
