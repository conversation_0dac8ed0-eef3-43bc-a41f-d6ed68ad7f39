import asyncio
import json
import re
import traceback
from pydantic import BaseModel
from typing import List, Optional, Any, Dict, Type, Union
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from dataclasses import dataclass


# Import schemas from music_gen_service
from app2.sse.sse_queue_manager import SSEQueueManager
from app2.llm.music_gen_service.llm_schemas import (
    DetermineMusicalParameters,
    FullMusicalParameters,
    IntervalMelodyOutput,
    LLMDeterminedMusicalParameters, 
    SelectInstruments, 
    SelectInstrumentsAndDescribeMelody,
    InstrumentSelectionItem,
    MelodyData, 
    Note, 
    Bar,
    SelectDrumSounds,
    CreateDrumBeat,
    DrumBeatPatternItem,
    ChordProgressionOutput,
    SongRequest
)
# Utilities from music_gen_service
from app2.llm.music_gen_service.music_researcher import MusicResearcher
from app2.core.logging import get_service_logger
from app2.models.track_models.drum_track import DrumTrackRead
from app2.models.track_models.sampler_track import SamplerTrackRead
from app2.api.dependencies import (
    get_drum_sample_service,
    get_drum_sample_public_repository,
    get_file_repository,
    get_file_service,
)
from app2.models.track_models.midi_track import MidiTrackRead
from app2.types.assistant_actions import AssistantAction

from app2.llm.music_gen_service.midi import (
    convert_interval_melody_to_absolute_melody,
    correct_notes_in_key,
    transform_chord_progression_to_instrument_format,
    transform_drum_beats_to_midi_format,
    transform_melody_data_to_instrument_format
)

from app2.llm.music_gen_service.music_gen_tools import (
    DETERMINE_MUSICAL_PARAMETERS_TOOL as DMP_TOOL_DEF,
    SELECT_INSTRUMENTS_TOOL as SI_TOOL_DEF,
    CREATE_MELODY_TOOL as CM_TOOL_DEF,
    CREATE_DRUM_BEAT_TOOL as CDB_TOOL_DEF,
    SELECT_DRUM_SOUNDS_TOOL as SDS_TOOL_DEF
)

from app2.llm.music_gen_service.prompt_utils import (
    get_ai_composer_agent_initial_system_prompt,
    get_melody_create_prompt
)

# Import the new validation function
from app2.llm.music_gen_service.music_utils import get_scale_pitch_classes, validate_melody_in_key, get_complete_scale_pitch_classes
# Import chord progression analysis function
from app2.llm.music_gen_service.chord_progression_analysis import analyze_chord_progression

from app2.llm.chat_wrapper import ChatSession
from app2.llm.available_models import ModelInfo
from app2.llm.streaming import TextDeltaEvent

from app2.core.config import settings
from app2.sse.request_manager import request_manager

logger = get_service_logger("music_agent")

# --- Music Generation Context ---
@dataclass
class MusicGenerationContext:
    """Stores essential context across music generation stages"""
    # Core request info
    user_prompt: str
    duration_bars: int
    
    # Research results
    general_research: Optional[str] = None
    chord_research: Optional[str] = None
    drum_research: Optional[str] = None
    
    # Generated parameters
    musical_params: Optional[FullMusicalParameters] = None
    selected_instruments: Optional[Union[SelectInstruments, SelectInstrumentsAndDescribeMelody]] = None
    selected_drums: Optional[SelectDrumSounds] = None
    
    # Model configuration
    model_info: Optional[ModelInfo] = None
    queue: Optional[SSEQueueManager] = None
    
    # Database and user context
    session: Optional[AsyncSession] = None
    user_id: Optional[str] = None
    generation_type: str = "midi"
    request_context: Optional[Dict[str, Any]] = None


# --- Parallel Execution Support (Simplified) ---
# All buffering/ordering logic removed - events are emitted immediately


# --- Music Generation Agent State Keys ---
class MusicAgentStateKeys:
    SONG_REQUEST = "song_request"
    MUSICAL_PARAMETERS = "musical_parameters" # Stores DetermineMusicalParameters model + title
    AVAILABLE_SOUNDFONTS = "available_soundfonts"
    AVAILABLE_DRUM_SAMPLES = "available_drum_samples"
    SOUNDFONT_MAP = "soundfont_map" # name to soundfont object
    DRUM_SAMPLE_MAP = "drum_sample_map" # name to drum sample object
    SELECTED_INSTRUMENTS_RAW = "selected_instruments_raw" # Stores SelectInstruments model
    PROCESSED_INSTRUMENTS = "processed_instruments" # Role -> {soundfont, notes[]}
    GENERATED_MELODY_DATA = "generated_melody_data" # Stores MelodyData
    SELECTED_DRUM_SOUNDS_RAW = "selected_drum_sounds_raw" # Stores SelectDrumSounds model
    PROCESSED_DRUM_SOUNDS = "processed_drum_sounds" # List of drum sample objects
    GENERATED_DRUM_BEAT_DATA = "generated_drum_beat_data" # Stores CreateDrumBeat model


# --- Music Generation Agent (No longer a Pydantic AI Agent) ---

# Remove Pydantic AI TestModel and default settings
# default_model = TestModel() 
# default_model_settings: Dict[str, Any] = {}

class MusicGenerationAgent:
    _music_researcher: MusicResearcher

    def __init__(self):
        #self._init_mock_data()
        self._music_researcher = MusicResearcher()
        
        # Token tracking by stage only (no more total accumulation)
        self.stage_tokens: Dict[str, Dict[str, int]] = {}
        
        # Context for sharing info across stages
        self.context: Optional[MusicGenerationContext] = None
        
        # Parallel execution - simplified with immediate emission

    def _track_stage_tokens(self, stage_name: str, tokens: Optional[Dict[str, int]] = None, append: bool = False):
        """Track token usage for a specific stage"""
        if tokens:
            # Use provided tokens
            usage = tokens
        else:
            # Legacy behavior - kept for compatibility but shouldn't be used
            logger.warning("_track_stage_tokens called without explicit tokens")
            usage = {"request_tokens": 0, "response_tokens": 0, "total_tokens": 0}
        
        if append and stage_name in self.stage_tokens:
            # Add to existing stage tokens
            self.stage_tokens[stage_name]["request_tokens"] += usage.get('request_tokens', 0)
            self.stage_tokens[stage_name]["response_tokens"] += usage.get('response_tokens', 0)
            self.stage_tokens[stage_name]["total_tokens"] += usage.get('total_tokens', 0)
        else:
            # Create new entry or overwrite
            self.stage_tokens[stage_name] = {
                "request_tokens": usage.get('request_tokens', 0),
                "response_tokens": usage.get('response_tokens', 0),
                "total_tokens": usage.get('total_tokens', 0)
            }
        logger.info(f"Stage '{stage_name}' tokens - Request: {usage.get('request_tokens', 0)}, Response: {usage.get('response_tokens', 0)}")
    
    async def _emit_step_events(self, step_id: int, step_name: str, step_func, *args, **kwargs):
        """Wrapper that emits step events for step lifecycle"""
        try:
            await self.context.queue.step_event(step_id, step_name, "stage", f"Starting {step_name}...")
            result = await step_func(*args, **kwargs)
            await self.context.queue.step_event(step_id, step_name, "status", f"{step_name} completed")
            return result
        except Exception as e:
            logger.error(f"Error in step {step_id} ({step_name}): {e}")
            try:
                await self.context.queue.step_event(step_id, step_name, "status", f"Error in {step_name}: {str(e)}")
            except Exception as emit_error:
                logger.error(f"Failed to emit error event: {emit_error}")
            raise
        
    async def _init_real_data(self, session: AsyncSession):
        _drum_file_repository = get_drum_sample_public_repository(session)
        _file_repository = get_file_repository(session)
        drum_sample_service = get_drum_sample_service(_drum_file_repository)
        file_service = get_file_service(_file_repository)
        self._available_soundfonts = await file_service.get_public_instrument_files()
        self._available_drum_samples = await drum_sample_service.get_all_samples()
        self._soundfont_map = {sf.display_name: sf for sf in self._available_soundfonts}
        self._drum_sample_map = {ds.display_name: ds for ds in self._available_drum_samples}
        self._drum_sample_id_map = {str(ds.id): ds for ds in self._available_drum_samples}

    def _create_fresh_chat_session(self, system_prompt: str) -> ChatSession:
        """Creates a new ChatSession with current model configuration."""
        if not self.context or not self.context.model_info:
            raise ValueError("Context not initialized with model info")
        
        return ChatSession(
            provider_name=self.context.model_info.provider_name,
            model_name=self.context.model_info.model_name,
            queue=self.context.queue,
            system_prompt=system_prompt,
            api_key=self.context.model_info.api_key,
            base_url=self.context.model_info.base_url,
            **getattr(self.context.model_info, 'provider_kwargs', {})
        )

    def _get_parameter_determination_prompt(self) -> str:
        """System prompt for determining musical parameters."""
        research_summary = ""
        if self.context and self.context.general_research:
            research_summary = f"\n\nResearch insights:\n{self.context.general_research[:1000]}"
        if self.context and self.context.chord_research:
            research_summary += f"\n\nChord progression research:\n{self.context.chord_research[:500]}"
        
        return f"""You are an AI music composer determining musical parameters.
Your task: Analyze the user's request and determine key musical parameters.

Context:{research_summary}

Focus on: key, mode, tempo, chord progression, and instrument suggestions.
When asked to explain, provide clear reasoning for your choices.
When asked for JSON, provide only the requested JSON structure."""

    def _get_instrument_selection_prompt(self) -> str:
        """System prompt for instrument selection."""
        if not self.context or not self.context.musical_params:
            return "You are an AI music composer selecting instruments."
        
        return f"""You are an AI music composer selecting instruments.

Current composition:
- Title: {self.context.musical_params.song_title}
- Key: {self.context.musical_params.key} {self.context.musical_params.mode}
- Tempo: {self.context.musical_params.tempo} BPM
- Chord Progression: {self.context.musical_params.chord_progression}
- Style: {self.context.general_research[:500] if self.context.general_research else 'No specific style research'}

Your task: Select appropriate instruments from available options.
Consider how instruments complement each other and fit the musical style."""

    def _get_drum_selection_prompt(self) -> str:
        """System prompt for drum sound selection."""
        if not self.context or not self.context.musical_params:
            return "You are an AI music composer selecting drum sounds."
            
        drum_context = ""
        if self.context.drum_research:
            drum_context = f"\n\nDrum research:\n{self.context.drum_research[:500]}"
        
        return f"""You are an AI music composer selecting drum sounds.

Current composition:
- Tempo: {self.context.musical_params.tempo} BPM
- Style: {self.context.musical_params.song_title}{drum_context}

Your task: Select appropriate drum sounds for the rhythm section.
Choose sounds that fit the tempo and style of the composition."""

    def _convert_key_to_enum(self, key: str, mode: str) -> 'KeySignature':
        """Convert LLM key+mode format to KeySignature enum value."""
        from app2.types.assistant_actions import KeySignature
        
        # Normalize inputs
        key = key.strip()
        mode = mode.strip().lower()
        
        # Handle sharp/flat notation
        key_mapping = {
            'C#': 'C_SHARP', 'Db': 'D_FLAT', 'D#': 'D_SHARP', 'Eb': 'E_FLAT',
            'F#': 'F_SHARP', 'Gb': 'G_FLAT', 'G#': 'G_SHARP', 'Ab': 'A_FLAT',
            'A#': 'A_SHARP', 'Bb': 'B_FLAT'
        }
        
        # Convert key notation
        enum_key = key_mapping.get(key, key.upper())
        
        # Construct enum name
        if mode in ['major', 'maj']:
            enum_name = f"{enum_key}_MAJOR"
        elif mode in ['minor', 'min', 'm']:
            enum_name = f"{enum_key}_MINOR" 
        else:
            logger.warning(f"Unknown mode '{mode}', defaulting to major")
            enum_name = f"{enum_key}_MAJOR"
        
        # Return enum value or default
        try:
            return getattr(KeySignature, enum_name)
        except AttributeError:
            logger.warning(f"Key signature '{enum_name}' not found, defaulting to C_MAJOR")
            return KeySignature.C_MAJOR

    # --- Helper for Focused LLM Calls (No longer uses RunContext) ---
    async def _focused_llm_call(self, prompt: str, output_type: Type[BaseModel], 
                               chat_session: Optional[ChatSession] = None) -> BaseModel:
        """Helper to make an isolated LLM call for structured output."""
        
        # Create fresh session if not provided
        if not chat_session:
            system_prompt = "You are an AI assistant that provides structured JSON responses."
            chat_session = self._create_fresh_chat_session(system_prompt)
            created_session = True
        else:
            created_session = False
        
        logger.info(f"DEBUG: _focused_llm_call for schema: {output_type.__name__}, fresh_session={created_session}")

        llm_call_model_settings: Dict[str, Any] = {}
        max_retries = 1 # Max number of retries (so 1 retry means 2 attempts total)
        if output_type is IntervalMelodyOutput: # More retries for complex melody generation
            max_retries = 2

        llm_output_obj: Optional[BaseModel] = None
        last_error: Optional[Exception] = None
        current_prompt = prompt

        for attempt in range(max_retries + 1):
            logger.info(f"Attempt {attempt + 1} for {output_type.__name__}...")
            raw_llm_output_data_str: Optional[str] = None
            last_error = None # Reset last_error for this attempt

            try:
                response_data = await chat_session.send_message_async(
                    user_prompt_content=current_prompt,
                    stream=False,
                    model_settings=llm_call_model_settings,
                    expect_json=True
                )

                if isinstance(response_data, str):
                    raw_llm_output_data_str = response_data
                    parsed_json_data = json.loads(raw_llm_output_data_str)
                    
                    if output_type is CreateDrumBeat: # Specific correction logic
                        if isinstance(parsed_json_data, dict) and 'drum_beats' in parsed_json_data and isinstance(parsed_json_data['drum_beats'], list):
                            for item_dict in parsed_json_data['drum_beats']:
                                if isinstance(item_dict, dict) and 'pattern' in item_dict and isinstance(item_dict['pattern'], list):
                                    pat = item_dict['pattern']
                                    expected_len = 32
                                    if len(pat) > expected_len:
                                        logger.info(f"ChatSession parse: Truncating drum pattern for {item_dict.get('drum_sound_id', 'N/A')} from {len(pat)} to {expected_len}.")
                                        item_dict['pattern'] = pat[:expected_len]
                                    elif len(pat) < expected_len:
                                        logger.info(f"ChatSession parse: Padding drum pattern for {item_dict.get('drum_sound_id', 'N/A')} from {len(pat)} to {expected_len}.")
                                        item_dict['pattern'].extend([False] * (expected_len - len(pat)))
                    
                    llm_output_obj = output_type(**parsed_json_data)
                    logger.info(f"Successfully parsed and validated output for {output_type.__name__} on attempt {attempt + 1}.")
                    break # Successful parse and validation, exit retry loop
                
                elif isinstance(response_data, output_type):
                    llm_output_obj = response_data
                    logger.info(f"Warning: ChatSession unexpectedly returned a parsed Pydantic object of type {type(response_data)}.")
                    break # Exit loop as we got a directly usable object
                else:
                    last_error = TypeError(f"ChatSession returned unexpected data type: {type(response_data)}. Content: {str(response_data)[:200]}...")
                    logger.info(f"Error on attempt {attempt + 1}: {last_error}")

            except json.JSONDecodeError as jde:
                last_error = jde
                logger.info(f"ERROR on attempt {attempt + 1} (JSONDecodeError) for {output_type.__name__}: {jde}. Raw: '{raw_llm_output_data_str if raw_llm_output_data_str else 'N/A'}...'")
            except Exception as e_pydantic: # Catches Pydantic validation errors or others
                last_error = e_pydantic
                parsed_json_for_error_msg = raw_llm_output_data_str if raw_llm_output_data_str else 'N/A (no raw string)'
                if 'parsed_json_data' in locals() and isinstance(parsed_json_data, dict):
                    parsed_json_for_error_msg = str(parsed_json_data)
                logger.info(f"ERROR on attempt {attempt + 1} (Pydantic/Validation Error) for {output_type.__name__}: {e_pydantic}. Data: {parsed_json_for_error_msg}...")
            
            if llm_output_obj: # Should have broken if successful
                break

            # If not the last attempt and an error occurred, prepare for retry
            if attempt < max_retries and last_error:
                logger.info(f"Preparing for retry attempt {attempt + 2} for {output_type.__name__}...")
                # Use str(last_error) directly. Limit previous raw output to a reasonable length.
                prev_output_for_retry = (raw_llm_output_data_str + '...' if raw_llm_output_data_str and len(raw_llm_output_data_str) > 500 else raw_llm_output_data_str) or 'N/A'

                retry_instruction = (
                    f"The previous attempt to generate JSON for {output_type.__name__} failed. "
                    f"Please carefully review the original request and the following error, then provide a corrected JSON response. "
                    f"Ensure your entire response is a single, valid JSON object matching the schema, with all keys and string values in double quotes.\n"
                    f"Previous Error: {str(last_error)}\n"
                    f"Previous Raw Output (possibly truncated):\n{prev_output_for_retry}\n\n"
                    f"Original request was (please regenerate response based on this original request, incorporating corrections):\n{prompt}"
                )
                current_prompt = retry_instruction # Use this elaborated prompt for the next attempt
            elif attempt >= max_retries and last_error and not llm_output_obj:
                logger.info(f"All {max_retries + 1} attempts failed for {output_type.__name__}. Last error: {last_error}")
        
        if not llm_output_obj: 
            # If llm_output_obj is None here, it means all retries failed, or cancellation happened 
            # and was caught by chat_session.send_message_async raising CancelledError, 
            # or our proactive checks raised CancelledError.
            # If it wasn't a CancelledError that got us here, it means all retries failed for other reasons.
            if last_error and not isinstance(last_error, asyncio.CancelledError):
                logger.error(f"All {max_retries + 1} attempts failed for {output_type.__name__}. Last error: {last_error}")
                raise ValueError(f"Failed to obtain valid output for {output_type.__name__} after {max_retries + 1} attempts. Last error: {last_error}") from last_error
            elif not last_error: # Loop finished, no object, no error - implies cancellation might have broken loop before send_message_async
                logger.warning(f"LLM call for {output_type.__name__} did not produce an object, possibly due to prior cancellation signal.")
                raise asyncio.CancelledError(f"LLM call for {output_type.__name__} aborted, no output produced.")
            # If last_error *was* a CancelledError, it should have been re-raised by send_message_async or our checks.
            # This path implies it was caught and we broke loop, or send_message_async returned None/unexpectedly.

        # Token accumulation happens via _track_stage_tokens in the calling methods
        
        return llm_output_obj # Return the validated object or the successfully constructed fallback

    # --- Music Generation Steps (formerly tools, now regular methods) ---

    async def determine_musical_parameters(self, user_prompt: str, duration_bars: int) -> FullMusicalParameters:
        """Determines core musical parameters via LLM call, streaming explanation first."""
        logger.info(f"Step 'determine_musical_parameters' called with user_prompt: '{user_prompt}', duration_bars: {duration_bars}")

        # Create fresh session with parameter-specific prompt
        system_prompt = self._get_parameter_determination_prompt()
        chat_session = self._create_fresh_chat_session(system_prompt)

        # 1. Stream Explanation
        explanation_prompt = f"""Based on the user request: '{user_prompt}' for a song of {duration_bars} bars, please explain your reasoning for the following musical parameters:
1. Key and Mode: What key/mode would fit the description and why?
2. Tempo (BPM): What tempo would capture the right feel and why?
3. Chord Progression: Suggest a suitable chord progression and explain why it works.
4. Melody Instrument Suggestion: What type of instrument would be good for the melody?
5. Chords Instrument Suggestion: What type of instrument would be good for the chords?
6. Song Title Suggestion: Suggest a title.

Provide only your reasoning and suggestions conversationally. Do NOT output JSON in this step."""
        
        logger.info("Streaming explanation for musical parameters...")
        explanation_text = ""
        try:
            async for text_chunk in await chat_session.send_message_async(
                user_prompt_content=explanation_prompt,
                stream=True
            ):
                if isinstance(text_chunk, str): 
                    logger.debug(f"STREAMING TEXT CHUNK: '{text_chunk}'") 
                    explanation_text += text_chunk
                    await self.context.queue.add_chunk(text_chunk)

            logger.info("Finished streaming explanation.")
            
            # Emit the captured explanation as a step event
            if explanation_text.strip():
                await self.context.queue.step_event(
                    step_id=1,
                    step_name="Determine Musical Parameters",
                    event_type="explanation",
                    content=explanation_text.strip(),
                    title="Musical Parameters Explanation"
                )
                logger.info("Emitted explanation event for musical parameters")
                
        except asyncio.CancelledError:
            logger.info("determine_musical_parameters: Streaming explanation task explicitly cancelled.")
            raise
        except Exception as e:
            logger.error(f"Error streaming explanation for musical parameters: {e}")
            await self.context.queue.error("Failed to stream parameter explanation.") 
            raise

        await self.context.queue.add_chunk("\n\n")
        await self.context.queue.add_chunk("---")
        await self.context.queue.add_chunk("\n\n")
        # 2. Get JSON Data (Focused Call)
        logger.info("Requesting JSON for musical parameters...")
        schema_example_for_focused_call = json.dumps(LLMDeterminedMusicalParameters.model_json_schema())
        focused_prompt_for_llm = (
            f"Based on the user request: '{user_prompt}' for a song of {duration_bars} bars, and considering the reasoning previously discussed, determine the core musical parameters. "
            f"The response MUST be a valid JSON object precisely matching the 'LLMDeterminedMusicalParameters' schema. "
            f"Schema to follow: {schema_example_for_focused_call}. "
            f"Ensure you populate: 'chord_progression', 'key', 'mode' (major/minor), 'tempo', 'melody_instrument_suggestion', 'chords_instrument_suggestion', and 'song_title' (can be null). "
            f"Output only the JSON object."
        )

        llm_simple_params = await self._focused_llm_call(
            focused_prompt_for_llm, 
            LLMDeterminedMusicalParameters,
            chat_session=chat_session  # Pass existing session for context continuity
        )

        # 3. Process JSON Data
        if not isinstance(llm_simple_params, LLMDeterminedMusicalParameters):
            logger.info(f"ERROR: _focused_llm_call for determine_musical_parameters did not return LLMDeterminedMusicalParameters. Got {type(llm_simple_params)}. Falling back.")
            return FullMusicalParameters(
                chord_progression="C-G-Am-F", key="C", mode="major", tempo=120,
                melody_instrument_suggestion="Piano", chords_instrument_suggestion="Strings",
                song_title="Fallback Song Title",
                original_user_prompt=user_prompt, 
                duration_bars=duration_bars
            )

        try:
            full_params = FullMusicalParameters(
                **llm_simple_params.model_dump(),
                original_user_prompt=user_prompt, 
                duration_bars=duration_bars
            )
        except Exception as e:
            logger.info(f"Error assembling FullMusicalParameters: {e}. Using fallback.")
            full_params = FullMusicalParameters(
                chord_progression="C-G-Am-F", key="C", mode="major", tempo=120,
                melody_instrument_suggestion="Piano", chords_instrument_suggestion="Strings",
                song_title="Fallback Assembled Title",
                original_user_prompt=user_prompt, 
                duration_bars=duration_bars
            )

        # Track stage tokens (full session cost)
        self._track_stage_tokens("determine_musical_parameters", chat_session.get_token_usage())

        # Send actions to set project parameters immediately
        from app2.types.assistant_actions import AssistantAction
        
        try:
            # Set BPM
            await self.context.queue.action(AssistantAction.change_bpm(float(full_params.tempo)))
            logger.info(f"Sent change_bpm action: {full_params.tempo}")
            
            # Set key signature
            key_signature = self._convert_key_to_enum(full_params.key, full_params.mode)
            await self.context.queue.action(AssistantAction.change_key(key_signature))
            logger.info(f"Sent change_key action: {full_params.key} {full_params.mode} -> {key_signature}")
            
            # Set project name if song title exists
            if full_params.song_title:
                await self.context.queue.action(AssistantAction.change_project_name(full_params.song_title))
                logger.info(f"Sent change_project_name action: {full_params.song_title}")
                
        except Exception as e:
            logger.error(f"Error sending parameter setting actions: {e}")
            # Don't raise - continue with generation even if parameter setting fails

        logger.info(f"Step 'determine_musical_parameters' returning: {full_params.model_dump_json(indent=2)}")
        return full_params

    async def select_instruments(self, determined_params: FullMusicalParameters) -> Union[SelectInstruments, SelectInstrumentsAndDescribeMelody]:
        """Streams explanation, then selects specific instruments via LLM call."""
        logger.info(f"Step 'select_instruments' called for song: {determined_params.song_title if determined_params.song_title else 'Untitled'}")
        
        # Update context with musical params
        if self.context:
            self.context.musical_params = determined_params
        
        # Create fresh session
        system_prompt = self._get_instrument_selection_prompt()
        chat_session = self._create_fresh_chat_session(system_prompt)
        
        soundfont_names_list = [sf.display_name for sf in self._available_soundfonts]

        # 1. Stream Explanation
        explanation_prompt_instruments = f"""Now I need to select specific instruments (soundfonts) for the composition.
Musical Context: Title: '{determined_params.song_title}', Key: {determined_params.key} {determined_params.mode}, Tempo: {determined_params.tempo} BPM.
User prompt: '{determined_params.original_user_prompt}'. Duration: {determined_params.duration_bars} bars.
My initial thoughts were: Melody: '{determined_params.melody_instrument_suggestion}', Chords: '{determined_params.chords_instrument_suggestion}'.

Available Soundfonts are: {json.dumps(soundfont_names_list)}

Please explain your choices for the melody and chords instruments from the available soundfonts. Consider how they fit the style and complement each other. 
Do NOT output JSON in this step, just your conversational reasoning."""

        logger.info("Streaming explanation for instrument selection...")
        logger.info(f"length of available soundfonts: {len(self._available_soundfonts)}")
        
        # Capture the full explanation for step event emission
        explanation_text = ""
        try:
            async for text_chunk in await chat_session.send_message_async(
                user_prompt_content=explanation_prompt_instruments,
                stream=True
            ):
                if isinstance(text_chunk, str):
                    logger.debug(f"INSTRUMENT EXPLANATION STREAMING TEXT CHUNK: '{text_chunk}'") 
                    explanation_text += text_chunk
            
            # Emit the complete explanation as a step event
            await self.context.queue.step_event(
                2, "Instrument Selection", "explanation", 
                content=explanation_text, title="Instrument Selection"
            )
            logger.info("Finished streaming instrument selection explanation.")
        except Exception as e:
            logger.error(f"Error streaming instrument selection explanation: {e}")
            await self.context.queue.step_event(
                2, "Instrument Selection", "status", f"Error: {str(e)}"
            )
            raise

        await self.context.queue.add_chunk("\n\n")
        await self.context.queue.add_chunk("---")
        await self.context.queue.add_chunk("\n\n")
        # 2. Get JSON Data (Focused Call) - Use different schema based on generation type
        logger.info("Requesting JSON for instrument selection...")
        
        # Determine which schema to use based on generation type
        is_audio_generation = self.context and self.context.generation_type == "audio"
        schema_class = SelectInstrumentsAndDescribeMelody if is_audio_generation else SelectInstruments
        
        if is_audio_generation:
            focused_prompt = f"""
Musical Context: Title: '{determined_params.song_title}', Key: {determined_params.key} {determined_params.mode}, Tempo: {determined_params.tempo} BPM.
User prompt: '{determined_params.original_user_prompt}'. Duration: {determined_params.duration_bars} bars.
Suggested Melody Instrument Type: {determined_params.melody_instrument_suggestion}
Suggested Chords Instrument Type: {determined_params.chords_instrument_suggestion}
Available Soundfonts (choose from this list only): {json.dumps(soundfont_names_list)}

Based on our prior discussion and reasoning, select specific soundfonts for 'chords' role and any supporting instruments like 'bass' if appropriate. 
For the melody, since we're using audio generation, DO NOT select a soundfont instrument. Instead, create a detailed description of what you want the melody to sound like.

The melody_description should be very detailed and include:
- The specific instrument or sound you want (e.g., "acoustic guitar", "violin", "piano")
- The playing style (e.g., "fingerpicked", "strummed", "legato", "staccato")
- The mood and character (e.g., "warm and intimate", "bright and energetic", "melancholic")
- Any specific techniques or qualities (e.g., "with slight reverb", "clean tone", "with vibrato")

Provide your response *only* in the 'SelectInstrumentsAndDescribeMelody' JSON structure.
Example of desired JSON structure: {json.dumps(SelectInstrumentsAndDescribeMelody.model_json_schema(), indent=2)}
Output only the JSON object."""
        else:
            focused_prompt = f"""
Musical Context: Title: '{determined_params.song_title}', Key: {determined_params.key} {determined_params.mode}, Tempo: {determined_params.tempo} BPM.
User prompt: '{determined_params.original_user_prompt}'. Duration: {determined_params.duration_bars} bars.
Suggested Melody Instrument Type: {determined_params.melody_instrument_suggestion}
Suggested Chords Instrument Type: {determined_params.chords_instrument_suggestion}
Available Soundfonts (choose from this list only): {json.dumps(soundfont_names_list)}

Based on our prior discussion and reasoning, select specific soundfonts for 'melody' and 'chords' roles. You can also suggest for 'bass' if appropriate.
Provide your response *only* in the 'SelectInstruments' JSON structure (an object with an 'instrument_selections' list of items, where each item has 'instrument_name', 'role', and 'explanation' fields).
Ensure instrument_name in each selection exactly matches a name from the Available Soundfonts list.
Example of desired JSON structure: {json.dumps(SelectInstruments.model_json_schema(), indent=2)}
Output only the JSON object."""
        
        instrument_selections_raw = await self._focused_llm_call(
            focused_prompt, 
            schema_class,
            chat_session=chat_session  # Pass existing session for context continuity
        )
        
        # 3. Process JSON Data - Handle both schema types
        melody_description = None
        instrument_selections = SelectInstruments(instrument_selections=[]) 
        
        if hasattr(instrument_selections_raw, 'instrument_selections') and instrument_selections_raw.instrument_selections is not None:
            instrument_selections = SelectInstruments(instrument_selections=instrument_selections_raw.instrument_selections)
            
            # Extract melody description if using SelectInstrumentsAndDescribeMelody
            if is_audio_generation and hasattr(instrument_selections_raw, 'melody_description'):
                melody_description = instrument_selections_raw.melody_description
                logger.info(f"Extracted melody description for audio generation: {melody_description}")
        
        logger.info(f"LLM selected instruments JSON: {instrument_selections.model_dump_json()}")
        if melody_description:
            logger.info(f"Melody description: {melody_description}")
        
        validated_selections = []
        if instrument_selections.instrument_selections: 
            for item in instrument_selections.instrument_selections:
                if item.instrument_name in self._soundfont_map:
                    validated_selections.append(item)
                else:
                    logger.info(f"Warning: LLM selected unknown instrument '{item.instrument_name}'. Ignoring.")
        else:
            logger.info("Warning: No instrument selections returned or in unexpected format from focused LLM call.")
        
        # Fallback logic - different for audio vs MIDI generation
        selected_roles = {sel.role.lower() for sel in validated_selections}
        
        # For audio generation, we don't need a melody instrument fallback since we have the description
        if not is_audio_generation and 'melody' not in selected_roles and self._available_soundfonts:
            logger.warning("No melody instrument selected by LLM, attempting fallback.")
            # Simple fallback: pick the first available soundfont not used for chords
            first_available_melody = next((sf for sf in self._available_soundfonts if sf.display_name not in [s.instrument_name for s in validated_selections if s.role.lower() == 'chords']), self._available_soundfonts[0])
            validated_selections.append(InstrumentSelectionItem(instrument_name=first_available_melody.display_name, role="melody", explanation="Fallback for missing melody role."))
            logger.info(f"Fallback selected melody: {first_available_melody.display_name}")

        if 'chords' not in selected_roles and self._available_soundfonts:
            logger.warning("No chords instrument selected by LLM, attempting fallback.")
            # Simple fallback: pick the first available soundfont not used for melody
            first_available_chords = next((sf for sf in self._available_soundfonts if sf.display_name not in [s.instrument_name for s in validated_selections if s.role.lower() == 'melody']), self._available_soundfonts[0])
            # Avoid using the same instrument as melody if possible
            if len(self._available_soundfonts) > 1 and first_available_chords.display_name == next((s.instrument_name for s in validated_selections if s.role.lower() == 'melody'), None):
                first_available_chords = next((sf for sf in self._available_soundfonts if sf.display_name != first_available_chords.display_name), self._available_soundfonts[1 if len(self._available_soundfonts) > 1 else 0])

            validated_selections.append(InstrumentSelectionItem(instrument_name=first_available_chords.display_name, role="chords", explanation="Fallback for missing chords role."))
            logger.info(f"Fallback selected chords: {first_available_chords.display_name}")
        
        # Track stage tokens (already using full session cost)
        self._track_stage_tokens("select_instruments", chat_session.get_token_usage())
        
        # Create the appropriate return object based on generation type
        if is_audio_generation:
            result = SelectInstrumentsAndDescribeMelody(
                instrument_selections=validated_selections,
                melody_description=melody_description
            )
            # Update context with the full result
            if self.context:
                self.context.selected_instruments = result
                # Store melody description separately for easy access
                self.context.melody_description = melody_description
        else:
            result = SelectInstruments(instrument_selections=validated_selections)
            # Update context with selected instruments
            if self.context:
                self.context.selected_instruments = result
            
        return result

    async def _generate_chords_and_send_sse(self, params: FullMusicalParameters, selected_instruments: SelectInstruments):
        """Finds chord instrument, generates notes, and sends add_midi_track SSE action."""
        logger.info("Starting chord generation...")
        chord_instrument_selection = next((
            item for item in selected_instruments.instrument_selections 
            if item.role.lower() == "chords"
        ), None)

        if chord_instrument_selection:
            chord_instrument_name = chord_instrument_selection.instrument_name
            if chord_instrument_name in self._soundfont_map:
                chord_instrument_details = self._soundfont_map[chord_instrument_name]
                logger.info(f"Found chords instrument: {chord_instrument_name}")

                try:
                    if isinstance(params.chord_progression, str):
                        processed_chord_progression = re.sub(
                            r"[,\s]+", "-", params.chord_progression
                        ).strip("-")
                    else:
                        logger.info(f"Warning: Invalid chord progression format: {params.chord_progression}, skipping chord generation.")
                        processed_chord_progression = None

                    if processed_chord_progression:
                        logger.info(f"Generating notes for chord progression: '{processed_chord_progression}' in {params.key} {params.mode} using {chord_instrument_name}")
                        
                        chord_notes_data = transform_chord_progression_to_instrument_format(
                            chord_progression=processed_chord_progression,
                            instrument=chord_instrument_details,
                            key=params.key,
                        )

                        if not chord_notes_data or "notes" not in chord_notes_data or not chord_notes_data["notes"]:
                            logger.info(
                                f"Chord transformation returned empty or invalid result for progression '{processed_chord_progression}'"
                            )
                        else:
                            logger.info(f"Generated {len(chord_notes_data.get('notes', []))} chord notes.")
                            track_id = uuid.uuid4()
                            chord_track = MidiTrackRead(
                                id=track_id,
                                name=chord_instrument_details.display_name,
                                instrument_id=chord_instrument_details.id,
                                midi_notes_json=chord_notes_data.get("notes"),
                                instrument_file=chord_instrument_details,
                            )
                            
                            logger.info(f"Sending add_midi_track action for chords: {chord_track.name}")
                            await self.context.queue.action(AssistantAction.add_midi_track(track=chord_track))

                except Exception as e:
                    logger.info(f"Error during chord generation/SSE: {str(e)}")
                    # Optionally send an error via queue?
                    # await queue.error("Failed to generate chords.")
            else:
                logger.info(f"Warning: Selected chords instrument '{chord_instrument_name}' not found in soundfont map.")
        else:
            logger.info("Warning: No instrument with role 'chords' selected. Skipping chord generation.")

    # COMMENTED OUT: Melody explanation step removed per user request
    # async def _stream_melody_explanation(self, params: FullMusicalParameters, selected_instruments: SelectInstruments):
    #     """Streams melody explanation separately to ensure proper ordering"""
    #     logger.info("Starting melody explanation...")
    #     
    #     melody_instrument_selection = next((
    #         item for item in selected_instruments.instrument_selections
    #         if item.role.lower() == "melody"
    #     ), None)
    #     
    #     if not melody_instrument_selection:
    #         logger.info("Warning: No instrument with role 'melody' selected. Skipping melody explanation.")
    #         return
    #     
    #     melody_instrument_name = melody_instrument_selection.instrument_name
    #     if melody_instrument_name not in self._soundfont_map:
    #         logger.info(f"Warning: Selected melody instrument '{melody_instrument_name}' not found in soundfont map. Skipping melody explanation.")
    #         return
    #     
    #     # Stream Explanation for Melody
    #     melody_explanation_prompt = f"""You are an AI music composer creating a melody.

    # Current composition:
    # - Key: {params.key} {params.mode}
    # - Tempo: {params.tempo} BPM
    # - Chord Progression: {params.chord_progression}
    # - Melody Instrument: {melody_instrument_name}

    # Your task: Describe the style, mood, and characteristics of the melody you are about to compose."""
    #     
    #     explanation_session = self._create_fresh_chat_session(melody_explanation_prompt)
    #     
    #     explanation_prompt_melody = f"""Now I'm going to create a melody.
    # Based on the key of {params.key} {params.mode}, tempo {params.tempo} BPM, the chord progression '{params.chord_progression}', and using the '{melody_instrument_name}' for the melody, describe the style, mood, and characteristics of the melody you are about to compose.

    # Keep it concise and conversational. Do NOT generate the actual musical notes or JSON in this step."""

    #     logger.info("Streaming explanation for melody...")
    #     try:
    #         async for text_chunk in await explanation_session.send_message_async(
    #             user_prompt_content=explanation_prompt_melody,
    #             stream=True
    #         ):
    #             if isinstance(text_chunk, str):
    #                 logger.debug(f"MELODY EXPLANATION STREAMING TEXT CHUNK: '{text_chunk}'") 
    #                 await self.context.queue.add_chunk(text_chunk)
    #         logger.info("Finished streaming melody explanation.")
    #         # Track tokens for the explanation phase
    #         self._track_stage_tokens("generate_melody_explanation", explanation_session.get_token_usage())
    #     except Exception as e:
    #         logger.error(f"Error streaming melody explanation: {e}")
    #         await self.context.queue.error("Failed to stream melody explanation.")
    #         return

    #     await self.context.queue.add_chunk("\n\n")
    #     await self.context.queue.add_chunk("---")
    #     await self.context.queue.add_chunk("\n\n")
    
    # COMMENTED OUT: Melody explanation step removed per user request  
    # async def _stream_melody_explanation_standalone(self, params: FullMusicalParameters):
    #     """Streams melody explanation as a standalone step without dependencies"""
    #     logger.info("Starting standalone melody explanation...")
    #     
    #     # Create a general melody explanation based on musical parameters
    #     melody_explanation_prompt = f"""You are an AI music composer creating a melody.

    # Current composition:
    # - Key: {params.key} {params.mode}
    # - Tempo: {params.tempo} BPM
    # - Chord Progression: {params.chord_progression}
    # - User Request: {params.original_user_prompt}

    # Your task: Describe the style, mood, and characteristics of the melody you are about to compose."""
    #     
    #     explanation_session = self._create_fresh_chat_session(melody_explanation_prompt)
    #     
    #     explanation_prompt_melody = f"""Now I'm going to create a melody.
    # Based on the key of {params.key} {params.mode}, tempo {params.tempo} BPM, the chord progression '{params.chord_progression}', and the user's request for '{params.original_user_prompt}', describe the style, mood, and characteristics of the melody you are about to compose.

    # Keep it concise and conversational. Do NOT generate the actual musical notes or JSON in this step."""

    #     logger.info("Streaming standalone melody explanation...")
    #     
    #     # Capture the full explanation for step event emission
    #     explanation_text = ""
    #     try:
    #         async for text_chunk in await explanation_session.send_message_async(
    #             user_prompt_content=explanation_prompt_melody,
    #             stream=True
    #         ):
    #             if isinstance(text_chunk, str):
    #                 logger.debug(f"STANDALONE MELODY EXPLANATION STREAMING TEXT CHUNK: '{text_chunk}'") 
    #                 explanation_text += text_chunk
    #         
    #         # Emit the complete explanation as a step event
    #         await self.context.queue.step_event(
    #             6, "Melody Explanation", "explanation", 
    #             content=explanation_text, title="Melody Explanation"
    #         )
    #         logger.info("Finished streaming standalone melody explanation.")
    #         # Track tokens for the explanation phase
    #         self._track_stage_tokens("generate_melody_explanation_standalone", explanation_session.get_token_usage())
    #     except Exception as e:
    #         logger.error(f"Error streaming standalone melody explanation: {e}")
    #         await self.context.queue.step_event(
    #             6, "Melody Explanation", "status", f"Error: {str(e)}"
    #         )
    #         return
    
    async def _generate_melody_notes_and_send_sse(self, params: FullMusicalParameters, selected_instruments: SelectInstruments):
        """Generates melody notes and sends SSE action (no explanation)"""
        logger.info("Starting melody note generation...")

        melody_instrument_selection = next((
            item for item in selected_instruments.instrument_selections
            if item.role.lower() == "melody"
        ), None)

        if not melody_instrument_selection:
            logger.info("Warning: No instrument with role 'melody' selected. Skipping melody generation.")
            return

        melody_instrument_name = melody_instrument_selection.instrument_name
        if melody_instrument_name not in self._soundfont_map:
            logger.info(f"Warning: Selected melody instrument '{melody_instrument_name}' not found in soundfont map. Skipping melody generation.")
            return
        
        melody_instrument_details = self._soundfont_map[melody_instrument_name]
        logger.info(f"Found melody instrument: {melody_instrument_name}")

        # Generate Melody Notes (no explanation, just processing)
        logger.info(f"Generating melody notes for {melody_instrument_name}...")
        try:
            melody_data = await self.generate_melody_notes(
                current_params=params,
                selected_instruments=selected_instruments 
            )

            if not melody_data or not melody_data.bars:
                logger.error("Melody generation returned empty or invalid result from generate_melody_notes.")
                await self.context.queue.error("Melody generation failed to produce notes.")
                raise Exception("Melody generation failed to produce notes")
            
            logger.info(f"Generated {len(melody_data.bars)} melody bars and {sum(len(bar.notes) for bar in melody_data.bars)} notes.")
            track_id = uuid.uuid4()
            
            melody_data_json = transform_melody_data_to_instrument_format(melody_data)
            
            melody_track = MidiTrackRead(
                id=track_id,
                name=melody_instrument_details.display_name,
                instrument_id=melody_instrument_details.id,
                midi_notes_json=melody_data_json, 
                instrument_file=melody_instrument_details,
            )
            
            logger.info(f"Sending add_midi_track action for melody: {melody_track.name}")
            await self.context.queue.action(AssistantAction.add_midi_track(track=melody_track))

        except Exception as e:
            logger.error(f"Error during melody note generation or SSE: {str(e)}", exc_info=True)
            await self.context.queue.error(f"Failed to generate melody notes: {str(e)}")
    
    async def _generate_drum_beat_patterns_and_send_sse(self, params: FullMusicalParameters, selected_drums_data: SelectDrumSounds, drum_research_result: Optional[str] = None):
        """Streams explanation, then generates drum beat patterns via LLM call."""
        logger.info(f"Step 'generate_drum_beat_patterns' called for song: {params.song_title if params.song_title else 'Untitled'}")
        drum_track = await self.generate_drum_beat_patterns(
            determined_params=params,
            selected_drums_data=selected_drums_data,
            drum_research_result=drum_research_result
        )
        
        await self.context.queue.action(AssistantAction.add_drum_track(track=drum_track))


    async def generate_melody_notes(self, current_params: FullMusicalParameters, selected_instruments: SelectInstruments) -> MelodyData:
        """Generates melody notes. This tool now uses a detailed interval-based prompt and converts the result to absolute notes."""
        logger.info(f"Step 'generate_melody_notes' for song: {current_params.song_title if current_params and current_params.song_title else 'Untitled'}")
        
        key = current_params.key
        mode = current_params.mode
        tempo = current_params.tempo
        allowed_intervals_string = str(list(range(-7, 8))) 
        chord_progression_str = current_params.chord_progression # The string form, e.g., "C-G-Am-F"
        user_prompt_lower = current_params.original_user_prompt.lower()
        mood = "upbeat" if "upbeat" in user_prompt_lower or "happy" in user_prompt_lower or "cheerful" in user_prompt_lower else "neutral"
        if "sad" in user_prompt_lower or "lo-fi" in user_prompt_lower: mood = "sad"
        tempo_character = "fast" if tempo > 140 else "moderate" if tempo > 100 else "slow"
        rhythm_type = "pop" 
        musical_style = "pop" 
        if "lo-fi" in user_prompt_lower: musical_style = "lo-fi"
        if "cinematic" in user_prompt_lower: musical_style = "cinematic"
        melodic_character = "catchy"
        duration_bars = current_params.duration_bars
        beats_per_bar = 4 
        duration_beats = duration_bars * beats_per_bar

        # --- Add Chord Progression Analysis ---
        actual_chord_analysis_data: Optional[Dict[str, Any]] = None # To store the raw dict
        note_analysis_json_str = "{}" # Default to empty JSON string if analysis fails
        try:
            #chord_list = [chord.strip() for chord in re.split(r'[-,\s]+', chord_progression_str) if chord.strip()]
            
            # Corrected logic for key_for_analysis
            key_for_analysis = key
            if mode.lower() == "minor":
                if not key.upper().endswith("M"): # Handles cases like "C", "D" for minor
                    key_for_analysis = key + "m" # Becomes "Cm", "Dm"
            # If key is already "Cm", "Dm", it remains as is.
            # If key is "C" (major), it remains "C".
            
            chord_progression_list = re.split(r"[-,\s]+", chord_progression_str)
            chord_progression_list = [
                chord.strip().replace("b", "-")
                for chord in chord_progression_list
                if chord.strip()
            ]

            logger.info(f"Analyzing chord progression: {chord_progression_list} in key: {key_for_analysis}")

            analysis_result = analyze_chord_progression(chord_progression_list, key_for_analysis) # Assume this returns a dict or None
            
            if analysis_result:
                actual_chord_analysis_data = analysis_result
                note_analysis_json_str = json.dumps(analysis_result, indent=2)
                #logger.info(f"Chord analysis generated: {note_analysis_json_str}...")
            else:
                logger.warning("Warning: Chord analysis returned no data.")
        except Exception as e_analysis:
            logger.info(f"Warning: Failed to analyze chord progression '{chord_progression_str}' for key '{key}' {mode}: {e_analysis}. Proceeding without detailed note weights.")
        # --- End Chord Progression Analysis ---

        interval_prompt = get_melody_create_prompt(
            key=key, mode=mode, tempo=tempo, 
            allowed_intervals_string=allowed_intervals_string, 
            chord_progression=chord_progression_str, 
            mood=mood, tempo_character=tempo_character, rhythm_type=rhythm_type, 
            musical_style=musical_style, melodic_character=melodic_character, 
            duration_bars=duration_bars, duration_beats=duration_beats
        )

        # Append chord analysis information to the prompt
        if note_analysis_json_str != "{}":
            enhancement_instruction = (
                "\n\n--- Additional Harmonic Context ---\n"
                "Use the following detailed chord analysis and note weights to guide your melodic interval choices. "
                "Focus on notes that align well with the underlying harmony of each chord, considering chord tones, extensions, and passing tones that resolve appropriately. "
                "This analysis provides weights for notes against each chord in the progression:\n"
                f"{note_analysis_json_str}\n"
                "Let this harmonic context heavily influence the intervals you select to create a musically consonant and expressive melody."
            )
            interval_prompt += enhancement_instruction

        logger.info(f"DEBUG: Creating fresh ChatSession for melody generation...")
        
        # Create fresh session for melody generation
        melody_system_prompt = f"""You are an AI music composer creating melodic patterns.
Key: {key} {mode}
Tempo: {tempo} BPM
Chord Progression: {chord_progression_str}

Create interval-based melodies that follow the harmonic structure."""
        
        melody_chat_session = self._create_fresh_chat_session(melody_system_prompt)
        
        logger.info(f"DEBUG: Prompting for IntervalMelodyOutput with fresh context. Prompt length: {len(interval_prompt)} chars.")
        logger.info(f"DEBUG: Prompt start: {interval_prompt[:200]}...")
        
        # Direct LLM call with fresh context instead of using _focused_llm_call
        max_retries = 2
        interval_melody_output_raw = None
        
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"Melody generation attempt {attempt + 1}...")
                response_data = await melody_chat_session.send_message_async(
                    user_prompt_content=interval_prompt,
                    stream=False,
                    model_settings={},
                    expect_json=True
                )
                
                logger.info(f"Response data type: {type(response_data)}")
                logger.info(f"Response data: {response_data}")
                
                if isinstance(response_data, str):
                    # Attempt to parse the JSON string
                    try:
                        parsed_json_data = json.loads(response_data)
                        #logger.info(f"Parsed JSON data raw: {parsed_json_data}") # Log the raw parsed data

                        # Find the dictionary within the parsed data
                        melody_dict_data = None
                        if isinstance(parsed_json_data, list):
                            for item in parsed_json_data:
                                if isinstance(item, dict):
                                    melody_dict_data = item
                                    break
                        elif isinstance(parsed_json_data, dict):
                            melody_dict_data = parsed_json_data

                        if melody_dict_data:
                            logger.info(f"Extracted melody dictionary: {melody_dict_data}")
                            # Instantiate the model using the extracted dictionary
                            interval_melody_output_raw = IntervalMelodyOutput(**melody_dict_data)
                            logger.info(f"Successfully parsed melody on attempt {attempt + 1}.")
                            break # Exit loop on success
                        else:
                            logger.info(f"Could not find a dictionary in the parsed JSON data on attempt {attempt + 1}.")
                            # Let the loop continue to retry if possible

                    except json.JSONDecodeError as jde:
                        logger.info(f"JSON decode error on attempt {attempt + 1}: {jde}. Raw response: {response_data[:500]}...") # Log raw response on error
                    except Exception as e_instantiate: # Catch potential errors during instantiation as well
                         logger.info(f"Error instantiating IntervalMelodyOutput on attempt {attempt + 1}: {e_instantiate}. Data used: {melody_dict_data}")

                elif isinstance(response_data, IntervalMelodyOutput):
                    interval_melody_output_raw = response_data
                    logger.info(f"ChatSession directly returned IntervalMelodyOutput on attempt {attempt + 1}.")
                    break
                else:
                    logger.info(f"Unexpected response type on attempt {attempt + 1}: {type(response_data)}")
            except Exception as e:
                logger.info(f"Error on melody generation attempt {attempt + 1}: {e}")
            
            if attempt < max_retries:
                retry_prompt = f"Previous attempt failed. Please provide a valid IntervalMelodyOutput JSON.\nSchema: {IntervalMelodyOutput.model_json_schema()}\nOriginal request: {interval_prompt[:300]}..."
                interval_prompt = retry_prompt
                
        # Fall back if all attempts failed
        if not interval_melody_output_raw:
            logger.error("All melody generation attempts failed.")
            raise Exception("Failed to generate melody after multiple attempts")
        
        interval_melody_output = interval_melody_output_raw
        logger.info(f"LLM returned interval melody: {interval_melody_output.model_dump_json(indent=2)}...")
        
        # Track tokens for melody generation (full session cost)
        self._track_stage_tokens("generate_melody", melody_chat_session.get_token_usage())

        final_melody_data_from_intervals = convert_interval_melody_to_absolute_melody(
            interval_melody=interval_melody_output,
            key_name=key,
            mode_name=mode # mode_name might be useful for determining actual scale for the root note
        )
        logger.info(f"Converted to absolute melody: {final_melody_data_from_intervals.model_dump_json(indent=2)}...")

        # Correct notes if analysis is available
        final_melody_data: MelodyData
        if actual_chord_analysis_data: # This is the dict from analyze_chord_progression
            logger.info("Attempting to correct out-of-key notes using chord analysis...")
            # Note: _correct_notes_in_key is synchronous, so no await here
            corrected_melody_data = correct_notes_in_key(
                melody_data=final_melody_data_from_intervals,
                key_name=key,
                mode_name=mode,
                chord_analysis_data=actual_chord_analysis_data, 
                chord_progression_str=chord_progression_str,
                duration_bars=duration_bars
            )
            final_melody_data = corrected_melody_data
        else:
            logger.info("Chord analysis data not available or failed, skipping note correction.")
            final_melody_data = final_melody_data_from_intervals
        
        logger.info(f"Melody after potential correction: {final_melody_data.model_dump_json(indent=2)}...")

        # Validate the generated melody right after conversion/correction
        try:
            # Normalize key for music21 (remove trailing 'm' if present)
            normalized_key = key
            if mode.lower() == "minor" and key.endswith("m"):
                normalized_key = key[:-1]  # Remove trailing 'm' from 'Cm', 'Am', etc.
            
            logger.info(f"Validating melody with normalized key: {normalized_key} {mode}")
            # Use our new robust validation function
            validate_melody_in_key(melody_data=final_melody_data, key_name=normalized_key, mode_name=mode)
            logger.info("Melody successfully validated by generate_melody_notes.")
        except ValueError as e_validation:
            logger.info(f"Melody validation failed: {e_validation}")
            # Propagate the validation error
            raise e_validation

        return final_melody_data


    async def select_drum_sounds(self, determined_params: FullMusicalParameters, drum_research_result: Optional[str] = None) -> SelectDrumSounds:
        """Streams explanation, then selects drum sounds via LLM call."""
        logger.info(f"Step 'select_drum_sounds' called for song: {determined_params.song_title if determined_params.song_title else 'Untitled'}")
        
        # Create fresh session
        system_prompt = self._get_drum_selection_prompt()
        chat_session = self._create_fresh_chat_session(system_prompt)
        
        drum_sample_names = [ds.display_name for ds in self._available_drum_samples]

        # 1. Stream Explanation for Drum Sound Selection
        explanation_context = f"For a song in {determined_params.key} {determined_params.mode} at {determined_params.tempo} BPM, with style hints from title '{determined_params.song_title}' and user prompt '{determined_params.original_user_prompt}'."
        if drum_research_result:
            explanation_context += f"\nConsider this research on drum sounds: {drum_research_result}"
        explanation_context += f"\nAvailable Drum Samples are: {json.dumps(drum_sample_names)}"

        explanation_prompt_drums = f"""Now I need to select specific drum sounds (e.g., kick, snare, hi-hat, cymbals).
{explanation_context}

Please explain your choices for 4-5 drum sounds from the available list that would fit the style. Describe the role each sound will play.
Do NOT output JSON in this step, just your conversational reasoning."""

        logger.info("Streaming explanation for drum sound selection...")
        
        # Capture the full explanation for step event emission
        explanation_text = ""
        try:
            async for text_chunk in await chat_session.send_message_async(
                user_prompt_content=explanation_prompt_drums,
                stream=True
            ):
                if isinstance(text_chunk, str):
                    logger.debug(f"Drum explanation chunk: '{text_chunk}'") 
                    explanation_text += text_chunk
            
            # Emit the complete explanation as a step event
            await self.context.queue.step_event(
                3, "Drum Selection", "explanation", 
                content=explanation_text, title="Drum Selection"
            )
            logger.info("Finished streaming drum sound selection explanation.")
        except Exception as e:
            logger.error(f"Error streaming drum sound selection explanation: {e}")
            await self.context.queue.step_event(
                3, "Drum Selection", "status", f"Error: {str(e)}"
            )
            raise

        await self.context.queue.add_chunk("\n\n")
        await self.context.queue.add_chunk("---")
        await self.context.queue.add_chunk("\n\n")
        # 2. Get JSON Data (Focused Call)
        logger.info("Requesting JSON for drum sound selection...")
        focused_prompt_context = f"For a song in {determined_params.key} {determined_params.mode} at {determined_params.tempo} BPM, with style hints from the title '{determined_params.song_title}', user prompt '{determined_params.original_user_prompt}', and chord progression '{determined_params.chord_progression}'."
        if drum_research_result:
            focused_prompt_context += f"\nRelevant drum sound research to consider: {drum_research_result}"
        
        focused_prompt = f"""
{focused_prompt_context}
Available Drum Samples (choose from this list only): {json.dumps(drum_sample_names)}

Based on our prior discussion and reasoning, select 4-5 appropriate drum sounds (e.g., kick, snare, hi-hat).
Provide your response *only* in the 'SelectDrumSounds' JSON structure (an object with a 'drum_sounds' list of strings, where each string is an exact name from the Available Drum Samples list).
<CRITICAL_INSTRUCTION>The sounds you choose MUST be in the list of available drum samples.</CRITICAL_INSTRUCTION>
<CRITICAL_INSTRUCTION>Any sounds not appearing in the available drum samples list will be rejected.</CRITICAL_INSTRUCTION>
<CRITICAL_INSTRUCTION>Remember, you MUST choose from THESE SAMPLES ONLY: {json.dumps(drum_sample_names)}</CRITICAL_INSTRUCTION>
Example of desired JSON structure: {json.dumps(SelectDrumSounds.model_json_schema())}
Output only the JSON object."""
        
        drum_sound_selections_raw = await self._focused_llm_call(
            focused_prompt, 
            SelectDrumSounds,
            chat_session=chat_session  # Pass existing session for context continuity
        )
        
        # 3. Process JSON Data (existing logic)
        drum_sound_selections = SelectDrumSounds(drum_sounds=[]) 
        if hasattr(drum_sound_selections_raw, 'drum_sounds') and drum_sound_selections_raw.drum_sounds is not None:
            drum_sound_selections = SelectDrumSounds(drum_sounds=drum_sound_selections_raw.drum_sounds)
        logger.info(f"LLM selected drum sounds JSON: {drum_sound_selections.model_dump_json()}")
        validated_drums = []
        if drum_sound_selections.drum_sounds:
            for name in drum_sound_selections.drum_sounds:
                if name in self._drum_sample_map:
                    validated_drums.append(name)
                else:
                    logger.info(f"Warning: LLM selected unknown drum sound '{name}'. Ignoring.")
                    logger.info(f"Available drum samples: {list(self._drum_sample_map.keys())}") # Log available keys for easier debug
        else:
            logger.info("Warning: No drum sounds returned or in unexpected format from focused LLM call.")
        
        # Fallback if no drums selected
        if not validated_drums and self._available_drum_samples:
            #raise Exception("No drum sounds selected by LLM or validated, attempting fallback.")
            logger.warning("No drum sounds selected by LLM or validated, attempting fallback.")
            # Simple fallback: try to pick common types if names are suggestive, else pick first few
            common_types = {'kick': False, 'snare': False, 'hat': False, 'clap': False}
            for sample in self._available_drum_samples:
                sample_name_lower = sample.display_name.lower()
                added = False
                if 'kick' in sample_name_lower and not common_types['kick']:
                    validated_drums.append(sample.display_name)
                    common_types['kick'] = True
                    added = True
                elif 'snare' in sample_name_lower and not common_types['snare']:
                    validated_drums.append(sample.display_name)
                    common_types['snare'] = True
                    added = True
                elif ('hat' in sample_name_lower or 'hi-hat' in sample_name_lower) and not common_types['hat']:
                    validated_drums.append(sample.display_name)
                    common_types['hat'] = True
                    added = True
                elif 'clap' in sample_name_lower and not common_types['clap']:
                    validated_drums.append(sample.display_name)
                    common_types['clap'] = True
                    added = True
                if len(validated_drums) >= 4: break # Limit fallback to around 4 sounds
            
            if not validated_drums: # If name matching failed, just pick first few
                validated_drums = [ds.display_name for ds in self._available_drum_samples[:min(4, len(self._available_drum_samples))]]
            logger.info(f"Fallback selected drums: {validated_drums}")
        
        # Track stage tokens (full session cost)
        self._track_stage_tokens("select_drum_sounds", chat_session.get_token_usage())
        
        # Update context with selected drums
        if self.context:
            self.context.selected_drums = SelectDrumSounds(drum_sounds=validated_drums)

        return SelectDrumSounds(drum_sounds=validated_drums)

    async def generate_drum_beat_patterns(self, determined_params: FullMusicalParameters, selected_drums_data: SelectDrumSounds, drum_research_result: Optional[str] = None) -> CreateDrumBeat:
        """Streams explanation, then generates drum beat patterns via LLM call."""
        logger.info(f"Step 'generate_drum_beat_patterns' called for song: {determined_params.song_title if determined_params.song_title else 'Untitled'}")
        
        drum_info_for_llm = []
        if hasattr(selected_drums_data, 'drum_sounds') and selected_drums_data.drum_sounds:
            for name in selected_drums_data.drum_sounds:
                if name in self._drum_sample_map:
                    # Ensure ID is a string for JSON serialization in prompts, and for CreateDrumBeat schema if it expects string IDs
                    drum_info_for_llm.append({"id": str(self._drum_sample_map[name].id), "name": name})
        
        if not drum_info_for_llm:
            logger.info("No valid selected drum sounds to generate patterns for. Returning empty patterns.")
            return CreateDrumBeat(drum_beats=[])
        
        # 2. Get JSON Data (Focused Call)
        logger.info("Requesting JSON for drum beat patterns...")
        # drum_info_for_prompt used in the focused_prompt should contain the IDs as expected by the CreateDrumBeat schema
        focused_prompt = f"""
Create drum beat patterns for a {determined_params.duration_bars}-bar song in {determined_params.key} {determined_params.mode} at {determined_params.tempo} BPM.
User prompt for style: '{determined_params.original_user_prompt}'.
Use these selected drum sounds (provide patterns for these IDs and names): {json.dumps(drum_info_for_llm)}.

Here is some research on drum beats of this style:
{drum_research_result}

<CRITICAL_INSTRUCTION>Ensure the drum patterns are in the style of the research and prompt provided.</CRITICAL_INSTRUCTION>
For EACH of the drum sounds listed above, create a rhythmic pattern.
Each pattern MUST be a JSON array of exactly 32 boolean values (true/false), representing 16th notes over 2 bars (in 4/4 time).
'true' means the drum hits on that 16th note step, 'false' means silence.
Do NOT exceed 32 items in any pattern list.
Output *only* in the 'CreateDrumBeat' JSON structure (an object with a 'drum_beats' list, where each item in the list has 'drum_sound_id' and 'pattern' fields).
Example of desired JSON structure: {json.dumps(CreateDrumBeat.model_json_schema(), indent=2)}
Output only the JSON object."""
        
        # Create a fresh ChatSession for this specific LLM call
        drum_system_prompt = f"""You are an AI music composer creating drum patterns.
Tempo: {determined_params.tempo} BPM
Style: {determined_params.song_title}

Create rhythmic patterns that fit the composition style."""
        
        drum_chat_session = self._create_fresh_chat_session(drum_system_prompt)

        max_retries = 1 
        drum_beat_data_raw: Optional[CreateDrumBeat] = None
        current_llm_prompt_for_drums = focused_prompt
        last_error: Optional[Exception] = None

        for attempt in range(max_retries + 1):
            logger.info(f"Drum beat pattern generation attempt {attempt + 1} for CreateDrumBeat...")
            raw_llm_output_str: Optional[str] = None
            # Reset last_error for this attempt, critical if previous attempt had error but didn't enter except block
            last_error = None 
            
            try:
                response_data = await drum_chat_session.send_message_async(
                    user_prompt_content=current_llm_prompt_for_drums,
                    stream=False,
                    model_settings={}, 
                    expect_json=True
                )

                if isinstance(response_data, str):
                    raw_llm_output_str = response_data
                    parsed_json_data = json.loads(raw_llm_output_str)
                    
                    # Specific correction logic for CreateDrumBeat patterns (ensure 32 booleans)
                    if isinstance(parsed_json_data, dict) and 'drum_beats' in parsed_json_data and isinstance(parsed_json_data['drum_beats'], list):
                        for item_dict in parsed_json_data['drum_beats']:
                            if isinstance(item_dict, dict) and 'pattern' in item_dict and isinstance(item_dict['pattern'], list):
                                pat = item_dict['pattern']
                                expected_len = 32 
                                if len(pat) > expected_len:
                                    logger.info(f"Drum pattern parse: Truncating drum pattern for {item_dict.get('drum_sound_id', 'N/A')} from {len(pat)} to {expected_len}.")
                                    item_dict['pattern'] = pat[:expected_len]
                                elif len(pat) < expected_len:
                                    logger.info(f"Drum pattern parse: Padding drum pattern for {item_dict.get('drum_sound_id', 'N/A')} from {len(pat)} to {expected_len}.")
                                    item_dict['pattern'].extend([False] * (expected_len - len(pat)))
                    
                    drum_beat_data_raw = CreateDrumBeat(**parsed_json_data)
                    logger.info(f"Successfully parsed and validated CreateDrumBeat on attempt {attempt + 1}.")
                    break 
                elif isinstance(response_data, CreateDrumBeat):
                    drum_beat_data_raw = response_data
                    logger.info(f"ChatSession directly returned CreateDrumBeat object on attempt {attempt + 1}.")
                    break
                else:
                    last_error = TypeError(f"ChatSession returned unexpected data type for drum patterns: {type(response_data)}. Content: {str(response_data)[:200]}...")
                    logger.info(f"Error on attempt {attempt + 1}: {last_error}")

            except json.JSONDecodeError as jde:
                last_error = jde
                logger.info(f"ERROR on attempt {attempt + 1} (JSONDecodeError) for CreateDrumBeat: {jde}. Raw: '{raw_llm_output_str if raw_llm_output_str else 'N/A'}...'")
            except Exception as e_pydantic: 
                last_error = e_pydantic
                parsed_json_for_error_msg = raw_llm_output_str if raw_llm_output_str else 'N/A (no raw string)'
                if 'parsed_json_data' in locals() and isinstance(parsed_json_data, dict):
                     parsed_json_for_error_msg = str(parsed_json_data) # locals() check needed
                logger.info(f"ERROR on attempt {attempt + 1} (Pydantic/Validation Error) for CreateDrumBeat: {e_pydantic}. Data: {parsed_json_for_error_msg}...")
            
            if drum_beat_data_raw: 
                break

            if attempt < max_retries and last_error:
                logger.info(f"Preparing for retry attempt {attempt + 2} for CreateDrumBeat...")
                prev_output_for_retry = (raw_llm_output_str[:500] + '...' if raw_llm_output_str and len(raw_llm_output_str) > 500 else raw_llm_output_str) or 'N/A'
                retry_instruction = (
                    f"The previous attempt to generate JSON for CreateDrumBeat failed. "
                    f"Please carefully review the original request and the following error, then provide a corrected JSON response. "
                    f"Ensure your entire response is a single, valid JSON object matching the schema, with all keys and string values in double quotes.\\n"
                    f"Previous Error: {str(last_error)}\\n"
                    f"Previous Raw Output (possibly truncated):\\n{prev_output_for_retry}\\n\\n"
                    f"Original request was (please regenerate response based on this original request, incorporating corrections):\\n{focused_prompt}"
                )
                current_llm_prompt_for_drums = retry_instruction
            elif attempt >= max_retries and last_error and not drum_beat_data_raw: # Check last_error here
                 logger.info(f"All {max_retries + 1} attempts failed for CreateDrumBeat. Last error: {last_error}")
        
        if not drum_beat_data_raw:
            logger.error("All drum beat pattern generation attempts failed.")
            return CreateDrumBeat(drum_beats=[])  # Return empty drum beats, not a failure dict
            
        drum_beat_data = drum_beat_data_raw
        # END of new LLM call logic
        
        # Track tokens for drum generation (full session cost)
        self._track_stage_tokens("generate_drum_beat", drum_chat_session.get_token_usage())
        
        # 3. Process JSON Data (existing logic)
        if not isinstance(drum_beat_data, CreateDrumBeat):
            logger.info(f"Warning: _focused_llm_call did not return CreateDrumBeat. Got {type(drum_beat_data)}. Returning empty.")
            # Fallback: Attempt to create an empty CreateDrumBeat if the call failed badly
            try:
                return CreateDrumBeat(drum_beats=[])
            except Exception:
                 # If even this fails, it means CreateDrumBeat model itself has issues or expects non-optional fields
                 # Let error propagate if basic fallback fails
                 # A more robust solution might be to always ensure CreateDrumBeat can be initialized with no args.
                 raise # Or return a pre-constructed empty valid object

        logger.info(f"LLM generated drum patterns JSON: {drum_beat_data.model_dump_json() if drum_beat_data else 'None'}")
        # Further validation of patterns can be added here if needed (e.g., ensuring all patterns are 32 booleans)
        
        logger.info(f"Drum sound keys: {[beat_data.drum_sound_id for beat_data in drum_beat_data.drum_beats]}")
        logger.info(f"Drum sound map keys: {[key for key in self._drum_sample_id_map.keys()]}")
        
        drum_track_id = uuid.uuid4()
        drum_track = DrumTrackRead(
            id=drum_track_id,
            name="Drums",
        )
        for beat_data in drum_beat_data.drum_beats:
            drum_sound_id = beat_data.drum_sound_id
            pattern = beat_data.pattern * 2

            logger.info(f"Drum sound ID: {drum_sound_id}")
            logger.info(f"Pattern: {pattern}")

            notes = transform_drum_beats_to_midi_format(pattern)
            logger.info(f"Notes: {notes}")

            if (
                not drum_sound_id
                or not isinstance(pattern, list)
                or len(pattern) != 64
            ):
                logger.warning(
                    f"Invalid drum beat data received: {beat_data}, skipping."
                )
                continue


            if drum_sound_id in self._drum_sample_id_map:
                drum_sample = self._drum_sample_id_map[drum_sound_id]
                logger.info(
                    f"Adding drum track for {drum_sample.display_name} (ID: {drum_sound_id})"
                )
                sampler_track_id = uuid.uuid4()
                drum_track.sampler_tracks.append(
                    SamplerTrackRead(
                        id=sampler_track_id,
                        name=drum_sample.display_name,
                        audio_file_name=drum_sample.file_name,
                        base_midi_note=settings.audio.DEFAULT_SAMPLER_BASE_NOTE,
                        grain_size=settings.audio.DEFAULT_SAMPLER_GRAIN_SIZE,
                        overlap=settings.audio.DEFAULT_SAMPLER_OVERLAP,
                        audio_file_sample_rate=settings.audio.SAMPLE_RATE,
                        audio_storage_key=drum_sample.storage_key,
                        audio_file_format=drum_sample.file_format,
                        audio_file_size=drum_sample.file_size,
                        audio_file_duration=drum_sample.duration or 0,
                        drum_track_id=drum_track_id,
                        midi_notes_json=notes,
                    )
                )
                drum_track.sampler_track_ids.append(sampler_track_id)
            else:
                logger.warning(
                    f"Drum sound ID '{drum_sound_id}' from LLM response not found in selected drums."
                )

        logger.info(f"Successfully processed and added drum track: {drum_track}")

        return drum_track

    async def generate_user_style_prompt(self, params: FullMusicalParameters, selected_instruments: SelectInstruments, user_prompt: str) -> str:
        """
        Generate a natural user-style prompt for FAL-AI diffusion model using LLM.
        
        Args:
            params: Musical parameters including key, tempo, chord progression
            selected_instruments: Selected instruments for context
            user_prompt: Original user request
            
        Returns:
            A natural user-style prompt that would be enhanced through the normal flow
        """
        logger.info("Generating user-style prompt using LLM...")
        
        # Find the melody instrument for context
        melody_instrument = "piano"  # Default fallback
        melody_instrument_selection = next((
            item for item in selected_instruments.instrument_selections
            if item.role.lower() == "melody"
        ), None)
        
        if melody_instrument_selection:
            melody_instrument = melody_instrument_selection.instrument_name
        
        # Create system prompt
        system_prompt = f"""You are a musician who wants to generate audio using a text-to-audio model.

Your task is to create a natural, user-style prompt that someone would type to generate a solo instrument melody.

Current musical context:
- Key: {params.key} {params.mode}
- Tempo: {params.tempo} BPM
- Chord Progression: {params.chord_progression}
- Selected Melody Instrument: {melody_instrument}
- Song Title: {params.song_title or 'Untitled'}
- Original User Request: "{user_prompt}"

Create a simple, natural prompt that a user would type to generate a solo {melody_instrument} melody. Keep it conversational and natural - like what someone would actually type, not a technical specification.

Examples of good user-style prompts:
- "piano solo melody"
- "gentle acoustic guitar melody"
- "sad violin solo"
- "upbeat saxophone melody"
- "classical piano piece"

Focus on the instrument and basic mood/style. Don't include technical specifications - those will be added automatically."""
        
        # Create the prompt request
        prompt_request = f"""Based on the musical context and the user's original request "{user_prompt}", create a natural user-style prompt for generating a solo {melody_instrument} melody.

The prompt should be something a real user would naturally type - simple, conversational, and focused on the instrument and basic musical character.

Respond with just the prompt text - no quotes, no additional formatting, just the natural user prompt."""
        
        # Create fresh session for prompt generation
        prompt_session = self._create_fresh_chat_session(system_prompt)
        
        try:
            # Generate the user-style prompt
            response_data = await prompt_session.send_message_async(
                user_prompt_content=prompt_request,
                stream=False,
                model_settings={},
                expect_json=False
            )
            
            if isinstance(response_data, str):
                user_style_prompt = response_data.strip()
                logger.info(f"Generated user-style prompt: '{user_style_prompt}'")
                
                # Track tokens for prompt generation
                self._track_stage_tokens("generate_user_style_prompt", prompt_session.get_token_usage())
                
                return user_style_prompt
            else:
                logger.error(f"Unexpected response format from LLM: {type(response_data)}")
                raise ValueError("Failed to generate user-style prompt: unexpected response format")
                
        except Exception as e:
            logger.error(f"Error generating user-style prompt: {e}")
            # Fallback to a basic user-style prompt
            fallback_prompt = f"{melody_instrument} solo melody"
            logger.info(f"Using fallback user-style prompt: '{fallback_prompt}'")
            return fallback_prompt

    async def generate_diffusion_melody_and_send_sse(self, params: FullMusicalParameters, selected_instruments: Union[SelectInstruments, SelectInstrumentsAndDescribeMelody]):
        """
        Generate audio using FAL-AI diffusion model and send as audio track SSE action.
        
        This is an alternative to MIDI-based melody generation that uses AI audio generation
        to create solo instrument melodies that can be layered in the DAW.
        
        Args:
            params: Musical parameters including key, tempo, chord progression
            selected_instruments: Selected instruments (for audio generation, includes melody_description)
            queue: SSE queue for sending progress updates and final audio track
        """
        logger.info("Starting diffusion-based melody generation with FAL-AI...")
        
        # Check if we have a melody description from SelectInstrumentsAndDescribeMelody
        has_melody_description = isinstance(selected_instruments, SelectInstrumentsAndDescribeMelody) and selected_instruments.melody_description
        
        if has_melody_description:
            # Use the detailed melody description directly
            user_style_prompt = selected_instruments.melody_description
            logger.info(f"Using melody description directly: {user_style_prompt}")
            
            await self.context.queue.stage(
                name="using_melody_description",
                description=f"Using detailed melody description: {user_style_prompt[:100]}..."
            )
        else:
            # Fallback to LLM-generated user-style prompt
            await self.context.queue.stage(
                name="generating_user_prompt",
                description="Creating natural user-style prompt for audio generation..."
            )
            
            # Get the original user prompt from context
            original_user_prompt = self.context.user_prompt if self.context and self.context.user_prompt else "Create a melody"
            
            # Generate user-style prompt using LLM
            user_style_prompt = await self.generate_user_style_prompt(
                params, 
                selected_instruments, 
                original_user_prompt
            )
        
        # Now enhance it with the same flow as the existing generate-audio mode
        # This is the same logic from assistant_streaming.py process_generate_audio_request
        
        # Extract musical context with sensible defaults
        bpm = params.tempo
        key_signature = f"{params.key} {params.mode}" if params.key and params.mode else "C Major"
        time_signature = {'numerator': 4, 'denominator': 4}  # Default to 4/4
        
        # For Stable Audio, include key and BPM in the prompt for musical context
        enhanced_prompt = f"{user_style_prompt}, {key_signature}, {bpm} BPM"
        
        # Stable Audio doesn't use negative prompts
        negative_prompt = None
        
        await self.context.queue.stage(
            name="generating_diffusion_audio",
            description=f"Generating audio using AI diffusion model..."
        )
        
        logger.info(f"=== DIFFUSION GENERATION PARAMETERS ===")
        logger.info(f"User-style prompt: '{user_style_prompt}'")
        logger.info(f"Enhanced prompt: '{enhanced_prompt}'")
        logger.info(f"Negative prompt: '{negative_prompt if negative_prompt else 'None'}'")
        logger.info(f"Musical params - BPM: {bpm}, Key: {key_signature}")
        logger.info(f"=== END PARAMETERS ===")
        
        try:
            # Import audio generation service
            from app2.services.audio_generation_service import get_audio_generation_service
            from app2.services.file_service import FileService
            from app2.repositories.file_repository import FileRepository
            from app2.models.track_models.audio_track import AudioTrack
            from app2.repositories.audio_track_repository import AudioTrackRepository
            from app2.types.assistant_actions import AudioTrackData
            import uuid
            
            # Get session and user_id from context
            if not self.context or not self.context.session:
                raise ValueError("Database session not available in music agent context")
            
            if not self.context.user_id:
                raise ValueError("User ID not available in music agent context")
            
            session = self.context.session
            user_id = self.context.user_id
            
            # Create file service instance
            file_repository = FileRepository(session)
            file_service = FileService(file_repository)
            
            # Create audio generation service instance
            # Get audio generation model from context if available (for Compose mode)
            audio_generation_model = None
            if self.context.request_context:
                audio_generation_model = self.context.request_context.get('audio_generation_model')
            
            # Log the audio model selection
            if audio_generation_model:
                logger.info(f"🎵 Using audio generation model from context: {audio_generation_model}")
            else:
                logger.info("🎵 No audio generation model specified, using default")
            
            audio_service = get_audio_generation_service(model=audio_generation_model)
            
            # Calculate duration for 4 bars based on BPM
            # Formula: (bars * beats_per_bar * 60) / BPM
            bars = 4  # Fixed to 4 bars as specified
            beats_per_bar = 4  # 4/4 time signature
            duration_seconds = (bars * beats_per_bar * 60) / bpm
            # Round up to nearest second as requested
            import math
            duration_seconds = math.ceil(duration_seconds)
            
            logger.info(f"Calculated duration: {duration_seconds} seconds for {bars} bars at {bpm} BPM")
            
            # Generate and download audio
            try:
                await self.context.queue.stage(
                    name="generating_audio",
                    description=f"Generating {duration_seconds}s audio with {audio_generation_model or 'default model'}..."
                )
                
                # Check if this is Stability AI service (supports additional parameters)
                if audio_generation_model == 'stability/stable-audio-2':
                    logger.info(f"🎵 Stable Audio generation with: duration={duration_seconds}s, bpm={bpm}, bars={bars}, key={key_signature}")
                    # Stable Audio doesn't use negative prompts
                    audio_data, generation_result = await audio_service.generate_and_download_audio(
                        prompt=enhanced_prompt,
                        duration=duration_seconds,
                        bpm=bpm,
                        bars=bars,
                        key=key_signature
                    )
                else:
                    logger.info(f"🎵 {audio_generation_model or 'Default'} audio generation (basic parameters only)")
                    # Other services (VertexAI, FAL AI) support negative prompts
                    audio_data, generation_result = await audio_service.generate_and_download_audio(
                        prompt=enhanced_prompt,
                        negative_prompt="drums, percussion"  # Only for non-Stable Audio services
                    )
                
                if not audio_data or not generation_result:
                    raise ValueError("Audio generation returned empty results")
                    
            except Exception as e:
                logger.error(f"Audio generation failed: {e}")
                await self.context.queue.error(
                    message="Failed to generate audio",
                    error_data={
                        "error": "audio_generation_failed",
                        "model": audio_generation_model or "default",
                        "prompt": enhanced_prompt[:100],
                        "details": str(e)
                    }
                )
                raise
            
            await self.context.queue.stage(
                name="processing_diffusion_audio",
                description="Processing and storing generated audio..."
            )
            
            # Upload audio to storage
            storage_key = await file_service.upload_audio_file(
                audio_data, 
                generation_result.file_name,
                user_id
            )
            
            # Create placeholder waveform data
            waveform_data = [0.0] * 200  # Simple placeholder
            
            # Create audio track record
            audio_track_id = str(uuid.uuid4())
            
            # Extract instrument name from the melody selection for track naming
            melody_instrument_selection = next((
                item for item in selected_instruments.instrument_selections
                if item.role.lower() == "melody"
            ), None)
            
            track_name = "AI Generated Melody"
            if melody_instrument_selection:
                track_name = f"AI {melody_instrument_selection.instrument_name} Melody"
            
            audio_track_data = {
                "id": audio_track_id,
                "name": track_name,
                "audio_file_storage_key": storage_key,
                "audio_file_format": "wav",
                "audio_file_size": len(audio_data),
                "audio_file_duration": generation_result.duration,
                "audio_file_sample_rate": generation_result.sample_rate,
                "waveform_data": waveform_data,
                "user_id": user_id
            }
            
            # Create AudioTrack model directly
            audio_track = AudioTrack(**audio_track_data)
            
            # Save to database
            session.add(audio_track)
            await session.commit()
            await session.refresh(audio_track)
            
            # Convert to read model for the response
            from app2.models.track_models.audio_track import AudioTrackRead
            created_track = AudioTrackRead.model_validate(audio_track)
            
            # Create action to add the audio track to the frontend
            action = AssistantAction(
                action_type="add_audio_track",
                data=AudioTrackData(track_data=created_track.model_dump(mode='json'))
            )
            
            # Send the action
            await self.context.queue.action(action)
            
            logger.info(f"Successfully generated and sent diffusion melody: {track_name} in {key_signature}")
            
        except Exception as e:
            logger.error(f"Error in diffusion melody generation: {str(e)}")
            await self.context.queue.error(f"Failed to generate diffusion melody: {str(e)}")
            raise

    def _sanitize_key_and_mode(self, key: str, mode: str) -> tuple[str, str]:
        """If key is like 'A minor' or 'C major', split it. Always return (tonic, mode)."""
        if key and isinstance(key, str) and " " in key:
            parts = key.strip().split()
            if len(parts) == 2 and parts[1].lower() in ("major", "minor"):
                return parts[0], parts[1].lower()
        return key, mode.lower() if mode else "major"

    # --- Main Orchestration Method (Parallel Execution) ---
    async def run(self, request_id: str, request: SongRequest, model_info: ModelInfo, queue: SSEQueueManager, db_session: AsyncSession, user_id: str = None, generation_type: str = "midi", request_context: Dict[str, Any] = None):
        """
        Main execution flow for the music generation agent with parallel execution.
        Processes the request through various stages, using LLM calls for decisions
        and transformations, and sends progress/results via the SSE queue in correct order.

        Args:
            request_id: The unique ID for this assistant request.
            request: The user's song request details.
            model_info: Information about the LLM to be used.
            queue: The SSE queue manager for sending events to the client.
            db_session: The database session for any database operations.
        """
        logger.info(f"MusicGenerationAgent run started (PARALLEL) for request_id: {request_id}, prompt: '{request.user_prompt}', duration: {request.duration_bars} bars")
        
        await self._init_real_data(db_session) # Initialize soundfonts and drum samples

        logger.info(f"Available soundfonts length: {len(self._available_soundfonts)}")
        logger.info(f"Available drum samples length: {len(self._available_drum_samples)}")
        
        #await queue.add_chunk(f"Doing research for prompt: '{request.user_prompt}'...\n\n")
        
        # Initialize context (needed for _emit_step_events)
        self.context = MusicGenerationContext(
            user_prompt=request.user_prompt,
            duration_bars=request.duration_bars,
            general_research=None,
            chord_research=None,
            drum_research=None,
            model_info=model_info,
            queue=queue,
            session=db_session,
            user_id=user_id,
            generation_type=generation_type,
            request_context=request_context
        )
        
        # Step 0: Research Musical Context
        async def do_research():
            return await asyncio.gather(
                self._music_researcher.enhance_description(request.user_prompt),
                self._music_researcher.research_chord_progression(request.user_prompt),
                self._music_researcher.research_drum_sounds(request.user_prompt)
            )
        
        research_result, chord_research_result, drum_research_result = await self._emit_step_events(
            0, "Researching Musical Context", do_research
        )

        # Update context with research results
        self.context.general_research = research_result.get("prompt_addition") if research_result and isinstance(research_result, dict) else None
        self.context.chord_research = chord_research_result
        self.context.drum_research = drum_research_result

        # Build research-enhanced prompt
        research_addition = ""
        if research_result and isinstance(research_result, dict) and research_result.get("prompt_addition"):
            research_addition += research_result["prompt_addition"]
        if chord_research_result:
            research_addition += f"\n\nChord progression research:\n{chord_research_result}"
        user_prompt_with_research = request.user_prompt + research_addition

        # Step 1: Determine musical parameters (required for all other steps)
        params = await self._emit_step_events(
            1, "Determine Musical Parameters", 
            self.determine_musical_parameters,
            user_prompt=user_prompt_with_research,
            duration_bars=request.duration_bars
        )
        # Sanitize key and mode after LLM output
        sanitized_key, sanitized_mode = self._sanitize_key_and_mode(params.key, params.mode)
        if sanitized_key != params.key or sanitized_mode != params.mode:
            logger.info(f"Sanitized key/mode: '{params.key}'/'{params.mode}' -> '{sanitized_key}'/'{sanitized_mode}'")
            params.key = sanitized_key
            params.mode = sanitized_mode

        try:
            # Steps 2-3: Instrument and Drum Selection (parallel)
            instrument_task = self._emit_step_events(
                2, "Instrument Selection", self.select_instruments, params
            )
            drum_selection_task = self._emit_step_events(
                3, "Drum Selection", self.select_drum_sounds, params, drum_research_result
            )
            
            # Wait for instrument and drum selection
            selected_instruments, selected_drum_sounds = await asyncio.gather(
                instrument_task, drum_selection_task,
                return_exceptions=True
            )
            
            # Handle exceptions from selection phase
            if isinstance(selected_instruments, Exception):
                logger.error(f"Instrument selection failed: {selected_instruments}")
                raise selected_instruments
            if isinstance(selected_drum_sounds, Exception):
                logger.error(f"Drum selection failed: {selected_drum_sounds}")
                raise selected_drum_sounds
            
            # Step 4: Start drum beat generation immediately after drum selection
            drum_beat_task = self._emit_step_events(
                4, "Drum Beat Generation", self._generate_drum_beat_patterns_and_send_sse, params, selected_drum_sounds, drum_research_result
            )
            
            # Create background task to handle drum completion immediately
            drum_result = None
            drum_exception = None
            
            async def handle_drum_completion():
                nonlocal drum_result, drum_exception
                try:
                    drum_result = await drum_beat_task
                except Exception as e:
                    drum_exception = e
            
            # Start background completion handler
            drum_completion_task = asyncio.create_task(handle_drum_completion())
            
            # Step 5: Chord Generation
            chord_task = self._emit_step_events(
                5, "Chord Generation", self._generate_chords_and_send_sse, params, selected_instruments
            )
            
            # Wait for chord generation to complete
            chord_data = await chord_task
            
            # Handle exceptions from chord generation phase
            if isinstance(chord_data, Exception):
                logger.error(f"Chord generation failed: {chord_data}")
                raise chord_data
            
            # Step 6: Melody Generation (MIDI or Audio based on generation_type) - moved from step 7
            if generation_type == "audio":
                melody_task = self._emit_step_events(
                    6, "Melody Generation", self.generate_diffusion_melody_and_send_sse, params, selected_instruments
                )
            else:  # default to midi
                melody_task = self._emit_step_events(
                    6, "Melody Generation", self._generate_melody_notes_and_send_sse, params, selected_instruments
                )
            
            # Wait for melody generation and drum completion task
            melody_data = await melody_task
            await drum_completion_task
            
            # Retrieve drum results
            if drum_exception:
                drum_data = drum_exception
            else:
                drum_data = drum_result
            
            # Handle exceptions from final generation phase
            if isinstance(melody_data, Exception):
                logger.error(f"Melody generation failed: {melody_data}")
                raise melody_data
            if isinstance(drum_data, Exception):
                logger.error(f"Drum beat generation failed: {drum_data}")
                raise drum_data
            
            logger.info("All generation tasks completed with immediate emission")
            
        except asyncio.CancelledError:
            logger.info("Music generation cancelled by user")
            await self.context.queue.add_chunk("\n\n[Generation cancelled by user]\n")
            raise
        except Exception as e:
            logger.error(f"Error during parallel execution: {e}")
            await self.context.queue.error(f"Generation failed: {str(e)}")
            raise
        
        logger.info(f"MusicGenerationAgent run finished. Title: {params.song_title}")
        
        # Calculate total tokens from all tracked stages
        total_request = 0
        total_response = 0
        for stage_name, tokens in self.stage_tokens.items():
            total_request += tokens.get("request_tokens", 0)
            total_response += tokens.get("response_tokens", 0)
        
        logger.info(f"Total token usage - Request: {total_request}, Response: {total_response}")
        
        # Return success result with metadata about the generation
        return {
            "success": True,
            "song_title": params.song_title,
            "tempo": params.tempo,
            "key": f"{params.key} {params.mode}",
            "duration_bars": request.duration_bars,
            "tracks_generated": len(selected_instruments.instrument_selections) + 1,  # instruments + drums
            "request_tokens": total_request,
            "response_tokens": total_response,
            "total_tokens": total_request + total_response,
            "stage_tokens": self.stage_tokens,  # Include stage-by-stage token breakdown
            "sequential_execution": True,  # Flag to indicate sequential execution for proper ordering
            "generation_steps_completed": 5  # Number of generation steps completed
        }

# Singleton instance
music_agent = MusicGenerationAgent()