"""
Base models for SQL database entities
"""

from datetime import datetime, timezone
from typing import Optional, Type, List
from sqlmodel import SQLModel, Field
from sqlalchemy import TIMESTAMP, Column
from sqlalchemy.dialects.postgresql import <PERSON><PERSON>N<PERSON>
from pydantic import BaseModel, create_model, ConfigDict
import uuid

from app2.types.drum_sample_types import DrumSampleType
from app2.types.genre_types import GenreType
from app2.types.track_types import TrackType
from sqlalchemy import Enum as SAEnum


class TrackInstance(BaseModel):
    """Represents a track instance within a project"""
    
    model_config = ConfigDict(
        extra='forbid',  # Don't allow extra fields
        arbitrary_types_allowed=False  # Stricter type checking
    )
    
    id: str
    x_position: float
    y_position: float
    trim_start_ticks: int
    trim_end_ticks: int


def all_optional(base_model: Type[BaseModel], name: str) -> Type[BaseModel]:
    """
    Creates a new model with the same fields, but all optional.

    Usage: SomeOptionalModel = SomeModel.all_optional('SomeOptionalModel')
    """
    return create_model(
        name,
        __base__=base_model,
        **{
            name: (info.annotation, None)
            for name, info in base_model.model_fields.items()
            if name not in ["type", "id"]
        },
    )


class TimestampMixin(SQLModel):
    """Mixin for created_at and updated_at timestamps"""

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        index=True,
        sa_type=TIMESTAMP(timezone=True)
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        index=True,
        sa_type=TIMESTAMP(timezone=True)
    )

    # # Configure JSON serialization for datetime fields
    # model_config = ConfigDict(
    #     json_encoders={
    #         datetime: lambda dt: dt.isoformat()
    #         + "Z"  # Add 'Z' for UTC timezone indicator in ISO format
    #     },
    #     populate_by_name=True,  # Recommended for SQLModel/Pydantic V2
    #     arbitrary_types_allowed=True,  # Often needed with SQLModel
    # )


class DefaultUUIDMixin(SQLModel):
    """Mixin that adds UUID primary key (generates default value)"""

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)


class UUIDMixin(SQLModel):
    """Mixin that adds UUID primary key (needs to be provided)"""

    id: uuid.UUID = Field(primary_key=True, index=True)


class DefaultUUIDStandardBase(DefaultUUIDMixin, TimestampMixin):
    """Base model with default UUID primary key and default timestamp fields"""

    pass


class StandardBase(UUIDMixin, TimestampMixin):
    """Base model with UUID primary key with mandatory provided UUID and default timestamp fields"""

    pass


class FileBase(StandardBase):
    """Base model for files"""

    file_name: str
    display_name: str
    storage_key: str
    file_format: str
    file_size: int


class UserBase(StandardBase):
    """Base model for users"""

    email: str = Field(unique=True, index=True)
    username: Optional[str] = Field(default=None, unique=True, index=True)
    display_name: Optional[str] = Field(default=None)
    avatar_url: Optional[str] = Field(default=None)


class ProjectBase(DefaultUUIDStandardBase):
    """Base model for projects"""

    name: str
    bpm: float
    time_signature_numerator: int
    time_signature_denominator: int
    key_signature: str
    version: int = Field(default=0)  # For optimistic locking


class TrackBase(StandardBase):
    """Base model for tracks"""

    name: str


class ProjectTrackBase(TimestampMixin):
    """Base model for project tracks"""

    name: str
    volume: float
    pan: float
    mute: bool
    duration_ticks: int
    track_number: int
    track_type: TrackType = Field(sa_column=Column(SAEnum(TrackType), nullable=False))
    instance_metadata: Optional[List[TrackInstance]] = Field(default=None, sa_column=Column(JSONB))


class InstrumentFileBase(FileBase):
    """Base model for instruments"""

    category: str
    is_public: bool
    description: Optional[str] = None


class DrumSamplePublicBase(FileBase):
    """Base model for drum samples"""

    genre: GenreType
    category: DrumSampleType
    kit_name: str
    duration: Optional[float] = None
    description: Optional[str] = None
    waveform_data: Optional[List[float]] = Field(default=None, sa_column=Column(JSONB))
