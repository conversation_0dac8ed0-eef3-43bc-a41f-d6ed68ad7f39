"""
Audio Inpainting API endpoint using FAL-AI's ace-step/audio-inpaint model.

This endpoint provides a simple interface for modifying audio files using AI.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import traceback

from app2.api.dependencies import get_current_user
from app2.services.fal_ai_service import get_fal_ai_service
from app2.services.credits_service import credits_service
from app2.constants.credits import get_operation_cost
from app2.core.logging import get_api_logger

# Set up logger
logger = get_api_logger("audio_inpaint")

# Create router
router = APIRouter()


class AudioInpaintRequest(BaseModel):
    """Request model for audio inpainting"""
    audio_url: str = Field(..., description="URL of the source audio file to modify", min_length=1)
    tags: str = Field(..., description="Style tags for the generation (e.g., 'lofi, hiphop, drum and bass, trap, chill')", min_length=1, max_length=500)
    prompt: Optional[str] = Field(None, description="Optional text description of desired modifications", max_length=1000)
    start_time: Optional[float] = Field(None, description="Optional start time for the segment to modify (in seconds)", ge=0)
    end_time: Optional[float] = Field(None, description="Optional end time for the segment to modify (in seconds)", ge=0)
    lyrics: Optional[str] = Field(None, description="Optional lyrics to guide the inpainting", max_length=1000)


class AudioInpaintResponse(BaseModel):
    """Response model for audio inpainting"""
    audio_url: str
    file_name: str
    duration: float
    sample_rate: int
    format: str
    success: bool = True


@router.post(
    "/inpaint",
    response_model=AudioInpaintResponse,
    status_code=status.HTTP_200_OK,
)
async def inpaint_audio(
    request: AudioInpaintRequest,
    user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Modify audio using FAL-AI's ace-step/audio-inpaint model.
    
    This endpoint takes an audio URL and modification prompt, sends them to FAL-AI,
    and returns the URL of the modified audio file.
    """
    try:
        logger.info(f"Audio inpaint request from user {user.get('id')}: {request.prompt[:100]}...")
        
        # Get user ID
        user_id = user.get("id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User ID not found"
            )
        
        # Validate time range if provided
        if request.start_time is not None and request.end_time is not None:
            if request.start_time >= request.end_time:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="start_time must be less than end_time"
                )
        
        # Validate audio URL format (basic check)
        if not request.audio_url.startswith(('http://', 'https://')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="audio_url must be a valid HTTP/HTTPS URL"
            )
        
        # Check credits before processing
        user_credits_info = await credits_service.get_user_credits(user_id)
        if user_credits_info and not user_credits_info.get("is_unlimited", False):
            current_balance = user_credits_info.get("balance", 0)
            if isinstance(current_balance, str):  # Handle "unlimited" string
                current_balance = 999999
            
            if current_balance < 1:
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail={
                        "error": "insufficient_credits",
                        "required": 1,
                        "current_balance": current_balance,
                        "message": "You need at least 1 credit to edit audio. Please upgrade your plan."
                    }
                )
        
        # Create FAL-AI service instance
        fal_ai_service = get_fal_ai_service()
        
        # Perform audio inpainting
        result = await fal_ai_service.audio_inpaint(
            audio_url=request.audio_url,
            tags=request.tags,
            prompt=request.prompt,
            start_time=request.start_time,
            end_time=request.end_time,
            lyrics=request.lyrics
        )
        
        # Deduct credits after successful inpainting
        if user_credits_info and not user_credits_info.get("is_unlimited", False):
            await credits_service.use_credits(
                user_id=user_id,
                amount=get_operation_cost("audio_inpainting"),
                description="Audio inpainting with FAL-AI",
                metadata={
                    "model": "fal-ai/ace-step/audio-inpaint",
                    "tags": request.tags,
                    "user_prompt": request.prompt,
                    "start_time": request.start_time,
                    "end_time": request.end_time,
                    "lyrics": request.lyrics,
                    "source_audio_url": request.audio_url,
                    "generation_type": "audio_inpaint"
                },
                # AI-specific parameters
                model_provider="fal-ai",
                model_name="ace-step/audio-inpaint",
                generation_type="audio_inpaint",
                prompt_preview=request.prompt[:200] if request.prompt else None
            )
        
        # Create response
        response = AudioInpaintResponse(
            audio_url=result.audio_url,
            file_name=result.file_name,
            duration=result.duration,
            sample_rate=result.sample_rate,
            format=result.format,
            success=True
        )
        
        logger.info(f"Audio inpaint successful for user {user_id}: {result.file_name}")
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions (like credit check failures)
        raise
    except Exception as e:
        logger.error(f"Error in audio inpaint request: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to inpaint audio: {str(e)}"
        )