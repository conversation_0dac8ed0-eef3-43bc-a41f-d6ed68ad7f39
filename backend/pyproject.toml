[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "sqlmodel>=0.0.24",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.1",
    "music21>=9.5.0",
    "mido>=1.3.3",
    "openai>=1.75.0",
    "numpy>=1.26.4",
    "librosa>=0.11.0",
    "instructor[anthropic]>=1.7.9",
    "pydantic-ai[openai]>=0.0.31",
    "pydantic-ai-slim[openai]>=0.0.31",
    "json-repair>=0.44.1",
    "fal-client>=0.7.0",
    "asyncpg>=0.30.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_mode = "auto"
