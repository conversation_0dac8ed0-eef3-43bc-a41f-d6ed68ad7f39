#!/usr/bin/env python3
"""
Migration script to initialize billing records for existing users.

This script creates UserCredits and CreditTransaction records for users who don't have them.
All users will be initialized with the free tier (10 credits, no rollover).
"""

import asyncio
import logging
from datetime import datetime
from typing import List
import uuid

from dotenv import load_dotenv
from sqlmodel import select
from sqlalchemy import text

# Load environment variables
load_dotenv()

from app2.models.user import User
from app2.models.credits import UserCredits, CreditTransaction, CreditTransactionType
from app2.services.credits_service import CreditsService
from app2.constants.credits import FREE_TIER_INITIAL_CREDITS

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Free tier defaults
FREE_TIER_CREDITS = FREE_TIER_INITIAL_CREDITS
FREE_TIER = "free"


async def find_users_without_credits() -> List[User]:
    """Find all users who don't have UserCredits records."""
    from app2.infrastructure.database.sqlmodel_client import get_async_session
    
    async with get_async_session() as session:
        # Get all users
        stmt = select(User)
        result = await session.execute(stmt)
        all_users = result.scalars().all()
        
        # Check which users don't have credits
        users_without_credits = []
        for user in all_users:
            credits_stmt = select(UserCredits).where(UserCredits.user_id == user.id)
            credits_result = await session.execute(credits_stmt)
            existing_credits = credits_result.scalars().first()
            
            if not existing_credits:
                users_without_credits.append(user)
        
        logger.info(f"Found {len(users_without_credits)} users without UserCredits records")
        return users_without_credits


async def create_user_credits_batch(users: List[User]) -> List[UserCredits]:
    """Create UserCredits records for a batch of users."""
    user_credits_list = []
    
    for user in users:
        user_credits = UserCredits(
            user_id=user.id,
            balance=FREE_TIER_CREDITS,
            monthly_allocation=FREE_TIER_CREDITS,
            subscription_tier=FREE_TIER,
            has_rollover=False,
            last_allocation_date=datetime.utcnow(),
            total_allocated=FREE_TIER_CREDITS,
            total_used=0
        )
        user_credits_list.append(user_credits)
    
    return user_credits_list


async def create_credit_transactions_batch(user_credits_list: List[UserCredits]) -> List[CreditTransaction]:
    """Create initial CreditTransaction records for the UserCredits."""
    transactions = []
    
    for user_credits in user_credits_list:
        transaction = CreditTransaction(
            user_id=user_credits.user_id,
            user_credits_id=user_credits.id,
            transaction_type=CreditTransactionType.MONTHLY_ALLOCATION,
            amount=FREE_TIER_CREDITS,
            balance_after=FREE_TIER_CREDITS,
            description="Initial free plan allocation (migration)"
        )
        transactions.append(transaction)
    
    return transactions


async def migrate_users_batch(users: List[User]) -> int:
    """Migrate a batch of users using the CreditsService."""
    if not users:
        return 0
    
    credits_service = CreditsService()
    migrated_count = 0
    
    for user in users:
        try:
            # Use the existing service to initialize credits
            await credits_service.initialize_user_credits(user, FREE_TIER)
            migrated_count += 1
            logger.info(f"Successfully migrated user {user.id} ({user.email})")
            
        except Exception as e:
            logger.error(f"Failed to migrate user {user.id} ({user.email}): {e}")
            # Continue with other users instead of failing the whole batch
            
    return migrated_count


async def run_migration():
    """Run the complete user billing migration."""
    logger.info("Starting user billing migration...")
    
    try:
        # Find users without credits
        users_without_credits = await find_users_without_credits()
        
        if not users_without_credits:
            logger.info("No users need migration. All users already have billing records.")
            return
        
        logger.info(f"Migrating {len(users_without_credits)} users...")
        
        # Process in batches of 50 to avoid memory issues
        batch_size = 50
        total_migrated = 0
        
        for i in range(0, len(users_without_credits), batch_size):
            batch = users_without_credits[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(users_without_credits) + batch_size - 1) // batch_size
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} users)")
            
            migrated_count = await migrate_users_batch(batch)
            total_migrated += migrated_count
            
            logger.info(f"Batch {batch_num} completed. {migrated_count} users migrated.")
        
        logger.info(f"Migration completed successfully! {total_migrated} users migrated.")
        
        # Verify migration
        remaining_users = await find_users_without_credits()
        if remaining_users:
            logger.warning(f"Warning: {len(remaining_users)} users still don't have billing records")
        else:
            logger.info("Verification: All users now have billing records ✅")
    
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(run_migration())