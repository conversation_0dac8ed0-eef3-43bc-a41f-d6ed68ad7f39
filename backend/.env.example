# Supabase configuration
SUPABASE_URL=https://your-supabase-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# JWT configuration
JWT_SECRET_KEY=your-own-jwt-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database URL (if needed)
DATABASE_URL=postgres://postgres:password@localhost:5432/beatgen

# Redis configuration for distributed request management
REDIS_URL=redis://localhost:6379

# Distributed Request Management
# Set to "true" to enable Redis-based distributed requests across multiple servers
# Set to "false" to use in-memory storage (single server only)
USE_DISTRIBUTED_REQUESTS=false