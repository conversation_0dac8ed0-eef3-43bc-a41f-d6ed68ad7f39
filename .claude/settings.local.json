{"permissions": {"allow": ["Bash(cd:*)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(npm install:*)", "<PERSON><PERSON>(python:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"(track\\.type|type)\\s*[!=]==?\\s*['\\\"](?i)(midi|audio|sampler|drum)['\\\"]\" /Users/<USER>/Developer/beatgen/frontend/src/studio/core/state/history/actions/TrackActions.ts)", "Bash(npm run build:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(rg:*)", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"saveProjectWithSounds|save.*with.*sounds|save_project_with_sounds\" /Users/<USER>/Developer/beatgen/backend)", "Bash(gh api:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 10 -B 5 \"MuiThemeProvider|studioDarkTheme|studioLightTheme\" studio/Studio.tsx)", "Bash(rm:*)", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_press_key", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_navigate", "<PERSON><PERSON>(npx playwright test:*)", "Bash(pgrep:*)", "<PERSON><PERSON>(make:*)", "mcp__filesystem__read_multiple_files", "mcp__filesystem__list_directory", "mcp__filesystem__read_file", "mcp__filesystem__write_file", "mcp__filesystem__create_directory", "mcp__filesystem__edit_file", "mcp__playwright__browser_snapshot", "<PERSON><PERSON>(mv:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"<canvas\" --type tsx --type ts)", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"<canvas\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 10 -B 2 \"<canvas\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"createElement.*canvas|document\\.createElement.*canvas|\\.canvas|Canvas\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"<svg\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"position.*absolute.*overlay|overlay.*position.*absolute|z-index.*overlay|overlay.*z-index\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"z-.*\\[1[0-9]|z-.*\\[2[0-9]|z-.*\\[3[0-9]|z-.*\\[4[0-9]|z-.*\\[5[0-9]|zIndex.*[1-9][0-9]\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"position.*absolute\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/Users/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"position.*absolute|pointerEvents\" --glob \"*.tsx\" --glob \"*.ts\")", "mcp__playwright__browser_type", "mcp__playwright__browser_take_screenshot", "<PERSON><PERSON>(open:*)", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_drag", "<PERSON><PERSON>(sed:*)", "mcp__filesystem__search_files", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(npm test:*)", "<PERSON><PERSON>(curl:*)", "Bash(cp:*)", "mcp__playwright__browser_file_upload", "mcp__playwright__browser_close", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(diff:*)", "mcp__filesystem__directory_tree", "WebFetch(domain:github.com)", "mcp__filesystem__list_allowed_directories", "Bash(npx vitest run:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:docs.stripe.com)", "<PERSON><PERSON>(source:*)", "Bash(ruff check:*)", "WebFetch(domain:supabase.com)", "Bash(npx eslint:*)", "Bash(pip install:*)", "Bash(pnpm build:*)", "Bash(git log:*)", "WebFetch(domain:googleapis.github.io)", "WebFetch(domain:cloud.google.com)", "<PERSON><PERSON>(timeout:*)", "Bash(npx vite build:*)", "WebFetch(domain:ai.pydantic.dev)", "WebFetch(domain:redis.readthedocs.io)"], "deny": []}}