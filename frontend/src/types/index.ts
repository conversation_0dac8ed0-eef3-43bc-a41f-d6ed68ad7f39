/**
 * Central exports for all unified types
 */

// Export track types
//export * from './track';

// Export project types
export * from './project';

// Export adapters (for conversions between API and unified types)
//export * from './adapters';

/**
 * NOTE: This is the central place for all application types.
 * All components should import from here rather than individual files.
 * 
 * Example usage:
 * 
 * import { Track, Project, apiProjectToProject } from '../types';
 */