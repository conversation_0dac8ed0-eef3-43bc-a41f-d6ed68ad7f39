import { Note } from '../../../../../types/note'; // Corrected path
import { NoteAction, BaseAction } from './BaseAction';
import { GetFn } from '../../../../stores/types';
/**
 * Action for adding a note without callbacks
 */
export class AddNoteAction extends NoteAction {
    readonly type = 'NOTE_ADD';
    private note: Note;

    constructor(
        get: GetFn,
        trackId: string,
        noteId: string,
        note: Note
    ) {
        super(get, trackId, noteId);
        this.note = note;
    }

    async execute(): Promise<void> {
        const midiManager = this.store.getMidiManager();
        if (!midiManager) {
            console.error(`${this.type}: MidiManager not available`);
            return;
        }

        try {
            await midiManager.addNoteToTrack(
                this.trackId, 
                { ...this.note, id: parseInt(this.noteId) },
            );
            this.markTrackAsDirty(); // Mark track as dirty when note is added
            
            this.log('Execute', {
                trackId: this.trackId,
                noteId: this.noteId,
                row: this.note.row,
                column: this.note.column,
                length: this.note.length
            });
        } catch (error) {
            console.error(`Error adding note to track ${this.trackId}:`, error);
        }
    }

    async undo(): Promise<void> {
        const midiManager = this.store.getMidiManager();
        if (!midiManager) {
            console.error(`${this.type}: MidiManager not available`);
            return;
        }

        try {
            await midiManager.removeNoteFromTrack(this.trackId, parseInt(this.noteId));
            this.markTrackAsDirty(); // Mark track as dirty when note is removed (undo)
            
            this.log('Undo', {
                trackId: this.trackId,
                noteId: this.noteId
            });
        } catch (error) {
            console.error(`Error removing note ${this.noteId} from track ${this.trackId}:`, error);
        }
    }
}

/**
 * Action for removing a note without callbacks
 */
export class DeleteNoteAction extends NoteAction {
    readonly type = 'NOTE_DELETE';
    private note: Note;

    constructor(
        get: GetFn,
        trackId: string,
        noteId: string,
        note: Note
    ) {
        super(get, trackId, noteId);
        this.note = { ...note };
    }

    async execute(): Promise<void> {
        const midiManager = this.store.getMidiManager();
        if (!midiManager) {
            console.error(`${this.type}: MidiManager not available`);
            return;
        }

        try {
            await midiManager.removeNoteFromTrack(this.trackId, parseInt(this.noteId));
            this.markTrackAsDirty(); // Mark track as dirty when note is deleted
            
            this.log('Execute', {
                trackId: this.trackId,
                noteId: this.noteId
            });
        } catch (error) {
            console.error(`Error removing note ${this.noteId} from track ${this.trackId}:`, error);
        }
    }

    async undo(): Promise<void> {
        const midiManager = this.store.getMidiManager();
        if (!midiManager) {
            console.error(`${this.type}: MidiManager not available`);
            return;
        }

        try {
            await midiManager.addNoteToTrack(this.trackId, this.note);
            this.markTrackAsDirty(); // Mark track as dirty when note is restored (undo)
            
            this.log('Undo', {
                trackId: this.trackId,
                noteId: this.noteId,
                row: this.note.row,
                column: this.note.column,
                length: this.note.length
            });
        } catch (error) {
            console.error(`Error restoring note ${this.noteId} to track ${this.trackId}:`, error);
        }
    }
}

/**
 * Action for moving a note without callbacks
 */
export class MoveNoteAction extends NoteAction {
    readonly type = 'NOTE_MOVE';
    private oldPosition: { column: number, row: number };
    private newPosition: { column: number, row: number };
    private noteData: Note;

    constructor(
        get: GetFn,
        trackId: string,
        noteId: string,
        oldPosition: { column: number, row: number },
        newPosition: { column: number, row: number },
        noteData: Note
    ) {
        super(get, trackId, noteId);
        this.oldPosition = oldPosition;
        this.newPosition = newPosition;
        this.noteData = { ...noteData };
    }

    private async updateNotePosition(position: { column: number, row: number }): Promise<void> {
        const midiManager = this.store.getMidiManager();
        if (!midiManager) {
            console.error(`${this.type}: MidiManager not available`);
            return;
        }

        try {
            const updatedNote: Note = {
                ...this.noteData,
                id: parseInt(this.noteId),
                row: position.row,
                column: position.column,
                trackId: this.trackId
            };
            
            await midiManager.updateNote(this.trackId, updatedNote);
        } catch (error) {
            console.error(`Error updating position of note ${this.noteId} in track ${this.trackId}:`, error);
        }
    }

    async execute(): Promise<void> {
        await this.updateNotePosition(this.newPosition);
        this.markTrackAsDirty(); // Mark track as dirty when note is moved
        this.log('Execute', {
            trackId: this.trackId,
            noteId: this.noteId,
            from: this.oldPosition,
            to: this.newPosition
        });
    }

    async undo(): Promise<void> {
        await this.updateNotePosition(this.oldPosition);
        this.markTrackAsDirty(); // Mark track as dirty when note move is undone
        this.log('Undo', {
            trackId: this.trackId,
            noteId: this.noteId,
            from: this.newPosition,
            to: this.oldPosition
        });
    }
}

/**
 * Action for resizing a note without callbacks
 */
export class ResizeNoteAction extends NoteAction {
    readonly type = 'NOTE_RESIZE';
    private oldLength: number;
    private newLength: number;
    private noteData: Note;
    private oldColumn?: number;
    private newColumn?: number;

    constructor(
        get: GetFn,
        trackId: string,
        noteId: string,
        oldLength: number,
        newLength: number,
        noteData: Note,
        oldColumn?: number,
        newColumn?: number
    ) {
        super(get, trackId, noteId);
        this.oldLength = oldLength;
        this.newLength = newLength;
        this.noteData = { ...noteData };
        this.oldColumn = oldColumn;
        this.newColumn = newColumn;
    }

    private async updateNoteSize(length: number, column?: number): Promise<void> {
        const midiManager = this.store.getMidiManager();
        if (!midiManager) {
            console.error(`${this.type}: MidiManager not available`);
            return;
        }

        try {
            const updatedNote: Note = {
                ...this.noteData,
                id: parseInt(this.noteId),
                length,
                trackId: this.trackId,
                ...(column !== undefined && { column })
            };
            
            await midiManager.updateNote(this.trackId, updatedNote);
        } catch (error) {
            console.error(`Error resizing note ${this.noteId} in track ${this.trackId}:`, error);
        }
    }

    async execute(): Promise<void> {
        await this.updateNoteSize(this.newLength, this.newColumn);
        this.markTrackAsDirty(); // Mark track as dirty when note is resized
        this.log('Execute', {
            trackId: this.trackId,
            noteId: this.noteId,
            from: { length: this.oldLength, column: this.oldColumn },
            to: { length: this.newLength, column: this.newColumn }
        });
    }

    async undo(): Promise<void> {
        await this.updateNoteSize(this.oldLength, this.oldColumn);
        this.markTrackAsDirty(); // Mark track as dirty when note resize is undone
        this.log('Undo', {
            trackId: this.trackId,
            noteId: this.noteId,
            from: { length: this.newLength, column: this.newColumn },
            to: { length: this.oldLength, column: this.oldColumn }
        });
    }
}

/**
 * Export all note actions
 */
export const NoteActions = {
    AddNote: AddNoteAction,
    DeleteNote: DeleteNoteAction,
    MoveNote: MoveNoteAction,
    ResizeNote: ResizeNoteAction
};