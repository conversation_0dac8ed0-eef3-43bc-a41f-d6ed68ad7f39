import React, { useMemo } from 'react';
import { useGridStore } from '../../../core/state/gridStore';
import { calculateAudioTrackWidth } from '../../../utils/trackWidthCalculators';
import { getTrackColor } from '../../../constants/gridConstants';
import BaseTrackPreview from '../base/BaseTrackPreview';
import WaveformDisplay from '../WaveformDisplay';
import { TrackPreviewProps } from '../types';
import { Tool } from '../../../shared/interactions';

/**
 * AudioTrackPreview is a specialized track component for audio tracks.
 * It provides audio-specific visualization with waveform display and
 * handles audio track width calculations based on audio duration and BPM.
 */

interface AudioTrackPreviewProps extends TrackPreviewProps {
  isSelected?: boolean;
  selectedTool?: Tool;
  isGroupDrag?: boolean;
  isPrimaryDrag?: boolean;
  onTrackClick?: (trackId: string, event: { shiftKey?: boolean; ctrlKey?: boolean; metaKey?: boolean }) => void;
  onTrackDoubleClick?: (trackId: string) => void;
  interaction?: any;
}

export const AudioTrackPreview: React.FC<AudioTrackPreviewProps> = (props) => {
  const { 
    track, 
    bpm, 
    trackIndex = 0,
    trackColor: providedTrackColor,
    trackWidth: providedTrackWidth,
    onResizeEnd,
    isSelected,
    selectedTool,
    isGroupDrag,
    isPrimaryDrag,
    onTrackClick,
    onTrackDoubleClick,
    interaction,
    ...restProps 
  } = props;
  
  const audioMeasureWidth = useGridStore(state => state.audioMeasurePixelWidth);
  const trackColor = providedTrackColor || getTrackColor(trackIndex);
  
  // Calculate display width for the track container (viewport)
  const trackWidth = useMemo(() => {
    // If a width is explicitly provided, use it - this is critical for resize operations
    if (providedTrackWidth && providedTrackWidth > 0) {
      return providedTrackWidth;
    }
    
    // Only calculate if we don't have an explicit width
    return calculateAudioTrackWidth(
      track.duration_ticks || 8, // Default to 8 seconds if no duration specified
      bpm,
      audioMeasureWidth
    );
  }, [track.duration_ticks, bpm, audioMeasureWidth, providedTrackWidth]);
  
  // Calculate full content width - this should NEVER change due to trimming
  // This is the full width of the audio waveform
  const fullContentWidth = useMemo(() => {
    return calculateAudioTrackWidth(
      track.duration_ticks || 8, // Default to 8 seconds if no duration specified
      bpm,
      audioMeasureWidth
    );
  }, [track.duration_ticks, bpm, audioMeasureWidth]);
  
  // Audio-specific track content rendering
  const renderTrackContent = () => {
    if (track) {
      return (
        <WaveformDisplay 
          audioFile={undefined} // TODO: Fix this
          trackColor={trackColor}
          duration={track.duration_ticks || 0}
          width={fullContentWidth} // Always use the full content width
        />
      );
    } else {
      // Placeholder waveform for tracks without audio files
      return Array.from({length: 40}).map((_, i) => (
        <div 
          key={i} 
          className="bg-white/70 mx-0.5"
          style={{
            height: Math.sin(i * 0.3) * 10 + 10,
            width: 2
          }}
        />
      ));
    }
  };
  
  return (
    <BaseTrackPreview
      {...restProps}
      track={track}
      trackWidth={trackWidth}
      contentWidth={fullContentWidth}
      trackColor={trackColor}
      bpm={bpm}
      renderTrackContent={renderTrackContent}
      trackIndex={trackIndex}
      onResizeEnd={onResizeEnd}
      isSelected={isSelected}
      selectedTool={selectedTool}
      isGroupDrag={isGroupDrag}
      isPrimaryDrag={isPrimaryDrag}
      onTrackClick={onTrackClick}
      onTrackDoubleClick={onTrackDoubleClick}
      interaction={interaction}
    />
  );
};

export default React.memo(AudioTrackPreview);