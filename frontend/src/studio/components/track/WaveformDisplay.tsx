import React, { useEffect, useRef } from 'react';

interface WaveformDisplayProps {
  audioFile?: File;
  waveformData?: number[] | null;
  trackColor: string;
  duration: number;
  width: number;
}

export const WaveformDisplay: React.FC<WaveformDisplayProps> = ({ 
  audioFile, 
  waveformData,
  trackColor,
  duration,
  width
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (!canvasRef.current) {
      console.error("Missing canvas reference");
      return;
    }
    
    // If we have waveform data but no audio file, use the data directly
    if (!audioFile && waveformData) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error("Failed to get canvas context");
        return;
      }
      
      setupCanvas(canvas, ctx, width);
      drawWaveformFromData(waveformData, canvas, ctx, trackColor);
      return;
    }
    
    if (!audioFile) {
      console.error("Missing audio file or waveform data");
      return;
    }
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error("Failed to get canvas context");
      return;
    }
    
    // Set up canvas dimensions based on parent element
    
    // Set up high-DPI canvas for sharper rendering
    const dpr = window.devicePixelRatio || 1;
    const displayWidth = width;
    // Use a larger default height to ensure the waveform is visible
    const displayHeight = canvas.parentElement?.clientHeight || 80;
    
    // Set display size in CSS pixels
    canvas.style.width = `${displayWidth}px`;
    canvas.style.height = `${displayHeight}px`;
    
    // Set canvas size accounting for device pixel ratio for sharper rendering
    canvas.width = displayWidth * dpr;
    canvas.height = displayHeight * dpr;
    
    // Scale all drawing operations to account for the device pixel ratio
    ctx.scale(dpr, dpr);
    
    // Clear canvas with transparent background
    ctx.clearRect(0, 0, displayWidth, displayHeight);
    
    // Process and draw the audio file
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target?.result) {
        console.error("Failed to read file");
        return;
      }
      
      // Create audio context
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Convert array buffer to audio buffer
      audioContext.decodeAudioData(event.target.result as ArrayBuffer)
        .then(audioBuffer => {
          // Get the raw audio data from the first channel
          const channelData = audioBuffer.getChannelData(0);
          
          // Draw the waveform
          drawWaveform(channelData, canvas, ctx, trackColor);
        })
        .catch(error => {
          console.error("Error decoding audio data:", error);
          throw new Error("Failed to decode audio data");
        });
    };
    
    reader.onerror = () => {
      console.error("Error reading file");
      throw new Error("Failed to read audio file");
    };
    
    // Start reading the file
    reader.readAsArrayBuffer(audioFile);
  }, [audioFile, waveformData, trackColor, width]);
  
  return (
    <div className="h-full w-full flex items-center overflow-hidden relative">
      <canvas 
        ref={canvasRef} 
        className="absolute left-0 top-0 w-full h-full block"
      />
    </div>
  );
};

// Draw the waveform from audio data
function drawWaveform(
  channelData: Float32Array, 
  canvas: HTMLCanvasElement, 
  ctx: CanvasRenderingContext2D, 
  trackColor: string
) {
  const dpr = window.devicePixelRatio || 1;
  // Get logical dimensions (not the scaled canvas dimensions)
  const width = canvas.width / dpr;
  const height = canvas.height / dpr;
  const middle = height / 2;
  
  // Draw waveform with calculated dimensions
  
  // Clear canvas with a transparent background
  ctx.clearRect(0, 0, width, height);
  
  // Enable anti-aliasing for smoother rendering
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  
  // Calculate how many samples per pixel for optimal resolution
  const totalSamples = channelData.length;
  const samplesPerPixel = Math.max(1, Math.floor(totalSamples / width));
  
  // Store the waveform points for drawing the outline
  const topPoints: {x: number, y: number}[] = [];
  const bottomPoints: {x: number, y: number}[] = [];
  
  // Draw center line for reference
  ctx.fillStyle = 'rgba(255,255,255,0.2)';
  ctx.fillRect(0, middle, width, 1);
  
  // Use track color with opacity for the waveform
  ctx.fillStyle = `${trackColor}40`;
  
  // For each pixel column in the canvas
  for (let x = 0; x < width; x++) {
    // Find the starting sample index for this pixel
    const startSample = Math.floor(x * totalSamples / width);
    
    // Find min and max in this sample range for better visualization
    let min = 0;
    let max = 0;
    
    // Analyze all samples for this pixel location
    for (let i = 0; i < samplesPerPixel; i++) {
      const sampleIndex = startSample + i;
      if (sampleIndex < totalSamples) {
        const sample = channelData[sampleIndex];
        if (sample < min) min = sample;
        if (sample > max) max = sample;
      }
    }
    
    // Ensure we have at least some height (important for quieter parts)
    const minHeight = height * 0.05; // At least 5% of height
    
    // Calculate bar height with a minimum size and proper scaling
    const normalizedMin = Math.min(0, min); // Ensure some negative value 
    const normalizedMax = Math.max(0, max);  // Ensure some positive value
    
    // Scale the values (audio can be very quiet, so we apply some amplification)
    const scaleFactor = 0.5; // Reduced scale factor for 50% height
    
    // Calculate top and bottom y-coordinates
    const topY = middle + (normalizedMin * height * scaleFactor);
    const bottomY = middle + (normalizedMax * height * scaleFactor);
    
    // Store points for outline drawing
    topPoints.push({x, y: topY});
    bottomPoints.push({x, y: bottomY});
    
    // Draw the bar for this pixel (fill)
    const barHeight = bottomY - topY;
    ctx.fillRect(x, topY, 1, barHeight);
  }

  // Use track color for outline
  ctx.strokeStyle = trackColor;
  ctx.lineWidth = 1;
  
  // Top outline
  ctx.beginPath();
  if (topPoints.length > 0) {
    ctx.moveTo(topPoints[0].x, topPoints[0].y);
    
    // Draw the top outline path
    for (let i = 1; i < topPoints.length; i++) {
      ctx.lineTo(topPoints[i].x, topPoints[i].y);
    }
    
    // Draw the stroke
    ctx.stroke();
  }
  
  // Bottom outline
  ctx.beginPath();
  if (bottomPoints.length > 0) {
    ctx.moveTo(bottomPoints[0].x, bottomPoints[0].y);
    
    // Draw the bottom outline path
    for (let i = 1; i < bottomPoints.length; i++) {
      ctx.lineTo(bottomPoints[i].x, bottomPoints[i].y);
    }
    
    // Draw the stroke
    ctx.stroke();
  }
}

// Setup canvas function
function setupCanvas(canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D, width: number) {
  const dpr = window.devicePixelRatio || 1;
  const displayWidth = width;
  const displayHeight = canvas.parentElement?.clientHeight || 80;
  
  canvas.style.width = `${displayWidth}px`;
  canvas.style.height = `${displayHeight}px`;
  
  canvas.width = displayWidth * dpr;
  canvas.height = displayHeight * dpr;
  
  ctx.scale(dpr, dpr);
  ctx.clearRect(0, 0, displayWidth, displayHeight);
}

// Draw waveform from pre-computed data array
function drawWaveformFromData(
  data: number[],
  canvas: HTMLCanvasElement,
  ctx: CanvasRenderingContext2D,
  trackColor: string
) {
  const dpr = window.devicePixelRatio || 1;
  const width = canvas.width / dpr;
  const height = canvas.height / dpr;
  const middle = height / 2;
  
  ctx.clearRect(0, 0, width, height);
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  
  // Draw center line
  ctx.fillStyle = `${trackColor}33`;
  ctx.fillRect(0, middle, width, 1);
  
  // Use track color with opacity for the waveform
  ctx.fillStyle = `${trackColor}CC`;
  
  const barWidth = width / data.length;
  
  // Draw each bar
  data.forEach((value, index) => {
    const x = index * barWidth;
    const barHeight = Math.max(2, value * height * 0.8);
    const y = middle - barHeight / 2;
    
    ctx.fillRect(x, y, Math.max(1, barWidth - 1), barHeight);
  });
  
  // Draw outline
  ctx.strokeStyle = trackColor;
  ctx.lineWidth = 1;
  
  // Top outline
  ctx.beginPath();
  data.forEach((value, index) => {
    const x = index * barWidth;
    const barHeight = Math.max(2, value * height * 0.8);
    const topY = middle - barHeight / 2;
    
    if (index === 0) {
      ctx.moveTo(x, topY);
    } else {
      ctx.lineTo(x, topY);
    }
  });
  ctx.stroke();
  
  // Bottom outline
  ctx.beginPath();
  data.forEach((value, index) => {
    const x = index * barWidth;
    const barHeight = Math.max(2, value * height * 0.8);
    const bottomY = middle + barHeight / 2;
    
    if (index === 0) {
      ctx.moveTo(x, bottomY);
    } else {
      ctx.lineTo(x, bottomY);
    }
  });
  ctx.stroke();
}

export default WaveformDisplay;