import React, { useMemo } from 'react';
import { useGridStore } from '../../../core/state/gridStore';
import { GRID_CONSTANTS, getTrackColor } from '../../../constants/gridConstants';
import { calculateMidiTrackWidth } from '../../../utils/trackWidthCalculators';
import MidiNotesPreview from '../../piano-roll/components/MidiNotesPreview';
import BaseTrackPreview from '../base/BaseTrackPreview';
import { TrackPreviewProps } from '../types';
import { useStudioStore } from '../../../stores/studioStore';
import { Tool } from '../../../shared/interactions';

/**
 * MidiTrackPreview is a specialized track component for MIDI tracks.
 * It provides MIDI-specific visualization with note previews and
 * handles MIDI track width calculations based on note positions and time signature.
 * Includes a trigger area to open the piano roll editor.
 */

interface MidiTrackPreviewProps extends TrackPreviewProps {
  isSelected?: boolean;
  selectedTool?: Tool;
  isGroupDrag?: boolean;
  isPrimaryDrag?: boolean;
  onTrackClick?: (trackId: string, event: { shiftKey?: boolean; ctrlKey?: boolean; metaKey?: boolean }) => void;
  onTrackDoubleClick?: (trackId: string) => void;
  interaction?: any;
}

export const MidiTrackPreview: React.FC<MidiTrackPreviewProps> = (props) => {
  const { 
    track, 
    timeSignature = [4, 4],
    trackIndex = 0,
    trackColor: providedTrackColor,
    trackWidth: providedTrackWidth,
    onResizeEnd,
    isSelected,
    selectedTool,
    isGroupDrag,
    isPrimaryDrag,
    onTrackClick,
    onTrackDoubleClick,
    interaction,
    ...restProps
  } = props;
  
  const midiMeasureWidth = useGridStore(state => state.midiMeasurePixelWidth);
  const trackColor = providedTrackColor || getTrackColor(trackIndex);
  
  // Get notes directly from MidiManager
  const { store } = useStudioStore();
  const midiManager = store?.getMidiManager();
  const trackNotes = midiManager?.getTrackNotes(track.id) || [];
  
  // Calculate display width for the track container (viewport)
  const trackWidth = useMemo(() => {
    // If a width is explicitly provided, use it - this is critical for resize operations
    if (providedTrackWidth && providedTrackWidth > 0) {
      return providedTrackWidth;
    }
    
    // Only calculate if we don't have an explicit width
    return calculateMidiTrackWidth(
      trackNotes,
      timeSignature,
      midiMeasureWidth
    );
  }, [trackNotes, timeSignature, midiMeasureWidth, providedTrackWidth]);
  
  // Calculate full content width - this should NEVER change due to trimming
  // This is the full width of all MIDI notes
  const fullContentWidth = useMemo(() => {
    return calculateMidiTrackWidth(
      trackNotes,
      timeSignature,
      midiMeasureWidth
    );
  }, [trackNotes, timeSignature, midiMeasureWidth]);
  
  // MIDI-specific track content rendering
  const renderTrackContent = () => (
    <>
      <div
        className="piano-roll-trigger absolute top-0 left-0 w-full h-full cursor-pointer opacity-30 hover:bg-white/30 transition-colors"
        data-testid="piano-roll-trigger"
        data-track-id={track.id}
        data-track-type={track.track_type}
        style={{
          zIndex: 100,
          backgroundColor: 'rgba(0, 100, 255, 0.1)'
        }}
      />
      {/* MidiNotesPreview is always rendered at full content width, 
          but only part of it is visible through the trimmed container */}
      <MidiNotesPreview 
        width={fullContentWidth} // Always use the full content width
        height={GRID_CONSTANTS.trackHeight - 6}
        noteColor={'white'}
        notes={trackNotes}
      />
    </>
  );
  
  return (
    <BaseTrackPreview
      {...restProps}
      track={track}
      trackWidth={trackWidth}
      contentWidth={fullContentWidth}
      trackColor={trackColor}
      timeSignature={timeSignature}
      renderTrackContent={renderTrackContent}
      trackIndex={trackIndex}
      onResizeEnd={onResizeEnd}
      isSelected={isSelected}
      selectedTool={selectedTool}
      isGroupDrag={isGroupDrag}
      isPrimaryDrag={isPrimaryDrag}
      onTrackClick={onTrackClick}
      onTrackDoubleClick={onTrackDoubleClick}
      interaction={interaction}
    />
  );
};

export default React.memo(MidiTrackPreview);