/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as StudioImport } from './routes/studio'
import { Route as SettingsImport } from './routes/settings'
import { Route as SamplerTracksImport } from './routes/sampler-tracks'
import { Route as ResetPasswordImport } from './routes/reset-password'
import { Route as RegisterImport } from './routes/register'
import { Route as ProjectsImport } from './routes/projects'
import { Route as ProfileImport } from './routes/profile'
import { Route as PricingImport } from './routes/pricing'
import { Route as OauthCallbackImport } from './routes/oauth-callback'
import { Route as MidiTracksImport } from './routes/midi-tracks'
import { Route as LoginImport } from './routes/login'
import { Route as HomeImport } from './routes/home'
import { Route as DrumTracksImport } from './routes/drum-tracks'
import { Route as CreditsImport } from './routes/credits'
import { Route as CompleteProfileImport } from './routes/complete-profile'
import { Route as CheckoutImport } from './routes/checkout'
import { Route as AudioTracksImport } from './routes/audio-tracks'
import { Route as IndexImport } from './routes/index'
import { Route as SubscriptionSuccessImport } from './routes/subscription.success'
import { Route as SubscriptionCancelImport } from './routes/subscription.cancel'
import { Route as CheckoutReturnImport } from './routes/checkout/return'
import { Route as AccountSubscriptionImport } from './routes/account.subscription'

// Create/Update Routes

const StudioRoute = StudioImport.update({
  id: '/studio',
  path: '/studio',
  getParentRoute: () => rootRoute,
} as any)

const SettingsRoute = SettingsImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRoute,
} as any)

const SamplerTracksRoute = SamplerTracksImport.update({
  id: '/sampler-tracks',
  path: '/sampler-tracks',
  getParentRoute: () => rootRoute,
} as any)

const ResetPasswordRoute = ResetPasswordImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRoute,
} as any)

const RegisterRoute = RegisterImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRoute,
} as any)

const ProjectsRoute = ProjectsImport.update({
  id: '/projects',
  path: '/projects',
  getParentRoute: () => rootRoute,
} as any)

const ProfileRoute = ProfileImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => rootRoute,
} as any)

const PricingRoute = PricingImport.update({
  id: '/pricing',
  path: '/pricing',
  getParentRoute: () => rootRoute,
} as any)

const OauthCallbackRoute = OauthCallbackImport.update({
  id: '/oauth-callback',
  path: '/oauth-callback',
  getParentRoute: () => rootRoute,
} as any)

const MidiTracksRoute = MidiTracksImport.update({
  id: '/midi-tracks',
  path: '/midi-tracks',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const HomeRoute = HomeImport.update({
  id: '/home',
  path: '/home',
  getParentRoute: () => rootRoute,
} as any)

const DrumTracksRoute = DrumTracksImport.update({
  id: '/drum-tracks',
  path: '/drum-tracks',
  getParentRoute: () => rootRoute,
} as any)

const CreditsRoute = CreditsImport.update({
  id: '/credits',
  path: '/credits',
  getParentRoute: () => rootRoute,
} as any)

const CompleteProfileRoute = CompleteProfileImport.update({
  id: '/complete-profile',
  path: '/complete-profile',
  getParentRoute: () => rootRoute,
} as any)

const CheckoutRoute = CheckoutImport.update({
  id: '/checkout',
  path: '/checkout',
  getParentRoute: () => rootRoute,
} as any)

const AudioTracksRoute = AudioTracksImport.update({
  id: '/audio-tracks',
  path: '/audio-tracks',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const SubscriptionSuccessRoute = SubscriptionSuccessImport.update({
  id: '/subscription/success',
  path: '/subscription/success',
  getParentRoute: () => rootRoute,
} as any)

const SubscriptionCancelRoute = SubscriptionCancelImport.update({
  id: '/subscription/cancel',
  path: '/subscription/cancel',
  getParentRoute: () => rootRoute,
} as any)

const CheckoutReturnRoute = CheckoutReturnImport.update({
  id: '/return',
  path: '/return',
  getParentRoute: () => CheckoutRoute,
} as any)

const AccountSubscriptionRoute = AccountSubscriptionImport.update({
  id: '/account/subscription',
  path: '/account/subscription',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/audio-tracks': {
      id: '/audio-tracks'
      path: '/audio-tracks'
      fullPath: '/audio-tracks'
      preLoaderRoute: typeof AudioTracksImport
      parentRoute: typeof rootRoute
    }
    '/checkout': {
      id: '/checkout'
      path: '/checkout'
      fullPath: '/checkout'
      preLoaderRoute: typeof CheckoutImport
      parentRoute: typeof rootRoute
    }
    '/complete-profile': {
      id: '/complete-profile'
      path: '/complete-profile'
      fullPath: '/complete-profile'
      preLoaderRoute: typeof CompleteProfileImport
      parentRoute: typeof rootRoute
    }
    '/credits': {
      id: '/credits'
      path: '/credits'
      fullPath: '/credits'
      preLoaderRoute: typeof CreditsImport
      parentRoute: typeof rootRoute
    }
    '/drum-tracks': {
      id: '/drum-tracks'
      path: '/drum-tracks'
      fullPath: '/drum-tracks'
      preLoaderRoute: typeof DrumTracksImport
      parentRoute: typeof rootRoute
    }
    '/home': {
      id: '/home'
      path: '/home'
      fullPath: '/home'
      preLoaderRoute: typeof HomeImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/midi-tracks': {
      id: '/midi-tracks'
      path: '/midi-tracks'
      fullPath: '/midi-tracks'
      preLoaderRoute: typeof MidiTracksImport
      parentRoute: typeof rootRoute
    }
    '/oauth-callback': {
      id: '/oauth-callback'
      path: '/oauth-callback'
      fullPath: '/oauth-callback'
      preLoaderRoute: typeof OauthCallbackImport
      parentRoute: typeof rootRoute
    }
    '/pricing': {
      id: '/pricing'
      path: '/pricing'
      fullPath: '/pricing'
      preLoaderRoute: typeof PricingImport
      parentRoute: typeof rootRoute
    }
    '/profile': {
      id: '/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof ProfileImport
      parentRoute: typeof rootRoute
    }
    '/projects': {
      id: '/projects'
      path: '/projects'
      fullPath: '/projects'
      preLoaderRoute: typeof ProjectsImport
      parentRoute: typeof rootRoute
    }
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterImport
      parentRoute: typeof rootRoute
    }
    '/reset-password': {
      id: '/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof ResetPasswordImport
      parentRoute: typeof rootRoute
    }
    '/sampler-tracks': {
      id: '/sampler-tracks'
      path: '/sampler-tracks'
      fullPath: '/sampler-tracks'
      preLoaderRoute: typeof SamplerTracksImport
      parentRoute: typeof rootRoute
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsImport
      parentRoute: typeof rootRoute
    }
    '/studio': {
      id: '/studio'
      path: '/studio'
      fullPath: '/studio'
      preLoaderRoute: typeof StudioImport
      parentRoute: typeof rootRoute
    }
    '/account/subscription': {
      id: '/account/subscription'
      path: '/account/subscription'
      fullPath: '/account/subscription'
      preLoaderRoute: typeof AccountSubscriptionImport
      parentRoute: typeof rootRoute
    }
    '/checkout/return': {
      id: '/checkout/return'
      path: '/return'
      fullPath: '/checkout/return'
      preLoaderRoute: typeof CheckoutReturnImport
      parentRoute: typeof CheckoutImport
    }
    '/subscription/cancel': {
      id: '/subscription/cancel'
      path: '/subscription/cancel'
      fullPath: '/subscription/cancel'
      preLoaderRoute: typeof SubscriptionCancelImport
      parentRoute: typeof rootRoute
    }
    '/subscription/success': {
      id: '/subscription/success'
      path: '/subscription/success'
      fullPath: '/subscription/success'
      preLoaderRoute: typeof SubscriptionSuccessImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

interface CheckoutRouteChildren {
  CheckoutReturnRoute: typeof CheckoutReturnRoute
}

const CheckoutRouteChildren: CheckoutRouteChildren = {
  CheckoutReturnRoute: CheckoutReturnRoute,
}

const CheckoutRouteWithChildren = CheckoutRoute._addFileChildren(
  CheckoutRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/audio-tracks': typeof AudioTracksRoute
  '/checkout': typeof CheckoutRouteWithChildren
  '/complete-profile': typeof CompleteProfileRoute
  '/credits': typeof CreditsRoute
  '/drum-tracks': typeof DrumTracksRoute
  '/home': typeof HomeRoute
  '/login': typeof LoginRoute
  '/midi-tracks': typeof MidiTracksRoute
  '/oauth-callback': typeof OauthCallbackRoute
  '/pricing': typeof PricingRoute
  '/profile': typeof ProfileRoute
  '/projects': typeof ProjectsRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/sampler-tracks': typeof SamplerTracksRoute
  '/settings': typeof SettingsRoute
  '/studio': typeof StudioRoute
  '/account/subscription': typeof AccountSubscriptionRoute
  '/checkout/return': typeof CheckoutReturnRoute
  '/subscription/cancel': typeof SubscriptionCancelRoute
  '/subscription/success': typeof SubscriptionSuccessRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/audio-tracks': typeof AudioTracksRoute
  '/checkout': typeof CheckoutRouteWithChildren
  '/complete-profile': typeof CompleteProfileRoute
  '/credits': typeof CreditsRoute
  '/drum-tracks': typeof DrumTracksRoute
  '/home': typeof HomeRoute
  '/login': typeof LoginRoute
  '/midi-tracks': typeof MidiTracksRoute
  '/oauth-callback': typeof OauthCallbackRoute
  '/pricing': typeof PricingRoute
  '/profile': typeof ProfileRoute
  '/projects': typeof ProjectsRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/sampler-tracks': typeof SamplerTracksRoute
  '/settings': typeof SettingsRoute
  '/studio': typeof StudioRoute
  '/account/subscription': typeof AccountSubscriptionRoute
  '/checkout/return': typeof CheckoutReturnRoute
  '/subscription/cancel': typeof SubscriptionCancelRoute
  '/subscription/success': typeof SubscriptionSuccessRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/audio-tracks': typeof AudioTracksRoute
  '/checkout': typeof CheckoutRouteWithChildren
  '/complete-profile': typeof CompleteProfileRoute
  '/credits': typeof CreditsRoute
  '/drum-tracks': typeof DrumTracksRoute
  '/home': typeof HomeRoute
  '/login': typeof LoginRoute
  '/midi-tracks': typeof MidiTracksRoute
  '/oauth-callback': typeof OauthCallbackRoute
  '/pricing': typeof PricingRoute
  '/profile': typeof ProfileRoute
  '/projects': typeof ProjectsRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRoute
  '/sampler-tracks': typeof SamplerTracksRoute
  '/settings': typeof SettingsRoute
  '/studio': typeof StudioRoute
  '/account/subscription': typeof AccountSubscriptionRoute
  '/checkout/return': typeof CheckoutReturnRoute
  '/subscription/cancel': typeof SubscriptionCancelRoute
  '/subscription/success': typeof SubscriptionSuccessRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/audio-tracks'
    | '/checkout'
    | '/complete-profile'
    | '/credits'
    | '/drum-tracks'
    | '/home'
    | '/login'
    | '/midi-tracks'
    | '/oauth-callback'
    | '/pricing'
    | '/profile'
    | '/projects'
    | '/register'
    | '/reset-password'
    | '/sampler-tracks'
    | '/settings'
    | '/studio'
    | '/account/subscription'
    | '/checkout/return'
    | '/subscription/cancel'
    | '/subscription/success'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/audio-tracks'
    | '/checkout'
    | '/complete-profile'
    | '/credits'
    | '/drum-tracks'
    | '/home'
    | '/login'
    | '/midi-tracks'
    | '/oauth-callback'
    | '/pricing'
    | '/profile'
    | '/projects'
    | '/register'
    | '/reset-password'
    | '/sampler-tracks'
    | '/settings'
    | '/studio'
    | '/account/subscription'
    | '/checkout/return'
    | '/subscription/cancel'
    | '/subscription/success'
  id:
    | '__root__'
    | '/'
    | '/audio-tracks'
    | '/checkout'
    | '/complete-profile'
    | '/credits'
    | '/drum-tracks'
    | '/home'
    | '/login'
    | '/midi-tracks'
    | '/oauth-callback'
    | '/pricing'
    | '/profile'
    | '/projects'
    | '/register'
    | '/reset-password'
    | '/sampler-tracks'
    | '/settings'
    | '/studio'
    | '/account/subscription'
    | '/checkout/return'
    | '/subscription/cancel'
    | '/subscription/success'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AudioTracksRoute: typeof AudioTracksRoute
  CheckoutRoute: typeof CheckoutRouteWithChildren
  CompleteProfileRoute: typeof CompleteProfileRoute
  CreditsRoute: typeof CreditsRoute
  DrumTracksRoute: typeof DrumTracksRoute
  HomeRoute: typeof HomeRoute
  LoginRoute: typeof LoginRoute
  MidiTracksRoute: typeof MidiTracksRoute
  OauthCallbackRoute: typeof OauthCallbackRoute
  PricingRoute: typeof PricingRoute
  ProfileRoute: typeof ProfileRoute
  ProjectsRoute: typeof ProjectsRoute
  RegisterRoute: typeof RegisterRoute
  ResetPasswordRoute: typeof ResetPasswordRoute
  SamplerTracksRoute: typeof SamplerTracksRoute
  SettingsRoute: typeof SettingsRoute
  StudioRoute: typeof StudioRoute
  AccountSubscriptionRoute: typeof AccountSubscriptionRoute
  SubscriptionCancelRoute: typeof SubscriptionCancelRoute
  SubscriptionSuccessRoute: typeof SubscriptionSuccessRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AudioTracksRoute: AudioTracksRoute,
  CheckoutRoute: CheckoutRouteWithChildren,
  CompleteProfileRoute: CompleteProfileRoute,
  CreditsRoute: CreditsRoute,
  DrumTracksRoute: DrumTracksRoute,
  HomeRoute: HomeRoute,
  LoginRoute: LoginRoute,
  MidiTracksRoute: MidiTracksRoute,
  OauthCallbackRoute: OauthCallbackRoute,
  PricingRoute: PricingRoute,
  ProfileRoute: ProfileRoute,
  ProjectsRoute: ProjectsRoute,
  RegisterRoute: RegisterRoute,
  ResetPasswordRoute: ResetPasswordRoute,
  SamplerTracksRoute: SamplerTracksRoute,
  SettingsRoute: SettingsRoute,
  StudioRoute: StudioRoute,
  AccountSubscriptionRoute: AccountSubscriptionRoute,
  SubscriptionCancelRoute: SubscriptionCancelRoute,
  SubscriptionSuccessRoute: SubscriptionSuccessRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/audio-tracks",
        "/checkout",
        "/complete-profile",
        "/credits",
        "/drum-tracks",
        "/home",
        "/login",
        "/midi-tracks",
        "/oauth-callback",
        "/pricing",
        "/profile",
        "/projects",
        "/register",
        "/reset-password",
        "/sampler-tracks",
        "/settings",
        "/studio",
        "/account/subscription",
        "/subscription/cancel",
        "/subscription/success"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/audio-tracks": {
      "filePath": "audio-tracks.tsx"
    },
    "/checkout": {
      "filePath": "checkout.tsx",
      "children": [
        "/checkout/return"
      ]
    },
    "/complete-profile": {
      "filePath": "complete-profile.tsx"
    },
    "/credits": {
      "filePath": "credits.tsx"
    },
    "/drum-tracks": {
      "filePath": "drum-tracks.tsx"
    },
    "/home": {
      "filePath": "home.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/midi-tracks": {
      "filePath": "midi-tracks.tsx"
    },
    "/oauth-callback": {
      "filePath": "oauth-callback.tsx"
    },
    "/pricing": {
      "filePath": "pricing.tsx"
    },
    "/profile": {
      "filePath": "profile.tsx"
    },
    "/projects": {
      "filePath": "projects.tsx"
    },
    "/register": {
      "filePath": "register.tsx"
    },
    "/reset-password": {
      "filePath": "reset-password.tsx"
    },
    "/sampler-tracks": {
      "filePath": "sampler-tracks.tsx"
    },
    "/settings": {
      "filePath": "settings.tsx"
    },
    "/studio": {
      "filePath": "studio.tsx"
    },
    "/account/subscription": {
      "filePath": "account.subscription.tsx"
    },
    "/checkout/return": {
      "filePath": "checkout/return.tsx",
      "parent": "/checkout"
    },
    "/subscription/cancel": {
      "filePath": "subscription.cancel.tsx"
    },
    "/subscription/success": {
      "filePath": "subscription.success.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
