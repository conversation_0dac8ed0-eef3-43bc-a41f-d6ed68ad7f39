.projects-container {
  padding-top: 2rem;
  padding-bottom: 4rem;
}

.project-card,
.audio-track-card,
.midi-track-card,
.sampler-track-card,
.drum-track-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.project-card:hover,
.audio-track-card:hover,
.midi-track-card:hover,
.sampler-track-card:hover,
.drum-track-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.project-card-content {
  flex-grow: 1;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.project-empty-state {
  text-align: center;
  padding: 3rem 2rem;
}

.project-empty-icon {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: 1rem;
}

.project-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

.project-metadata {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  font-size: 0.875rem;
  /* color: rgba(0, 0, 0, 0.6); */ /* Removed to allow Tailwind class to take effect */
}

.project-divider {
  margin: 1rem 0;
}

.hover-shadow-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-shadow-card:hover {
  box-shadow: 0 0 12px var(--hover-shadow-color);
}