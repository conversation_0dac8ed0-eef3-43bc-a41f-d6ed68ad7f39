var Rs=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(A,t)=>(typeof require<"u"?require:A)[t]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});function Yn(e){e=Math.floor(e);let A=Math.floor(e/60),t=Math.round(e-A*60);return{minutes:A,seconds:t,time:`${A.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`}}function Gs(e){return e.trim().replaceAll(".mid","").replaceAll(".kar","").replaceAll(".rmi","").replaceAll(".xmf","").replaceAll(".mxmf","").replaceAll("_"," ").trim()}function HA(e){let A="";for(let t=0;t<e.length;t++){let n=e[t].toString(16).padStart(2,"0").toUpperCase();A+=n,A+=" "}return A}function Ms(e){let A=[];for(let t of e)(t===47||t===92)&&(t=10),A.push(t);return new Uint8Array(A)}var I={warn:"color: orange;",unrecognized:"color: red;",info:"color: aqua;",recognized:"color: lime",value:"color: yellow; background-color: black;"};var wt={backwards:0,forwards:1,shuffleOn:2,shuffleOff:3},zA={loadNewSongList:0,pause:1,stop:2,play:3,setTime:4,changeMIDIMessageSending:5,setPlaybackRate:6,setLoop:7,changeSong:8,getMIDI:9,setSkipToFirstNote:10,setPreservePlaybackState:11},UA={midiEvent:0,songChange:1,textEvent:2,timeChange:3,pause:4,getMIDI:5,midiError:6,metaEvent:7,loopCountChange:8};var L=class extends Uint8Array{currentIndex=0;constructor(A){super(A)}slice(A,t){let n=super.slice(A,t);return n.currentIndex=0,n}};function wA(e){let A=e.reduce((s,o)=>s+o.length,0),t=new L(A),n=0;for(let s of e)t.set(s,n),n+=s.length;return t}var ne=class{ticks;messageStatusByte;messageData;constructor(A,t,n){this.ticks=A,this.messageStatusByte=t,this.messageData=n}};function xs(e){let A=e&240,t=e&15,n=t;switch(A){case 128:case 144:case 160:case 176:case 192:case 208:case 224:break;case 240:switch(t){case 0:n=-3;break;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:n=-1;break;case 15:n=-2;break}break;default:n=-1}return n}var F={noteOff:128,noteOn:144,polyPressure:160,controllerChange:176,programChange:192,channelPressure:208,pitchBend:224,systemExclusive:240,timecode:241,songPosition:242,songSelect:243,tuneRequest:246,clock:248,start:250,continue:251,stop:252,activeSensing:254,reset:255,sequenceNumber:0,text:1,copyright:2,trackName:3,instrumentName:4,lyric:5,marker:6,cuePoint:7,programName:8,midiChannelPrefix:32,midiPort:33,endOfTrack:47,setTempo:81,smpteOffset:84,timeSignature:88,keySignature:89,sequenceSpecific:127};function Zt(e){let A=e&240,t=e&15,n=-1,s=e;return A>=128&&A<=224&&(n=t,s=A),{status:s,channel:n}}var y={bankSelect:0,modulationWheel:1,breathController:2,footController:4,portamentoTime:5,dataEntryMsb:6,mainVolume:7,balance:8,pan:10,expressionController:11,effectControl1:12,effectControl2:13,generalPurposeController1:16,generalPurposeController2:17,generalPurposeController3:18,generalPurposeController4:19,lsbForControl0BankSelect:32,lsbForControl1ModulationWheel:33,lsbForControl2BreathController:34,lsbForControl4FootController:36,lsbForControl5PortamentoTime:37,lsbForControl6DataEntry:38,lsbForControl7MainVolume:39,lsbForControl8Balance:40,lsbForControl10Pan:42,lsbForControl11ExpressionController:43,lsbForControl12EffectControl1:44,lsbForControl13EffectControl2:45,sustainPedal:64,portamentoOnOff:65,sostenutoPedal:66,softPedal:67,legatoFootswitch:68,hold2Pedal:69,soundVariation:70,filterResonance:71,releaseTime:72,attackTime:73,brightness:74,decayTime:75,vibratoRate:76,vibratoDepth:77,vibratoDelay:78,soundController10:79,generalPurposeController5:80,generalPurposeController6:81,generalPurposeController7:82,generalPurposeController8:83,portamentoControl:84,reverbDepth:91,tremoloDepth:92,chorusDepth:93,detuneDepth:94,phaserDepth:95,dataIncrement:96,dataDecrement:97,NRPNLsb:98,NRPNMsb:99,RPNLsb:100,RPNMsb:101,allSoundOff:120,resetAllControllers:121,localControlOnOff:122,allNotesOff:123,omniModeOff:124,omniModeOn:125,monoModeOn:126,polyModeOn:127},Ns={8:2,9:2,10:2,11:2,12:1,13:1,14:2};var bs=!1,Ls=!0,Xt=!1,Us=!0;function Ts(e,A,t,n){bs=e,Ls=A,Xt=t,Us=n}function p(...e){bs&&console.info(...e)}function Y(...e){Ls&&console.warn(...e)}function vs(...e){Us&&console.table(...e)}function VA(...e){Xt&&console.group(...e)}function FA(...e){Xt&&console.groupCollapsed(...e)}function V(){Xt&&console.groupEnd()}function se(e,A){let t=0;for(let n=8*(A-1);n>=0;n-=8)t|=e[e.currentIndex++]<<n;return t>>>0}function Ft(e,A){let t=new Array(A).fill(0);for(let n=A-1;n>=0;n--)t[n]=e&255,e>>=8;return t}function Hs(e,A){if(this.sendMIDIMessages&&e.messageStatusByte>=128){this.sendMIDIMessage([e.messageStatusByte,...e.messageData]);return}let t=Zt(e.messageStatusByte),n=this.midiPortChannelOffsets[this.midiPorts[A]]||0;switch(t.channel+=n,t.status){case F.noteOn:let s=e.messageData[1];if(s>0)this.synth.noteOn(t.channel,e.messageData[0],s),this.playingNotes.push({midiNote:e.messageData[0],channel:t.channel,velocity:s});else{this.synth.noteOff(t.channel,e.messageData[0]);let g=this.playingNotes.findIndex(d=>d.midiNote===e.messageData[0]&&d.channel===t.channel);g!==-1&&this.playingNotes.splice(g,1)}break;case F.noteOff:this.synth.noteOff(t.channel,e.messageData[0]);let o=this.playingNotes.findIndex(g=>g.midiNote===e.messageData[0]&&g.channel===t.channel);o!==-1&&this.playingNotes.splice(o,1);break;case F.pitchBend:this.synth.pitchWheel(t.channel,e.messageData[1],e.messageData[0]);break;case F.controllerChange:if(this.midiData.isMultiPort&&this.midiData.usedChannelsOnTrack[A].size===0)return;this.synth.controllerChange(t.channel,e.messageData[0],e.messageData[1]);break;case F.programChange:if(this.midiData.isMultiPort&&this.midiData.usedChannelsOnTrack[A].size===0)return;this.synth.programChange(t.channel,e.messageData[0]);break;case F.polyPressure:this.synth.polyPressure(t.channel,e.messageData[0],e.messageData[1]);break;case F.channelPressure:this.synth.channelPressure(t.channel,e.messageData[0]);break;case F.systemExclusive:this.synth.systemExclusive(e.messageData,n);break;case F.setTempo:e.messageData.currentIndex=0;let r=6e7/se(e.messageData,3);this.oneTickToSeconds=60/(r*this.midiData.timeDivision),this.oneTickToSeconds===0&&(this.oneTickToSeconds=60/(120*this.midiData.timeDivision),Y("invalid tempo! falling back to 120 BPM"),r=120);break;case F.timeSignature:case F.endOfTrack:case F.midiChannelPrefix:case F.songPosition:case F.activeSensing:case F.keySignature:case F.sequenceNumber:case F.sequenceSpecific:break;case F.text:case F.lyric:case F.copyright:case F.trackName:case F.marker:case F.cuePoint:case F.instrumentName:case F.programName:let E=-1;t.status===F.lyric&&(E=Math.min(this.midiData.lyricsTicks.indexOf(e.ticks),this.midiData.lyrics.length-1));let a=t.status;this.midiData.isKaraokeFile&&(t.status===F.text||t.status===F.lyric)&&(E=Math.min(this.midiData.lyricsTicks.indexOf(e.ticks),this.midiData.lyricsTicks.length),a=F.lyric),this.post(UA.textEvent,[e.messageData,a,E]);break;case F.midiPort:this.assignMIDIPort(A,e.messageData[0]);break;case F.reset:this.synth.stopAllChannels(),this.synth.resetAllControllers();break;default:Y(`%cUnrecognized Event: %c${e.messageStatusByte}%c status byte: %c${Object.keys(F).find(g=>F[g]===t.status)}`,I.warn,I.unrecognized,I.warn,I.value);break}t.status>=0&&t.status<128&&this.post(UA.metaEvent,[e.messageStatusByte,e.messageData,A])}function Ys(){for(let e=0;e<16;e++)this.synth.createWorkletChannel(!0)}function Js(){if(!this.isActive)return;let e=this.currentTime;for(;this.playedTime<e;){let A=this._findFirstEventIndex(),t=this.tracks[A][this.eventIndex[A]];if(this._processEvent(t,A),this.eventIndex[A]++,A=this._findFirstEventIndex(),this.tracks[A].length<=this.eventIndex[A]){if(this.loop){this.setTimeTicks(this.midiData.loop.start);return}this.eventIndex[A]--,this.pause(!0),this.songs.length>1&&this.nextSong();return}let n=this.tracks[A][this.eventIndex[A]];this.playedTime+=this.oneTickToSeconds*(n.ticks-t.ticks);let s=this.loop&&(this.loopCount>0||this.loopCount===-1);if(this.midiData.loop.end<=t.ticks&&s){this.loopCount!==1/0&&(this.loopCount--,this.post(UA.loopCountChange,this.loopCount)),this.setTimeTicks(this.midiData.loop.start);return}else if(e>=this.duration){if(s){this.loopCount!==1/0&&(this.loopCount--,this.post(UA.loopCountChange,this.loopCount)),this.setTimeTicks(this.midiData.loop.start);return}this.eventIndex[A]--,this.pause(!0),this.songs.length>1&&this.nextSong();return}}}function Ks(){let e=0,A=1/0;return this.tracks.forEach((t,n)=>{this.eventIndex[n]>=t.length||t[this.eventIndex[n]].ticks<A&&(e=n,A=t[this.eventIndex[n]].ticks)}),e}var it=class{timeDivision=0;duration=0;tempoChanges=[{ticks:0,tempo:120}];copyright="";tracksAmount=0;lyrics=[];lyricsTicks=[];firstNoteOn=0;keyRange={min:0,max:127};lastVoiceEventTick=0;midiPorts=[0];midiPortChannelOffsets=[0];usedChannelsOnTrack=[];loop={start:0,end:0};midiName="";midiNameUsesFileName=!1;fileName="";rawMidiName=void 0;format=0;RMIDInfo={};bankOffset=0;isKaraokeFile=!1;isMultiPort=!1;MIDIticksToSeconds(A){let t=0;for(;A>0;){let n=this.tempoChanges.find(o=>o.ticks<A),s=A-n.ticks;t+=s*60/(n.tempo*this.timeDivision),A-=s}return t}};var Wt=class extends it{isEmbedded=!1;constructor(A){super(),this.timeDivision=A.timeDivision,this.duration=A.duration,this.tempoChanges=A.tempoChanges,this.copyright=A.copyright,this.tracksAmount=A.tracksAmount,this.lyrics=A.lyrics,this.lyricsTicks=A.lyricsTicks,this.firstNoteOn=A.firstNoteOn,this.keyRange=A.keyRange,this.lastVoiceEventTick=A.lastVoiceEventTick,this.midiPorts=A.midiPorts,this.midiPortChannelOffsets=A.midiPortChannelOffsets,this.usedChannelsOnTrack=A.usedChannelsOnTrack,this.loop=A.loop,this.midiName=A.midiName,this.midiNameUsesFileName=A.midiNameUsesFileName,this.fileName=A.fileName,this.rawMidiName=A.rawMidiName,this.format=A.format,this.RMIDInfo=A.RMIDInfo,this.bankOffset=A.bankOffset,this.isKaraokeFile=A.isKaraokeFile,this.isMultiPort=A.isMultiPort,this.isEmbedded=A.embeddedSoundFont!==void 0}},la={duration:99999,firstNoteOn:0,loop:{start:0,end:123456},lastVoiceEventTick:123456,lyrics:[],copyright:"",midiPorts:[],midiPortChannelOffsets:[],tracksAmount:0,tempoChanges:[{ticks:0,tempo:120}],fileName:"NOT_LOADED.mid",midiName:"Loading...",rawMidiName:new Uint8Array([76,111,97,100,105,110,103,46,46,46]),usedChannelsOnTrack:[],timeDivision:0,keyRange:{min:0,max:127},isEmbedded:!1,RMIDInfo:{},bankOffset:0,midiNameUsesFileName:!1,format:0};function N(e,A){let t=0;for(let n=0;n<A;n++)t|=e[e.currentIndex++]<<n*8;return t>>>0}function Xe(e,A,t){for(let n=0;n<t;n++)e[e.currentIndex++]=A>>n*8&255}function H(e,A){e[e.currentIndex++]=A&255,e[e.currentIndex++]=A>>8}function AA(e,A){Xe(e,A,4)}function Ne(e,A){let t=A<<8|e;return t>32767?t-65536:t}function qs(e){return e>127?e-256:e}function eA(e,A,t=void 0,n=!0){if(t){let s=e.slice(e.currentIndex,e.currentIndex+A);return e.currentIndex+=A,new TextDecoder(t.replace(/[^\x20-\x7E]/g,"")).decode(s.buffer)}else{let s=!1,o="";for(let r=0;r<A;r++){let E=e[e.currentIndex++];if(!s){if((E<32||E>127)&&E!==10){if(n){s=!0;continue}else if(E===0){s=!0;continue}}o+=String.fromCharCode(E)}}return o}}function at(e,A=0){let t=e.length;A>0&&(t=A);let n=new L(t);return TA(n,e,A),n}function fe(e){return at(e,e.length+1)}function TA(e,A,t=0){t>0&&A.length>t&&(A=A.slice(0,t));for(let n=0;n<A.length;n++)e[e.currentIndex++]=A.charCodeAt(n);if(t>A.length)for(let n=0;n<t-A.length;n++)e[e.currentIndex++]=0;return e}var hA=class{constructor(A,t,n){this.header=A,this.size=t,this.chunkData=n}};function IA(e,A=!0,t=!1){let n=eA(e,4),s=N(e,4),o;return A&&(o=new L(e.buffer.slice(e.currentIndex,e.currentIndex+s))),(A||t)&&(e.currentIndex+=s),s%2!==0&&e[e.currentIndex]===0&&e.currentIndex++,new hA(n,s,o)}function uA(e,A=void 0){let t=8+e.size;e.size%2!==0&&t++,A&&(t+=A.length);let n=new L(t);return A&&(n.set(A,n.currentIndex),n.currentIndex+=A.length),TA(n,e.header),AA(n,t-8-(A?.length||0)),n.set(e.chunkData,n.currentIndex),n}function z(e,A,t=!1,n=!1){if(t){let g=new Uint8Array(A.length+1);g.set(A),A=g}let s=8,o=s+A.length,r=A.length;o%2!==0&&o++;let E=e;n&&(o+=4,r+=4,s+=4,E="LIST");let a=new L(o);return TA(a,E),AA(a,r),n&&TA(a,e),a.set(A,s),a}function ZA(e,A){return e.find(t=>t.header!=="LIST"?!1:(t.chunkData.currentIndex=0,eA(t.chunkData,4)===A))}function yA(e){let A=0;for(;e;){let t=e[e.currentIndex++];if(A=A<<7|t&127,t>>7!==1)break}return A}function _t(e){let A=[e&127];for(e>>=7;e>0;)A.unshift(e&127|128),e>>=7;return A}var i={INVALID:-1,startAddrsOffset:0,endAddrOffset:1,startloopAddrsOffset:2,endloopAddrsOffset:3,startAddrsCoarseOffset:4,modLfoToPitch:5,vibLfoToPitch:6,modEnvToPitch:7,initialFilterFc:8,initialFilterQ:9,modLfoToFilterFc:10,modEnvToFilterFc:11,endAddrsCoarseOffset:12,modLfoToVolume:13,unused1:14,chorusEffectsSend:15,reverbEffectsSend:16,pan:17,unused2:18,unused3:19,unused4:20,delayModLFO:21,freqModLFO:22,delayVibLFO:23,freqVibLFO:24,delayModEnv:25,attackModEnv:26,holdModEnv:27,decayModEnv:28,sustainModEnv:29,releaseModEnv:30,keyNumToModEnvHold:31,keyNumToModEnvDecay:32,delayVolEnv:33,attackVolEnv:34,holdVolEnv:35,decayVolEnv:36,sustainVolEnv:37,releaseVolEnv:38,keyNumToVolEnvHold:39,keyNumToVolEnvDecay:40,instrument:41,reserved1:42,keyRange:43,velRange:44,startloopAddrsCoarseOffset:45,keyNum:46,velocity:47,initialAttenuation:48,reserved2:49,endloopAddrsCoarseOffset:50,coarseTune:51,fineTune:52,sampleID:53,sampleModes:54,reserved3:55,scaleTuning:56,exclusiveClass:57,overridingRootKey:58,unused5:59,endOper:60},X=[];X[i.startAddrsOffset]={min:0,max:32768,def:0};X[i.endAddrOffset]={min:-32768,max:32768,def:0};X[i.startloopAddrsOffset]={min:-32768,max:32768,def:0};X[i.endloopAddrsOffset]={min:-32768,max:32768,def:0};X[i.startAddrsCoarseOffset]={min:0,max:32768,def:0};X[i.modLfoToPitch]={min:-12e3,max:12e3,def:0};X[i.vibLfoToPitch]={min:-12e3,max:12e3,def:0};X[i.modEnvToPitch]={min:-12e3,max:12e3,def:0};X[i.initialFilterFc]={min:1500,max:13500,def:13500};X[i.initialFilterQ]={min:0,max:960,def:0};X[i.modLfoToFilterFc]={min:-12e3,max:12e3,def:0};X[i.modEnvToFilterFc]={min:-12e3,max:12e3,def:0};X[i.endAddrsCoarseOffset]={min:-32768,max:32768,def:0};X[i.modLfoToVolume]={min:-960,max:960,def:0};X[i.chorusEffectsSend]={min:0,max:1e3,def:0};X[i.reverbEffectsSend]={min:0,max:1e3,def:0};X[i.pan]={min:-500,max:500,def:0};X[i.delayModLFO]={min:-12e3,max:5e3,def:-12e3};X[i.freqModLFO]={min:-16e3,max:4500,def:0};X[i.delayVibLFO]={min:-12e3,max:5e3,def:-12e3};X[i.freqVibLFO]={min:-16e3,max:4500,def:0};X[i.delayModEnv]={min:-32768,max:5e3,def:-32768};X[i.attackModEnv]={min:-32768,max:8e3,def:-32768};X[i.holdModEnv]={min:-12e3,max:5e3,def:-12e3};X[i.decayModEnv]={min:-12e3,max:8e3,def:-12e3};X[i.sustainModEnv]={min:0,max:1e3,def:0};X[i.releaseModEnv]={min:-7200,max:8e3,def:-12e3};X[i.keyNumToModEnvHold]={min:-1200,max:1200,def:0};X[i.keyNumToModEnvDecay]={min:-1200,max:1200,def:0};X[i.delayVolEnv]={min:-12e3,max:5e3,def:-12e3};X[i.attackVolEnv]={min:-12e3,max:8e3,def:-12e3};X[i.holdVolEnv]={min:-12e3,max:5e3,def:-12e3};X[i.decayVolEnv]={min:-12e3,max:8e3,def:-12e3};X[i.sustainVolEnv]={min:0,max:1440,def:0};X[i.releaseVolEnv]={min:-7200,max:8e3,def:-12e3};X[i.keyNumToVolEnvHold]={min:-1200,max:1200,def:0};X[i.keyNumToVolEnvDecay]={min:-1200,max:1200,def:0};X[i.startloopAddrsCoarseOffset]={min:-32768,max:32768,def:0};X[i.keyNum]={min:-1,max:127,def:-1};X[i.velocity]={min:-1,max:127,def:-1};X[i.initialAttenuation]={min:-250,max:1440,def:0};X[i.endloopAddrsCoarseOffset]={min:-32768,max:32768,def:0};X[i.coarseTune]={min:-120,max:120,def:0};X[i.fineTune]={min:-12700,max:12700,def:0};X[i.scaleTuning]={min:0,max:1200,def:100};X[i.exclusiveClass]={min:0,max:99999,def:0};X[i.overridingRootKey]={min:-1,max:127,def:-1};X[i.sampleModes]={min:0,max:3,def:0};var U=class{generatorType=i.INVALID;generatorValue=0;constructor(A=i.INVALID,t=0,n=!0){if(this.generatorType=A,t===void 0)throw new Error("No value provided.");if(this.generatorValue=Math.round(t),n){let s=X[A];s!==void 0&&(this.generatorValue=Math.max(s.min,Math.min(s.max,this.generatorValue)))}}};function Os(e,A,t){let n=X[e]||{min:0,max:32768,def:0},s=A.find(g=>g.generatorType===e),o=0;s&&(o=s.generatorValue);let r=t.find(g=>g.generatorType===e),E=n.def;r&&(E=r.generatorValue);let a=E+o;return e===i.initialAttenuation?a:Math.max(n.min,Math.min(n.max,a))}var j={noController:0,noteOnVelocity:2,noteOnKeyNum:3,polyPressure:10,channelPressure:13,pitchWheel:14,pitchWheelRange:16,link:127},GA={linear:0,concave:1,convex:2,switch:3},_=class e{currentValue=0;sourceEnum;secondarySourceEnum;modulatorDestination;transformAmount;transformType;constructor(A,t,n,s,o){this.sourceEnum=A,this.modulatorDestination=n,this.secondarySourceEnum=t,this.transformAmount=s,this.transformType=o,this.modulatorDestination>58&&(this.modulatorDestination=i.INVALID),this.sourcePolarity=this.sourceEnum>>9&1,this.sourceDirection=this.sourceEnum>>8&1,this.sourceUsesCC=this.sourceEnum>>7&1,this.sourceIndex=this.sourceEnum&127,this.sourceCurveType=this.sourceEnum>>10&3,this.secSrcPolarity=this.secondarySourceEnum>>9&1,this.secSrcDirection=this.secondarySourceEnum>>8&1,this.secSrcUsesCC=this.secondarySourceEnum>>7&1,this.secSrcIndex=this.secondarySourceEnum&127,this.secSrcCurveType=this.secondarySourceEnum>>10&3,this.isEffectModulator=(this.sourceEnum===219||this.sourceEnum===221)&&this.secondarySourceEnum===0&&(this.modulatorDestination===i.reverbEffectsSend||this.modulatorDestination===i.chorusEffectsSend)}static copy(A){return new e(A.sourceEnum,A.secondarySourceEnum,A.modulatorDestination,A.transformAmount,A.transformType)}static isIdentical(A,t,n=!1){return A.sourceEnum===t.sourceEnum&&A.modulatorDestination===t.modulatorDestination&&A.secondarySourceEnum===t.secondarySourceEnum&&A.transformType===t.transformType&&(!n||A.transformAmount===t.transformAmount)}static debugString(A){function t(o,r){return Object.keys(o).find(E=>o[E]===r)}let n=t(GA,A.sourceCurveType);n+=A.sourcePolarity===0?" unipolar ":" bipolar ",n+=A.sourceDirection===0?"forwards ":"backwards ",A.sourceUsesCC?n+=t(y,A.sourceIndex):n+=t(j,A.sourceIndex);let s=t(GA,A.secSrcCurveType);return s+=A.secSrcPolarity===0?" unipolar ":" bipolar ",s+=A.secSrcCurveType===0?"forwards ":"backwards ",A.secSrcUsesCC?s+=t(y,A.secSrcIndex):s+=t(j,A.secSrcIndex),`Modulator:
        Source: ${n}
        Secondary source: ${s}
        Destination: ${t(i,A.modulatorDestination)}
        Trasform amount: ${A.transformAmount}
        Transform type: ${A.transformType}
        

`}sumTransform(A){return new e(this.sourceEnum,this.secondarySourceEnum,this.modulatorDestination,this.transformAmount+A.transformAmount,this.transformType)}},Jn=960,Kn=GA.concave;function oe(e,A,t,n,s){return e<<10|A<<9|t<<8|n<<7|s}var Ci=[new _(oe(Kn,0,1,0,j.noteOnVelocity),0,i.initialAttenuation,Jn,0),new _(129,0,i.vibLfoToPitch,50,0),new _(oe(Kn,0,1,1,y.mainVolume),0,i.initialAttenuation,Jn,0),new _(13,0,i.vibLfoToPitch,50,0),new _(526,16,i.fineTune,12700,0),new _(650,0,i.pan,500,0),new _(oe(Kn,0,1,1,y.expressionController),0,i.initialAttenuation,Jn,0),new _(219,0,i.reverbEffectsSend,200,0),new _(221,0,i.chorusEffectsSend,200,0)],Ei=[new _(oe(GA.linear,0,0,0,j.polyPressure),0,i.vibLfoToPitch,50,0),new _(oe(GA.linear,0,0,1,y.tremoloDepth),0,i.modLfoToVolume,24,0),new _(oe(GA.convex,1,0,1,y.attackTime),0,i.attackVolEnv,6e3,0),new _(oe(GA.linear,1,0,1,y.releaseTime),0,i.releaseVolEnv,3600,0),new _(oe(GA.linear,1,0,1,y.brightness),0,i.initialFilterFc,6e3,0),new _(oe(GA.linear,1,0,1,y.filterResonance),0,i.initialFilterQ,250,0)],zt=Ci.concat(Ei);var MA=128,jt=147,be=new Int16Array(jt).fill(0),xA=(e,A)=>be[e]=A<<7;xA(y.mainVolume,100);xA(y.balance,64);xA(y.expressionController,127);xA(y.pan,64);xA(y.portamentoOnOff,127);xA(y.filterResonance,64);xA(y.releaseTime,64);xA(y.attackTime,64);xA(y.brightness,64);xA(y.decayTime,64);xA(y.vibratoRate,64);xA(y.vibratoDepth,64);xA(y.vibratoDelay,64);xA(y.generalPurposeController6,64);xA(y.generalPurposeController8,64);xA(y.RPNLsb,127);xA(y.RPNMsb,127);xA(y.NRPNLsb,127);xA(y.NRPNMsb,127);var $t=1;be[y.portamentoControl]=$t;xA(MA+j.pitchWheel,64);xA(MA+j.pitchWheelRange,2);var cA={channelTuning:0,channelTransposeFine:1,modulationMultiplier:2,masterTuning:3,channelTuningSemitones:4},qn=Object.keys(cA).length,On=new Float32Array(qn);On[cA.modulationMultiplier]=1;var YA={Idle:0,RPCoarse:1,RPFine:2,NRPCoarse:3,NRPFine:4,DataCoarse:5,DataFine:6},Ps={velocityOverride:128};var Vs="spessasynth-worklet-system";var An="gs";function It(e){return e.messageData[0]===67&&e.messageData[2]===76&&e.messageData[5]===126&&e.messageData[6]===0}function en(e){return e.messageData[0]===65&&e.messageData[2]===66&&e.messageData[3]===18&&e.messageData[4]===64&&(e.messageData[5]&16)!==0&&e.messageData[6]===21}function tn(e){return e.messageData[0]===65&&e.messageData[2]===66&&e.messageData[6]===127}function nn(e){return e.messageData[0]===126&&e.messageData[2]===9&&e.messageData[3]===1}function sn(e){return e.messageData[0]===126&&e.messageData[2]===9&&e.messageData[3]===3}var rn=64,Zs=121;function Xs(e){return e==="gm2"?Zs:0}function re(e){return e===120||e===126||e===127}function on(e){return re(e)||e===rn||e===Zs}function gt(e,A,t,n,s,o){let r=e,E=0;if(n)RA(t)?on(A)||(r=A):t==="gm2"&&(r=A);else{let a=!0;switch(t){case"gm":p(`%cIgnoring the Bank Select (${A}), as the synth is in GM mode.`,I.info),a=!1;break;case"xg":a=on(A),re(A)?E=2:o%16!==9&&(E=1);break;case"gm2":A===120?E=2:o%16!==9&&(E=1)}s&&(A=128),A===128&&!s&&(A=e),a&&(r=A)}return{newBank:r,drumsStatus:E}}function Ct(e,A,t,n){return n?t?re(e)?e:128:on(e)||A===0&&e!==0?e:on(A)?0:A:t?128:e}function RA(e){return e==="gm2"||e==="xg"}function Pn(e){return new ne(e,F.systemExclusive,new L([65,16,66,18,64,0,127,0,65,247]))}function Et(e,A,t,n){return new ne(n,F.controllerChange|e%16,new L([A,t]))}function Bi(e,A){let t=16|[1,2,3,4,5,6,7,8,0,9,10,11,12,13,14,15][e%16],n=[65,16,66,18,64,t,21,1],o=128-(64+t+21+1)%128;return new ne(A,F.systemExclusive,new L([...n,o,247]))}function Ws(e=[],A=[],t=[],n=[]){let s=this;FA("%cApplying changes to the MIDI file...",I.info),p("Desired program changes:",e),p("Desired CC changes:",A),p("Desired channels to clear:",t),p("Desired channels to transpose:",n);let o=new Set;e.forEach(x=>{o.add(x.channel)});let r="gs",E=!1,a=Array(s.tracks.length).fill(0),g=s.tracks.length;function d(){let x=0,b=1/0;return s.tracks.forEach((G,C)=>{a[C]>=G.length||G[a[C]].ticks<b&&(x=C,b=G[a[C]].ticks)}),x}let B=s.midiPorts.slice(),l={},h=0;function m(x,b){s.usedChannelsOnTrack[x].size!==0&&(h===0&&(h+=16,l[b]=0),l[b]===void 0&&(l[b]=h,h+=16),B[x]=b)}s.midiPorts.forEach((x,b)=>{m(b,x)});let u=h,S=Array(u).fill(!0),w=Array(u).fill(0),k=Array(u).fill(0);for(n.forEach(x=>{let b=Math.trunc(x.keyShift),G=x.keyShift-b;w[x.channel]=b,k[x.channel]=G});g>0;){let x=d(),b=s.tracks[x];if(a[x]>=b.length){g--;continue}let G=a[x]++,C=b[G],M=()=>{b.splice(G,1),a[x]--},q=(BA,sA=0)=>{b.splice(G+sA,0,BA),a[x]++},gA=l[B[x]]||0;if(C.messageStatusByte===F.midiPort){m(x,C.messageData[0]);continue}if(C.messageStatusByte<=F.sequenceSpecific&&C.messageStatusByte>=F.sequenceNumber)continue;let tA=C.messageStatusByte&240,T=C.messageStatusByte&15,$=T+gA;if(t.indexOf($)!==-1){M();continue}switch(tA){case F.noteOn:if(S[$]){S[$]=!1,A.filter(EA=>EA.channel===$).forEach(EA=>{let v=Et(T,EA.controllerNumber,EA.controllerValue,C.ticks);q(v)});let oA=k[$];if(oA!==0){let EA=oA*64+64,v=Et(T,y.RPNMsb,0,C.ticks),J=Et(T,y.RPNLsb,1,C.ticks),W=Et($,y.dataEntryMsb,EA,C.ticks),O=Et(T,y.lsbForControl6DataEntry,0,C.ticks);q(O),q(W),q(J),q(v)}if(o.has($)){let EA=e.find(rA=>rA.channel===$),v=Math.max(0,Math.min(EA.bank,127)),J=EA.program;p(`%cSetting %c${EA.channel}%c to %c${v}:${J}%c. Track num: %c${x}`,I.info,I.recognized,I.info,I.recognized,I.info,I.recognized);let W=new ne(C.ticks,F.programChange|T,new L([J]));q(W);let O=(rA,KA)=>{let Ie=Et(T,rA?y.lsbForControl0BankSelect:y.bankSelect,KA,C.ticks);q(Ie)};RA(r)?EA.isDrum?(p(`%cAdding XG Drum change on track %c${x}`,I.recognized,I.value),O(!1,re(v)?v:127),O(!0,0)):v===rn?(O(!1,rn),O(!0,0)):(O(!1,0),O(!0,v)):(O(!1,v),EA.isDrum&&T!==9&&(p(`%cAdding GS Drum change on track %c${x}`,I.recognized,I.value),q(Bi(T,C.ticks))))}}C.messageData[0]+=w[$];break;case F.noteOff:C.messageData[0]+=w[$];break;case F.programChange:if(o.has($)){M();continue}break;case F.controllerChange:let BA=C.messageData[0];if(A.find(oA=>oA.channel===$&&BA===oA.controllerNumber)!==void 0){M();continue}if((BA===y.bankSelect||BA===y.lsbForControl0BankSelect)&&o.has($)){M();continue}break;case F.systemExclusive:if(It(C))p("%cXG system on detected",I.info),r="xg",E=!0;else if(C.messageData[0]===67&&C.messageData[2]===76&&C.messageData[3]===8&&C.messageData[5]===3)o.has(C.messageData[4]+gA)&&M();else if(tn(C)){E=!0,p("%cGS on detected!",I.recognized);break}else(nn(C)||sn(C))&&(p("%cGM/2 on detected, removing!",I.info),M(),E=!1)}}if(!E&&e.length>0){let x=0;s.tracks[0][0].messageStatusByte===F.trackName&&x++,s.tracks[0].splice(x,0,Pn(0)),p("%cGS on not detected. Adding it.",I.info)}V()}function _s(e){let A=[],t=[],n=[],s=[];e.channelSnapshots.forEach((o,r)=>{if(o.isMuted){t.push(r);return}let E=o.channelTransposeKeyShift+o.customControllers[cA.channelTransposeFine]/100;E!==0&&A.push({channel:r,keyShift:E}),o.lockPreset&&n.push({channel:r,program:o.program,bank:o.bank,isDrum:o.drumChannel}),o.lockedControllers.forEach((a,g)=>{if(!a||g>127||g===y.bankSelect)return;let d=o.midiControllers[g]>>7;s.push({channel:r,controllerNumber:g,controllerValue:d})})}),this.modifyMIDI(n,s,t,A)}var fA={name:"INAM",album:"IPRD",album2:"IALB",artist:"IART",genre:"IGNR",picture:"IPIC",copyright:"ICOP",creationDate:"ICRD",comment:"ICMT",engineer:"IENG",software:"ISFT",encoding:"IENC",midiEncoding:"MENC",bankOffset:"DBNK"},Le="utf-8",hi="Created using SpessaSynth";function zs(e,A,t=0,n="Shift_JIS",s={},o=!0){let r=this;if(VA("%cWriting the RMIDI File...",I.info),p(`%cConfiguration: Bank offset: %c${t}%c, encoding: %c${n}`,I.info,I.value,I.info,I.value),p("metadata",s),p("Initial bank offset",r.bankOffset),o){let w=function(){let G=0,C=1/0;return r.tracks.forEach((M,q)=>{u[q]>=M.length||M[u[q]].ticks<C&&(G=q,C=M[u[q]].ticks)}),G},h="gm",m=[],u=Array(r.tracks.length).fill(0),S=r.tracks.length,k=Array(r.tracks.length).fill(0),x=16+r.midiPortChannelOffsets.reduce((G,C)=>C>G?C:G),b=[];for(let G=0;G<x;G++)b.push({program:0,drums:G%16===9,lastBank:void 0,lastBankLSB:void 0,hasBankSelect:!1});for(;S>0;){let G=w(),C=r.tracks[G];if(u[G]>=C.length){S--;continue}let M=C[u[G]];u[G]++;let q=r.midiPortChannelOffsets[k[G]];if(M.messageStatusByte===F.midiPort){k[G]=M.messageData[0];continue}let gA=M.messageStatusByte&240;if(gA!==F.controllerChange&&gA!==F.programChange&&gA!==F.systemExclusive)continue;if(gA===F.systemExclusive){if(!en(M)){It(M)?h="xg":tn(M)?h="gs":nn(M)?(h="gm",m.push({tNum:G,e:M})):sn(M)&&(h="gm2");continue}let oA=[9,0,1,2,3,4,5,6,7,8,10,11,12,13,14,15][M.messageData[5]&15]+q;b[oA].drums=!!(M.messageData[7]>0&&M.messageData[5]>>4);continue}let tA=(M.messageStatusByte&15)+q,T=b[tA];if(gA===F.programChange){let oA=RA(h),EA=M.messageData[0];T.drums?A.presets.findIndex(O=>O.program===EA&&O.isDrumPreset(oA,!0))===-1&&(M.messageData[0]=A.presets.find(O=>O.isDrumPreset(oA))?.program||0,p(`%cNo drum preset %c${EA}%c. Channel %c${tA}%c. Changing program to ${M.messageData[0]}.`,I.info,I.unrecognized,I.info,I.recognized,I.info)):A.presets.findIndex(O=>O.program===EA&&!O.isDrumPreset(oA))===-1&&(M.messageData[0]=A.presets.find(O=>!O.isDrumPreset(oA))?.program||0,p(`%cNo preset %c${EA}%c. Channel %c${tA}%c. Changing program to ${M.messageData[0]}.`,I.info,I.unrecognized,I.info,I.recognized,I.info)),T.program=M.messageData[0];let v=Math.max(0,T.lastBank?.messageData[1]-r.bankOffset),J=T?.lastBankLSB?.messageData[1]-r.bankOffset||0;if(T.lastBank===void 0)continue;let W=Ct(v,J,T.drums,oA);if(A.presets.findIndex(O=>O.bank===W&&O.program===M.messageData[0])===-1){let O=A.presets.find(rA=>rA.program===M.messageData[0])?.bank+t||t;T.lastBank.messageData[1]=O,T?.lastBankLSB?.messageData&&(T.lastBankLSB.messageData[1]=O),p(`%cNo preset %c${W}:${M.messageData[0]}%c. Channel %c${tA}%c. Changing bank to ${O}.`,I.info,I.unrecognized,I.info,I.recognized,I.info)}else{let O=W;RA(h)&&W===128&&(W=127);let rA=(W===128?128:O)+t;T.lastBank.messageData[1]=rA,T?.lastBankLSB?.messageData&&!T.drums&&(T.lastBankLSB.messageData[1]=T.lastBankLSB.messageData[1]-r.bankOffset+t),p(`%cPreset %c${W}:${M.messageData[0]}%c exists. Channel %c${tA}%c.  Changing bank to ${rA}.`,I.info,I.recognized,I.info,I.recognized,I.info)}continue}let $=M.messageData[0]===y.lsbForControl0BankSelect;if(M.messageData[0]!==y.bankSelect&&!$)continue;T.hasBankSelect=!0;let BA=M.messageData[1],sA=gt(T?.lastBank?.messageData[1]||0,BA,h,$,T.drums,tA);sA.drumsStatus===2?T.drums=!0:sA.drumsStatus===1&&(T.drums=!1),$?T.lastBankLSB=M:T.lastBank=M}if(b.forEach((G,C)=>{if(G.hasBankSelect===!0)return;let M=C%16,q=F.programChange|M,gA=Math.floor(C/16)*16,tA=r.midiPortChannelOffsets.indexOf(gA),T=r.tracks.find((oA,EA)=>r.midiPorts[EA]===tA&&r.usedChannelsOnTrack[EA].has(M));if(T===void 0)return;let $=T.findIndex(oA=>oA.messageStatusByte===q);if($===-1){let oA=T.findIndex(J=>J.messageStatusByte>128&&J.messageStatusByte<240&&(J.messageStatusByte&15)===M);if(oA===-1)return;let EA=T[oA].ticks,v=A.getPreset(0,0).program;T.splice(oA,0,new ne(EA,F.programChange|M,new L([v]))),$=oA}p(`%cAdding bank select for %c${C}`,I.info,I.recognized);let BA=T[$].ticks,sA=A.getPreset(0,G.program,RA(h))?.bank+t||t;T.splice($,0,new ne(BA,F.controllerChange|M,new L([y.bankSelect,sA])))}),h!=="gs"&&!RA(h)){for(let C of m)r.tracks[C.tNum].splice(r.tracks[C.tNum].indexOf(C.e),1);let G=0;r.tracks[0][0].messageStatusByte===F.trackName&&G++,r.tracks[0].splice(G,0,Pn(0))}}let E=new L(r.writeMIDI().buffer),a=[at("INFO")],g=new TextEncoder;if(a.push(z(fA.software,g.encode("SpessaSynth"),!0)),s.name!==void 0?(a.push(z(fA.name,g.encode(s.name),!0)),n=Le):a.push(z(fA.name,r.rawMidiName,!0)),s.creationDate!==void 0)n=Le,a.push(z(fA.creationDate,g.encode(s.creationDate),!0));else{let h=new Date().toLocaleString(void 0,{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric"});a.push(z(fA.creationDate,fe(h),!0))}if(s.comment!==void 0&&(n=Le,a.push(z(fA.comment,g.encode(s.comment)))),s.engineer!==void 0&&a.push(z(fA.engineer,g.encode(s.engineer),!0)),s.album!==void 0&&(n=Le,a.push(z(fA.album,g.encode(s.album),!0)),a.push(z(fA.album2,g.encode(s.album),!0))),s.artist!==void 0&&(n=Le,a.push(z(fA.artist,g.encode(s.artist),!0))),s.genre!==void 0&&(n=Le,a.push(z(fA.genre,g.encode(s.genre),!0))),s.picture!==void 0&&a.push(z(fA.picture,new Uint8Array(s.picture))),s.copyright!==void 0)n=Le,a.push(z(fA.copyright,g.encode(s.copyright),!0));else{let h=r.copyright.length>0?r.copyright:hi;a.push(z(fA.copyright,fe(h)))}let d=new L(2);Xe(d,t,2),a.push(z(fA.bankOffset,d)),s.midiEncoding!==void 0&&(a.push(z(fA.midiEncoding,g.encode(s.midiEncoding))),n=Le),a.push(z(fA.encoding,fe(n)));let B=wA(a),l=wA([at("RMID"),z("data",E),z("LIST",B),e]);return p("%cFinished!",I.info),V(),z("RIFF",l)}function js(){let e=this;if(!e.tracks)throw new Error("MIDI has no tracks!");let A=[];for(let s of e.tracks){let o=[],r=0,E;for(let a of s){let g=a.ticks-r,d;a.messageStatusByte<=F.sequenceSpecific?d=[255,a.messageStatusByte,..._t(a.messageData.length),...a.messageData]:a.messageStatusByte===F.systemExclusive?d=[240,..._t(a.messageData.length),...a.messageData]:(d=[],E!==a.messageStatusByte&&(E=a.messageStatusByte,d.push(a.messageStatusByte)),d.push(...a.messageData)),o.push(..._t(g)),o.push(...d),r+=g}A.push(new Uint8Array(o))}function t(s,o){for(let r=0;r<s.length;r++)o.push(s.charCodeAt(r))}let n=[];t("MThd",n),n.push(...Ft(6,4)),n.push(0,e.format),n.push(...Ft(e.tracksAmount,2)),n.push(...Ft(e.timeDivision,2));for(let s of A)t("MTrk",n),n.push(...Ft(s.length,4)),n.push(...s);return new Uint8Array(n)}function $s(e){let A=this;FA("%cSearching for all used programs and keys...",I.info);let t=16+A.midiPortChannelOffsets.reduce((B,l)=>l>B?l:B),n=[];for(let B=0;B<t;B++){let l=B%16===9?128:0;n.push({program:0,bank:l,bankLSB:0,actualBank:l,drums:B%16===9,string:`${l}:0`})}let s="gs";function o(B){let l=Ct(B.bank,B.bankLSB,B.drums,RA(s)),h=e.getPreset(l,B.program,RA(s));B.actualBank=h.bank,B.program=h.program,B.string=B.actualBank+":"+B.program,r[B.string]||(p(`%cDetected a new preset: %c${B.string}`,I.info,I.recognized),r[B.string]=new Set)}let r={},E=Array(A.tracks.length).fill(0),a=A.tracks.length;function g(){let B=0,l=1/0;return A.tracks.forEach((h,m)=>{E[m]>=h.length||h[E[m]].ticks<l&&(B=m,l=h[E[m]].ticks)}),B}let d=A.midiPorts.slice();for(n.forEach(B=>{o(B)});a>0;){let B=g(),l=A.tracks[B];if(E[B]>=l.length){a--;continue}let h=l[E[B]];if(E[B]++,h.messageStatusByte===F.midiPort){d[B]=h.messageData[0];continue}let m=h.messageStatusByte&240;if(m!==F.noteOn&&m!==F.controllerChange&&m!==F.programChange&&m!==F.systemExclusive)continue;let u=(h.messageStatusByte&15)+A.midiPortChannelOffsets[d[B]]||0,S=n[u];switch(m){case F.programChange:S.program=h.messageData[0],o(S);break;case F.controllerChange:let w=h.messageData[0]===y.lsbForControl0BankSelect;if(h.messageData[0]!==y.bankSelect&&!w||s==="gs"&&S.drums)continue;let k=h.messageData[1],x=Math.max(0,k-A.bankOffset);switch(w?S.bankLSB=x:S.bank=x,gt(S.bank,x,s,w,S.drums,u).drumsStatus){case 0:break;case 1:S.drums=!1,o(S);break;case 2:S.drums=!0,o(S);break}break;case F.noteOn:if(h.messageData[1]===0)continue;r[S.string].add(`${h.messageData[0]}-${h.messageData[1]}`);break;case F.systemExclusive:if(!en(h)){It(h)&&(s="xg",p("%cXG on detected!",I.recognized));continue}let G=[9,0,1,2,3,4,5,6,7,8,10,11,12,13,14,15][h.messageData[5]&15]+A.midiPortChannelOffsets[d[B]],C=!!(h.messageData[7]>0&&h.messageData[5]>>4);S=n[G],S.drums=C,o(S);break}}for(let B of Object.keys(r))r[B].size===0&&(p(`%cDetected change but no keys for %c${B}`,I.info,I.value),delete r[B]);return V(),r}var ge=class e extends it{embeddedSoundFont=void 0;tracks=[];isDLSRMIDI=!1;static copyFrom(A){let t=new e;return t.midiName=A.midiName,t.midiNameUsesFileName=A.midiNameUsesFileName,t.fileName=A.fileName,t.timeDivision=A.timeDivision,t.duration=A.duration,t.copyright=A.copyright,t.tracksAmount=A.tracksAmount,t.firstNoteOn=A.firstNoteOn,t.keyRange={...A.keyRange},t.lastVoiceEventTick=A.lastVoiceEventTick,t.loop={...A.loop},t.format=A.format,t.bankOffset=A.bankOffset,t.isKaraokeFile=A.isKaraokeFile,t.isMultiPort=A.isMultiPort,t.isDLSRMIDI=A.isDLSRMIDI,t.tempoChanges=[...A.tempoChanges],t.lyrics=A.lyrics.map(n=>new Uint8Array(n)),t.lyricsTicks=[...A.lyricsTicks],t.midiPorts=[...A.midiPorts],t.midiPortChannelOffsets=[...A.midiPortChannelOffsets],t.usedChannelsOnTrack=A.usedChannelsOnTrack.map(n=>new Set(n)),t.rawMidiName=A.rawMidiName?new Uint8Array(A.rawMidiName):void 0,t.embeddedSoundFont=A.embeddedSoundFont?A.embeddedSoundFont.slice(0):void 0,t.RMIDInfo={...A.RMIDInfo},t.tracks=A.tracks.map(n=>[...n]),t}_parseInternal(){VA("%cInterpreting MIDI events...",I.info);let A=!1;this.keyRange={max:0,min:127};let t=[],n=!1;typeof this.RMIDInfo.ICOP<"u"&&(n=!0);let s=!1;typeof this.RMIDInfo.INAM<"u"&&(s=!0);let o=null,r=null;for(let B=0;B<this.tracks.length;B++){let l=this.tracks[B],h=new Set,m=!1;for(let u of l){if(u.messageStatusByte>=128&&u.messageStatusByte<240){m=!0;for(let w=0;w<u.messageData.length;w++)u.messageData[w]=Math.min(127,u.messageData[w]);switch(u.ticks>this.lastVoiceEventTick&&(this.lastVoiceEventTick=u.ticks),u.messageStatusByte&240){case F.controllerChange:switch(u.messageData[0]){case 2:case 116:o=u.ticks;break;case 4:case 117:r===null?r=u.ticks:r=0;break;case 0:this.isDLSRMIDI&&u.messageData[1]!==0&&u.messageData[1]!==127&&(p("%cDLS RMIDI with offset 1 detected!",I.recognized),this.bankOffset=1)}break;case F.noteOn:h.add(u.messageStatusByte&15);let w=u.messageData[0];this.keyRange.min=Math.min(this.keyRange.min,w),this.keyRange.max=Math.max(this.keyRange.max,w);break}}u.messageData.currentIndex=0;let S=eA(u.messageData,u.messageData.length);switch(u.messageData.currentIndex=0,u.messageStatusByte){case F.setTempo:u.messageData.currentIndex=0,this.tempoChanges.push({ticks:u.ticks,tempo:6e7/se(u.messageData,3)}),u.messageData.currentIndex=0;break;case F.marker:switch(S.trim().toLowerCase()){default:break;case"start":case"loopstart":o=u.ticks;break;case"loopend":r=u.ticks}u.messageData.currentIndex=0;break;case F.copyright:n||(u.messageData.currentIndex=0,t.push(eA(u.messageData,u.messageData.length,void 0,!1)),u.messageData.currentIndex=0);break;case F.lyric:if(S.trim().startsWith("@KMIDI KARAOKE FILE")&&(this.isKaraokeFile=!0,p("%cKaraoke MIDI detected!",I.recognized)),this.isKaraokeFile)u.messageStatusByte=F.text;else{this.lyrics.push(u.messageData),this.lyricsTicks.push(u.ticks);break}case F.text:let k=S.trim();k.startsWith("@KMIDI KARAOKE FILE")?(this.isKaraokeFile=!0,p("%cKaraoke MIDI detected!",I.recognized)):this.isKaraokeFile&&(k.startsWith("@T")||k.startsWith("@A")?A?t.push(k.substring(2).trim()):(this.midiName=k.substring(2).trim(),A=!0,s=!0,this.rawMidiName=at(this.midiName)):k[0]!=="@"&&(this.lyrics.push(Ms(u.messageData)),this.lyricsTicks.push(u.ticks)));break}}if(this.usedChannelsOnTrack.push(h),!m){let u=l.find(S=>S.messageStatusByte===F.trackName);if(u){u.messageData.currentIndex=0;let S=eA(u.messageData,u.messageData.length);t.push(S)}}}this.tempoChanges.reverse(),p("%cCorrecting loops, ports and detecting notes...",I.info);let E=[];for(let B of this.tracks){let l=B.find(h=>(h.messageStatusByte&240)===F.noteOn);l&&E.push(l.ticks)}this.firstNoteOn=Math.min(...E),p(`%cFirst note-on detected at: %c${this.firstNoteOn}%c ticks!`,I.info,I.recognized,I.info),o!==null&&r===null?(o=this.firstNoteOn,r=this.lastVoiceEventTick):(o===null&&(o=this.firstNoteOn),(r===null||r===0)&&(r=this.lastVoiceEventTick)),this.loop={start:o,end:r},p(`%cLoop points: start: %c${this.loop.start}%c end: %c${this.loop.end}`,I.info,I.recognized,I.info,I.recognized);let a=0;this.midiPorts=[],this.midiPortChannelOffsets=[];for(let B=0;B<this.tracks.length;B++)if(this.midiPorts.push(-1),this.usedChannelsOnTrack[B].size!==0)for(let l of this.tracks[B]){if(l.messageStatusByte!==F.midiPort)continue;let h=l.messageData[0];this.midiPorts[B]=h,this.midiPortChannelOffsets[h]===void 0&&(this.midiPortChannelOffsets[h]=a,a+=16)}let g=1/0;for(let B of this.midiPorts)B!==-1&&g>B&&(g=B);if(g===1/0&&(g=0),this.midiPorts=this.midiPorts.map(B=>B===-1?g:B),this.midiPortChannelOffsets.length===0&&(this.midiPortChannelOffsets=[0]),this.midiPortChannelOffsets.length<2?p("%cNo additional MIDI Ports detected.",I.info):(this.isMultiPort=!0,p("%cMIDI Ports detected!",I.recognized)),!s)if(this.tracks.length>1){if(this.tracks[0].find(B=>B.messageStatusByte>=F.noteOn&&B.messageStatusByte<F.polyPressure)===void 0){let B=this.tracks[0].find(l=>l.messageStatusByte===F.trackName);B&&(this.rawMidiName=B.messageData,B.messageData.currentIndex=0,this.midiName=eA(B.messageData,B.messageData.length,void 0,!1))}}else{let B=this.tracks[0].find(l=>l.messageStatusByte===F.trackName);B&&(this.rawMidiName=B.messageData,B.messageData.currentIndex=0,this.midiName=eA(B.messageData,B.messageData.length,void 0,!1))}if(n||(this.copyright=t.map(B=>B.trim().replace(/(\r?\n)+/g,`
`)).filter(B=>B.length>0).join(`
`)||""),this.midiName=this.midiName.trim(),this.midiNameUsesFileName=!1,this.midiName.length===0){p("%cNo name detected. Using the alt name!",I.info),this.midiName=Gs(this.fileName),this.midiNameUsesFileName=!0,this.rawMidiName=new Uint8Array(this.midiName.length);for(let B=0;B<this.midiName.length;B++)this.rawMidiName[B]=this.midiName.charCodeAt(B)}else p(`%cMIDI Name detected! %c"${this.midiName}"`,I.info,I.recognized);let d=!0;for(let B of this.lyrics)if(B[0]===32||B[B.length-1]===32){d=!1;break}d&&(this.lyrics=this.lyrics.map(B=>{if(B[B.length-1]===45)return B;let l=new Uint8Array(B.length+1);return l.set(B,0),l[B.length]=32,l})),this.duration=this.MIDIticksToSeconds(this.lastVoiceEventTick),p("%cSuccess!",I.recognized),V()}flush(){for(let A of this.tracks)A.sort((t,n)=>t.ticks-n.ticks);this._parseInternal()}};ge.prototype.writeMIDI=js;ge.prototype.modifyMIDI=Ws;ge.prototype.applySnapshotToMIDI=_s;ge.prototype.writeRMIDI=zs;ge.prototype.getUsedProgramsAndKeys=$s;var Vn;(()=>{var e=Uint8Array,A=Uint16Array,t=Int32Array,n=new e([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),s=new e([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),o=new e([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),r=function(v,J){for(var W=new A(31),O=0;O<31;++O)W[O]=J+=1<<v[O-1];for(var rA=new t(W[30]),O=1;O<30;++O)for(var KA=W[O];KA<W[O+1];++KA)rA[KA]=KA-W[O]<<5|O;return{b:W,r:rA}},E=r(n,2),a=E.b,g=E.r;a[28]=258,g[258]=28;var d=r(s,0),B=d.b,l=d.r,h=new A(32768);for(k=0;k<32768;++k)m=(k&43690)>>1|(k&21845)<<1,m=(m&52428)>>2|(m&13107)<<2,m=(m&61680)>>4|(m&3855)<<4,h[k]=((m&65280)>>8|(m&255)<<8)>>1;var m,k,u=function(v,J,W){for(var O=v.length,rA=0,KA=new A(J);rA<O;++rA)v[rA]&&++KA[v[rA]-1];var Ie=new A(J);for(rA=1;rA<J;++rA)Ie[rA]=Ie[rA-1]+KA[rA-1]<<1;var ce;if(W){ce=new A(1<<J);var le=15-J;for(rA=0;rA<O;++rA)if(v[rA])for(var $e=rA<<4|v[rA],pe=J-v[rA],aA=Ie[v[rA]-1]++<<pe,QA=aA|(1<<pe)-1;aA<=QA;++aA)ce[h[aA]>>le]=$e}else for(ce=new A(O),rA=0;rA<O;++rA)v[rA]&&(ce[rA]=h[Ie[v[rA]-1]++]>>15-v[rA]);return ce},S=new e(288);for(k=0;k<144;++k)S[k]=8;var k;for(k=144;k<256;++k)S[k]=9;var k;for(k=256;k<280;++k)S[k]=7;var k;for(k=280;k<288;++k)S[k]=8;var k,w=new e(32);for(k=0;k<32;++k)w[k]=5;var k,x=u(S,9,1),b=u(w,5,1),G=function(v){for(var J=v[0],W=1;W<v.length;++W)v[W]>J&&(J=v[W]);return J},C=function(v,J,W){var O=J/8|0;return(v[O]|v[O+1]<<8)>>(J&7)&W},M=function(v,J){var W=J/8|0;return(v[W]|v[W+1]<<8|v[W+2]<<16)>>(J&7)},q=function(v){return(v+7)/8|0},gA=function(v,J,W){return(J==null||J<0)&&(J=0),(W==null||W>v.length)&&(W=v.length),new e(v.subarray(J,W))},tA=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],T=function(v,J,W){var O=new Error(J||tA[v]);if(O.code=v,Error.captureStackTrace&&Error.captureStackTrace(O,T),!W)throw O;return O},$=function(v,J,W,O){var rA=v.length,KA=O?O.length:0;if(!rA||J.f&&!J.l)return W||new e(0);var Ie=!W,ce=Ie||J.i!=2,le=J.i;Ie&&(W=new e(rA*3));var $e=function(Kt){var xe=W.length;if(Kt>xe){var pt=new e(Math.max(xe*2,Kt));pt.set(W),W=pt}},pe=J.f||0,aA=J.p||0,QA=J.b||0,Qe=J.l,XA=J.d,Re=J.m,Ge=J.n,Qt=rA*8;do{if(!Qe){pe=C(v,aA,1);var At=C(v,aA+1,3);if(aA+=3,At)if(At==1)Qe=x,XA=b,Re=9,Ge=5;else if(At==2){var dt=C(v,aA,31)+257,wn=C(v,aA+10,15)+4,Fn=dt+C(v,aA+5,31)+1;aA+=14;for(var et=new e(Fn),Me=new e(19),WA=0;WA<wn;++WA)Me[o[WA]]=C(v,aA+WA*3,7);aA+=wn*3;for(var Rn=G(Me),vt=(1<<Rn)-1,tt=u(Me,Rn,1),WA=0;WA<Fn;){var ut=tt[C(v,aA,vt)];aA+=ut&15;var qA=ut>>4;if(qA<16)et[WA++]=qA;else{var ye=0,ft=0;for(qA==16?(ft=3+C(v,aA,3),aA+=2,ye=et[WA-1]):qA==17?(ft=3+C(v,aA,7),aA+=3):qA==18&&(ft=11+C(v,aA,127),aA+=7);ft--;)et[WA++]=ye}}var Gn=et.subarray(0,dt),Se=et.subarray(dt);Re=G(Gn),Ge=G(Se),Qe=u(Gn,Re,1),XA=u(Se,Ge,1)}else T(1);else{var qA=q(aA)+4,Ht=v[qA-4]|v[qA-3]<<8,Yt=qA+Ht;if(Yt>rA){le&&T(0);break}ce&&$e(QA+Ht),W.set(v.subarray(qA,Yt),QA),J.b=QA+=Ht,J.p=aA=Yt*8,J.f=pe;continue}if(aA>Qt){le&&T(0);break}}ce&&$e(QA+131072);for(var us=(1<<Re)-1,fs=(1<<Ge)-1,Jt=aA;;Jt=aA){var ye=Qe[M(v,aA)&us],Oe=ye>>4;if(aA+=ye&15,aA>Qt){le&&T(0);break}if(ye||T(2),Oe<256)W[QA++]=Oe;else if(Oe==256){Jt=aA,Qe=null;break}else{var Mn=Oe-254;if(Oe>264){var WA=Oe-257,De=n[WA];Mn=C(v,aA,(1<<De)-1)+a[WA],aA+=De}var nt=XA[M(v,aA)&fs],Pe=nt>>4;nt||T(3),aA+=nt&15;var Se=B[Pe];if(Pe>3){var De=s[Pe];Se+=M(v,aA)&(1<<De)-1,aA+=De}if(aA>Qt){le&&T(0);break}ce&&$e(QA+131072);var mt=QA+Mn;if(QA<Se){var st=KA-Se,NA=Math.min(Se,mt);for(st+QA<0&&T(3);QA<NA;++QA)W[QA]=O[st+QA]}for(;QA<mt;++QA)W[QA]=W[QA-Se]}}J.l=Qe,J.p=Jt,J.b=QA,J.f=pe,Qe&&(pe=1,J.m=Re,J.d=XA,J.n=Ge)}while(!pe);return QA!=W.length&&Ie?gA(W,0,QA):W.subarray(0,QA)},BA=new e(0);function sA(v,J){return $(v,{i:2},J&&J.out,J&&J.dictionary)}var oA=typeof TextDecoder<"u"&&new TextDecoder,EA=0;try{oA.decode(BA,{stream:!0}),EA=1}catch{}Vn=sA})();var Zn={XMFFileType:0,nodeName:1,nodeIDNumber:2,resourceFormat:3,filenameOnDisk:4,filenameExtensionOnDisk:5,macOSFileTypeAndCreator:6,mimeType:7,title:8,copyrightNotice:9,comment:10,autoStart:11,preload:12,contentDescription:13,ID3Metadata:14},Bt={inLineResource:1,inFileResource:2,inFileNode:3,externalFile:4,externalXMF:5,XMFFileURIandNodeID:6},Xn={StandardMIDIFile:0,StandardMIDIFileType1:1,DLS1:2,DLS2:3,DLS22:4,mobileDLS:5},ci={standard:0,MMA:1,registered:2,nonRegistered:3},an={none:0,MMAUnpacker:1,registered:2,nonRegistered:3},Wn=class e{length;itemCount;metadataLength;metadata={};nodeData;innerNodes=[];packedContent=!1;nodeUnpackers=[];resourceFormat="unknown";constructor(A){let t=A.currentIndex;this.length=yA(A),this.itemCount=yA(A);let n=yA(A),s=A.currentIndex-t,o=n-s,r=A.slice(A.currentIndex,A.currentIndex+o);A.currentIndex+=o,this.metadataLength=yA(r);let E=r.slice(r.currentIndex,r.currentIndex+this.metadataLength);r.currentIndex+=this.metadataLength;let a,g;for(;E.currentIndex<E.length;){if(E[E.currentIndex]===0)E.currentIndex++,a=yA(E),Object.values(Zn).indexOf(a)===-1?(Y(`Unknown field specifier: ${a}`),g=`unknown_${a}`):g=Object.keys(Zn).find(u=>Zn[u]===a);else{let u=yA(E);a=eA(E,u),g=a}let m=yA(E);if(m===0){let u=yA(E),S=E.slice(E.currentIndex,E.currentIndex+u);E.currentIndex+=u,yA(S)<4?this.metadata[g]=eA(S,u-1):this.metadata[g]=S.slice(S.currentIndex)}else Y(`International content: ${m}`),E.currentIndex+=yA(E)}let d=r.currentIndex,B=yA(r),l=r.slice(r.currentIndex,d+B);if(r.currentIndex=d+B,B>0)for(this.packedContent=!0;l.currentIndex<B;){let h={};switch(h.id=yA(l),h.id){case an.nonRegistered:case an.registered:throw V(),new Error(`Unsupported unpacker ID: ${h.id}`);default:throw V(),new Error(`Unknown unpacker ID: ${h.id}`);case an.none:h.standardID=yA(l);break;case an.MMAUnpacker:let m=l[l.currentIndex++];m===0&&(m<<=8,m|=l[l.currentIndex++],m<<=8,m|=l[l.currentIndex++]);let u=yA(l);h.manufacturerID=m,h.manufacturerInternalID=u;break}h.decodedSize=yA(l),this.nodeUnpackers.push(h)}switch(A.currentIndex=t+n,this.referenceTypeID=yA(A),this.nodeData=A.slice(A.currentIndex,t+this.length),A.currentIndex=t+this.length,this.referenceTypeID){case Bt.inLineResource:break;case Bt.externalXMF:case Bt.inFileNode:case Bt.XMFFileURIandNodeID:case Bt.externalFile:case Bt.inFileResource:throw V(),new Error(`Unsupported reference type: ${this.referenceTypeID}`);default:throw V(),new Error(`Unknown reference type: ${this.referenceTypeID}`)}if(this.isFile){if(this.packedContent){let m=this.nodeData.slice(2,this.nodeData.length);p(`%cPacked content. Attemting to deflate. Target size: %c${this.nodeUnpackers[0].decodedSize}`,I.warn,I.value);try{this.nodeData=new L(Vn(m).buffer)}catch(u){throw V(),new Error(`Error unpacking XMF file contents: ${u.message}.`)}}let h=this.metadata.resourceFormat;if(h===void 0)Y("No resource format for this file node!");else{h[0]!==ci.standard&&(Y(`Non-standard formatTypeID: ${h}`),this.resourceFormat=h.toString());let u=h[1];Object.values(Xn).indexOf(u)===-1?Y(`Unrecognized resource format: ${u}`):this.resourceFormat=Object.keys(Xn).find(S=>Xn[S]===u)}}else for(this.resourceFormat="folder";this.nodeData.currentIndex<this.nodeData.length;){let h=this.nodeData.currentIndex,m=yA(this.nodeData),u=this.nodeData.slice(h,h+m);this.nodeData.currentIndex=h+m,this.innerNodes.push(new e(u))}}get isFile(){return this.itemCount===0}};function Ao(e,A){e.bankOffset=0;let t=eA(A,4);if(t!=="XMF_")throw V(),new SyntaxError(`Invalid XMF Header! Expected "_XMF", got "${t}"`);VA("%cParsing XMF file...",I.info);let n=eA(A,4);if(p(`%cXMF version: %c${n}`,I.info,I.recognized),n==="2.00"){let a=se(A,4),g=se(A,4);p(`%cFile Type ID: %c${a}%c, File Type Revision ID: %c${g}`,I.info,I.recognized,I.info,I.recognized)}yA(A);let s=yA(A);A.currentIndex+=s,A.currentIndex=yA(A);let o=new Wn(A),r,E=a=>{let g=(d,B)=>{a.metadata[d]!==void 0&&typeof a.metadata[d]=="string"&&(e.RMIDInfo[B]=a.metadata[d])};if(g("nodeName",fA.name),g("title",fA.name),g("copyrightNotice",fA.copyright),g("comment",fA.comment),a.isFile)switch(a.resourceFormat){default:return;case"DLS1":case"DLS2":case"DLS22":case"mobileDLS":p("%cFound embedded DLS!",I.recognized),e.embeddedSoundFont=a.nodeData.buffer;break;case"StandardMIDIFile":case"StandardMIDIFileType1":p("%cFound embedded MIDI!",I.recognized),r=a.nodeData;break}else for(let d of a.innerNodes)E(d)};return E(o),V(),r}var In=class extends ge{constructor(A,t=""){super(),FA("%cParsing MIDI File...",I.info),this.fileName=t;let n=new L(A),s,o=eA(n,4);if(n.currentIndex-=4,o==="RIFF"){n.currentIndex+=8;let E=eA(n,4,void 0,!1);if(E!=="RMID")throw V(),new SyntaxError(`Invalid RMIDI Header! Expected "RMID", got "${E}"`);let a=IA(n);if(a.header!=="data")throw V(),new SyntaxError(`Invalid RMIDI Chunk header! Expected "data", got "${E}"`);for(s=a.chunkData;n.currentIndex<=n.length;){let g=n.currentIndex,d=IA(n,!0);if(d.header==="RIFF"){let B=eA(d.chunkData,4).toLowerCase();B==="sfbk"||B==="sfpk"||B==="dls "?(p("%cFound embedded soundfont!",I.recognized),this.embeddedSoundFont=n.slice(g,g+d.size).buffer):Y(`Unknown RIFF chunk: "${B}"`),B==="dls "&&(this.isDLSRMIDI=!0)}else if(d.header==="LIST"&&eA(d.chunkData,4)==="INFO"){for(p("%cFound RMIDI INFO chunk!",I.recognized),this.RMIDInfo={};d.chunkData.currentIndex<=d.size;){let l=IA(d.chunkData,!0);this.RMIDInfo[l.header]=l.chunkData}this.RMIDInfo.ICOP&&(this.copyright=eA(this.RMIDInfo.ICOP,this.RMIDInfo.ICOP.length,void 0,!1).replaceAll(`
`," ")),this.RMIDInfo.INAM&&(this.rawMidiName=this.RMIDInfo[fA.name],this.midiName=eA(this.rawMidiName,this.rawMidiName.length,void 0,!1).replaceAll(`
`," ")),this.RMIDInfo.IALB&&!this.RMIDInfo.IPRD&&(this.RMIDInfo.IPRD=this.RMIDInfo.IALB),this.RMIDInfo.IPRD&&!this.RMIDInfo.IALB&&(this.RMIDInfo.IALB=this.RMIDInfo.IPRD),this.bankOffset=1,this.RMIDInfo[fA.bankOffset]&&(this.bankOffset=N(this.RMIDInfo[fA.bankOffset],2))}}this.isDLSRMIDI&&(this.bankOffset=0),this.embeddedSoundFont===void 0&&(this.bankOffset=0)}else o==="XMF_"?s=Ao(this,n):s=n;let r=this.readMIDIChunk(s);if(r.type!=="MThd")throw V(),new SyntaxError(`Invalid MIDI Header! Expected "MThd", got "${r.type}"`);if(r.size!==6)throw V(),new RangeError(`Invalid MIDI header chunk size! Expected 6, got ${r.size}`);this.format=se(r.data,2),this.tracksAmount=se(r.data,2),this.timeDivision=se(r.data,2);for(let E=0;E<this.tracksAmount;E++){let a=[],g=this.readMIDIChunk(s);if(g.type!=="MTrk")throw V(),new SyntaxError(`Invalid track header! Expected "MTrk" got "${g.type}"`);let d,B=0;for(this.format===2&&E>0&&(B+=this.tracks[E-1][this.tracks[E-1].length-1].ticks);g.data.currentIndex<g.size;){B+=yA(g.data);let l=g.data[g.data.currentIndex],h;if(d!==void 0&&l<128)h=d;else{if(d===void 0&&l<128)throw V(),new SyntaxError(`Unexpected byte with no running byte. (${l})`);h=g.data[g.data.currentIndex++]}let m=xs(h),u;switch(m){case-1:u=0;break;case-2:h=g.data[g.data.currentIndex++],u=yA(g.data);break;case-3:u=yA(g.data);break;default:u=Ns[h>>4],d=h;break}let S=new L(u);S.set(g.data.slice(g.data.currentIndex,g.data.currentIndex+u),0);let w=new ne(B,h,S);a.push(w),g.data.currentIndex+=u}this.tracks.push(a),p(`%cParsed %c${this.tracks.length}%c / %c${this.tracksAmount}`,I.info,I.value,I.info,I.value)}p("%cAll tracks parsed correctly!",I.recognized),this._parseInternal(),V(),p(`%cMIDI file parsed. Total tick time: %c${this.lastVoiceEventTick}%c, total seconds time: %c${this.duration}`,I.info,I.recognized,I.info,I.recognized)}readMIDIChunk(A){let t={};t.type=eA(A,4),t.size=se(A,4),t.data=new L(t.size);let n=A.slice(A.currentIndex,A.currentIndex+t.size);return t.data.set(n,0),A.currentIndex+=t.size,t}};function eo(e,A){this.midiData.usedChannelsOnTrack[e].size!==0&&(this.midiPortChannelOffset===0&&(this.midiPortChannelOffset+=16,this.midiPortChannelOffsets[A]=0),this.midiPortChannelOffsets[A]===void 0&&(this.synth.workletProcessorChannels.length<this.midiPortChannelOffset+15&&this._addNewMidiPort(),this.midiPortChannelOffsets[A]=this.midiPortChannelOffset,this.midiPortChannelOffset+=16),this.midiPorts[e]=A)}function to(e,A=!0){if(this.stop(),!e.tracks)throw new Error("This MIDI has no tracks!");if(this.oneTickToSeconds=60/(120*e.timeDivision),this.midiData=e,this.midiData.embeddedSoundFont!==void 0)p("%cEmbedded soundfont detected! Using it.",I.recognized),this.synth.setEmbeddedSoundFont(this.midiData.embeddedSoundFont,this.midiData.bankOffset);else{this.synth.overrideSoundfont&&this.synth.clearSoundFont(!0,!0),FA("%cPreloading samples...",I.info);let t=this.midiData.getUsedProgramsAndKeys(this.synth.soundfontManager);for(let[n,s]of Object.entries(t)){let o=parseInt(n.split(":")[0]),r=parseInt(n.split(":")[1]),E=this.synth.getPreset(o,r);p(`%cPreloading used samples on %c${E.presetName}%c...`,I.info,I.recognized,I.info);for(let a of s){let g=a.split("-");E.preloadSpecific(parseInt(g[0]),parseInt(g[1]))}}V()}if(this.tracks=this.midiData.tracks,this.midiPorts=this.midiData.midiPorts.slice(),this.midiPortChannelOffset=0,this.midiPortChannelOffsets={},this.midiData.midiPorts.forEach((t,n)=>{this.assignMIDIPort(n,t)}),this.duration=this.midiData.duration,this.firstNoteTime=this.midiData.MIDIticksToSeconds(this.midiData.firstNoteOn),p(`%cTotal song time: ${Yn(Math.ceil(this.duration)).time}`,I.recognized),this.post(UA.songChange,[new Wt(this.midiData),this.songIndex,A]),this.duration<=1&&(Y(`%cVery short song: (${Yn(Math.round(this.duration)).time}). Disabling loop!`,I.warn),this.loop=!1),A)this.play(!0);else{let t=this._skipToFirstNoteOn?this.midiData.firstNoteOn-1:0;this.setTimeTicks(t),this.pause()}}function no(e,A=!0){this.songs=e.reduce((t,n)=>{if(n.duration)return t.push(ge.copyFrom(n)),t;try{t.push(new In(n.binary,n.altName||""))}catch(s){return console.error(s),this.post(UA.midiError,s),t}return t},[]),!(this.songs.length<1)&&(this.songIndex=0,this.songs.length>1&&(this.loop=!1),this.shuffleSongIndexes(),this.loadCurrentSong(A))}function so(){if(this.songs.length===1){this.currentTime=0;return}this.songIndex++,this.songIndex%=this.songs.length,this.loadCurrentSong()}function oo(){if(this.songs.length===1){this.currentTime=0;return}this.songIndex--,this.songIndex<0&&(this.songIndex=this.songs.length-1),this.loadCurrentSong()}function ro(e=!0){e&&p("%cResetting all controllers!",I.info),this.callEvent("allcontrollerreset",void 0),this.setSystem(An);for(let A=0;A<this.workletProcessorChannels.length;A++){this.workletProcessorChannels[A].resetControllers();let t=this.workletProcessorChannels[A];t.lockPreset?this.callEvent("drumchange",{channel:A,isDrumChannel:t.drumChannel}):(t.setBankSelect(Xs(this.system)),A%16===9?(t.setPreset(this.drumPreset),t.presetUsesOverride=this.defaultDrumsUsesOverride,t.drumChannel=!0,this.callEvent("drumchange",{channel:A,isDrumChannel:!0})):(t.drumChannel=!1,t.presetUsesOverride=this.defaultDrumsUsesOverride,t.setPreset(this.defaultPreset),this.callEvent("drumchange",{channel:A,isDrumChannel:!1})));let n=t.preset.bank,s=n===128?128:t.presetUsesOverride?n+this.soundfontBankOffset:n;this.callEvent("programchange",{channel:A,program:t.preset.program,bank:s,userCalled:!1});for(let o=0;o<128;o++)this.workletProcessorChannels[A].lockedControllers[o]&&this.callEvent("controllerchange",{channel:A,controllerNumber:o,controllerValue:this.workletProcessorChannels[A].midiControllers[o]>>7});if(this.workletProcessorChannels[A].lockedControllers[MA+j.pitchWheel]===!1){let o=this.workletProcessorChannels[A].midiControllers[MA+j.pitchWheel],r=o>>7,E=o&127;this.callEvent("pitchwheel",{channel:A,MSB:r,LSB:E})}}this.tunings=[],this.tunings=[];for(let A=0;127>A;A++)this.tunings.push([]);this.setMIDIVolume(1)}function io(){this.channelOctaveTuning.fill(0);for(let A=0;A<be.length;A++){if(this.lockedControllers[A])continue;let t=be[A];this.midiControllers[A]!==t&&A<127?A===y.portamentoControl?this.midiControllers[A]=$t:this.controllerChange(A,t>>7):this.midiControllers[A]=t}this.channelVibrato={rate:0,depth:0,delay:0},this.holdPedal=!1,this.randomPan=!1;let e=this.customControllers[cA.channelTransposeFine];this.customControllers.set(On),this.setCustomController(cA.channelTransposeFine,e),this.resetParameters()}var _n=new Set([y.bankSelect,y.lsbForControl0BankSelect,y.mainVolume,y.lsbForControl7MainVolume,y.pan,y.lsbForControl10Pan,y.reverbDepth,y.tremoloDepth,y.chorusDepth,y.detuneDepth,y.phaserDepth,y.soundVariation,y.filterResonance,y.releaseTime,y.attackTime,y.brightness,y.decayTime,y.vibratoRate,y.vibratoDepth,y.vibratoDelay,y.soundController10]);function ao(){this.channelOctaveTuning.fill(0),this.pitchWheel(64,0),this.channelVibrato={rate:0,depth:0,delay:0};for(let e=0;e<128;e++){let A=be[e];!_n.has(e)&&A!==this.midiControllers[e]&&(e===y.portamentoControl?this.midiControllers[e]=$t:this.controllerChange(e,A>>7))}}function Io(){this.dataEntryState=YA.Idle,p("%cResetting Registered and Non-Registered Parameters!",I.info)}var We=be.slice(0,128);function go(e,A=void 0){this.oneTickToSeconds=60/(120*this.midiData.timeDivision),this.synth.resetAllControllers(),this.sendMIDIReset(),this._resetTimers();let t=this.synth.workletProcessorChannels.length,n=Array(t).fill(8192),s=[];for(let a=0;a<t;a++)s.push({program:-1,bank:0,actualBank:0});let o=a=>a===y.dataDecrement||a===y.dataIncrement||a===y.dataEntryMsb||a===y.dataDecrement||a===y.lsbForControl6DataEntry||a===y.RPNLsb||a===y.RPNMsb||a===y.NRPNLsb||a===y.NRPNMsb||a===y.bankSelect||a===y.lsbForControl0BankSelect||a===y.resetAllControllers,r=[];for(let a=0;a<t;a++)r.push(Array.from(We));function E(a){if(n[a]=8192,r?.[a]!==void 0)for(let g=0;g<We.length;g++)_n.has(g)||(r[a][g]=We[g])}for(;;){let a=this._findFirstEventIndex(),g=this.tracks[a][this.eventIndex[a]];if(A!==void 0){if(g.ticks>=A)break}else if(this.playedTime>=e)break;let d=Zt(g.messageStatusByte),B=d.channel+(this.midiPortChannelOffsets[this.midiPorts[a]]||0);switch(d.status){case F.noteOn:r[B]===void 0&&(r[B]=Array.from(We)),r[B][y.portamentoControl]=g.messageData[0];break;case F.noteOff:break;case F.pitchBend:n[B]=g.messageData[1]<<7|g.messageData[0];break;case F.programChange:if(this.midiData.isMultiPort&&this.midiData.usedChannelsOnTrack[a].size===0)break;let h=s[B];h.program=g.messageData[0],h.actualBank=h.bank;break;case F.controllerChange:if(this.midiData.isMultiPort&&this.midiData.usedChannelsOnTrack[a].size===0)break;let m=g.messageData[0];if(o(m)){let u=g.messageData[1];if(m===y.bankSelect){s[B].bank=u;break}else m===y.resetAllControllers&&E(B);this.sendMIDIMessages?this.sendMIDICC(B,m,u):this.synth.controllerChange(B,m,u)}else r[B]===void 0&&(r[B]=Array.from(We)),r[B][m]=g.messageData[1];break;default:this._processEvent(g,a);break}this.eventIndex[a]++,a=this._findFirstEventIndex();let l=this.tracks[a][this.eventIndex[a]];if(l===void 0)return this.stop(),!1;this.playedTime+=this.oneTickToSeconds*(l.ticks-g.ticks)}if(this.sendMIDIMessages){for(let a=0;a<t;a++)if(n[a]!==void 0&&this.sendMIDIPitchWheel(a,n[a]>>7,n[a]&127),r[a]!==void 0&&r[a].forEach((g,d)=>{g!==We[d]&&!o(d)&&this.sendMIDICC(a,d,g)}),s[a].program>=0&&s[a].actualBank>=0){let g=s[a].actualBank;this.sendMIDICC(a,y.bankSelect,g),this.sendMIDIProgramChange(a,s[a].program)}}else for(let a=0;a<t;a++)if(n[a]!==void 0&&this.synth.pitchWheel(a,n[a]>>7,n[a]&127),r[a]!==void 0&&r[a].forEach((g,d)=>{g!==We[d]&&!o(d)&&this.synth.controllerChange(a,d,g)}),s[a].program>=0&&s[a].actualBank>=0){let g=s[a].actualBank;this.synth.controllerChange(a,y.bankSelect,g),this.synth.programChange(a,s[a].program)}return!0}function Co(e=!1){if(this.midiData!==void 0){if(e){this.pausedTime=void 0,this.currentTime=0;return}if(this.currentTime>=this.duration){this.pausedTime=void 0,this.currentTime=0;return}this.paused&&(this._recalculateStartTime(this.pausedTime),this.pausedTime=void 0),this.sendMIDIMessages||this.playingNotes.forEach(A=>{this.synth.noteOn(A.channel,A.midiNote,A.velocity,!1,!0)}),this.setProcessHandler()}}function Eo(e){this.stop(),this.playingNotes=[],this.pausedTime=void 0,this.post(UA.timeChange,currentTime-this.midiData.MIDIticksToSeconds(e));let A=this._playTo(0,e);this._recalculateStartTime(this.playedTime),A&&this.play()}function Bo(e){this.absoluteStartTime=currentTime-e/this._playbackRate}var lA={noteOff:0,noteOn:1,ccChange:2,programChange:3,channelPressure:4,polyPressure:5,killNote:6,ccReset:7,setChannelVibrato:8,soundFontManager:9,stopAll:10,killNotes:11,muteChannel:12,addNewChannel:13,customcCcChange:14,debugMessage:15,systemExclusive:16,setMasterParameter:17,setDrums:18,pitchWheel:19,transpose:20,highPerformanceMode:21,lockController:22,sequencerSpecific:23,requestSynthesizerSnapshot:24,setLogLevel:25,keyModifierManager:26,setEffectsGain:27,destroyWorklet:28},Ue={mainVolume:0,masterPan:1,voicesCap:2,interpolationType:3,midiSystem:4},ie=-1,JA={channelProperties:0,eventCall:1,masterParameterChange:2,sequencerSpecific:3,synthesizerSnapshot:4,ready:5,soundfontError:6};function ho(e,A){switch(e){default:break;case zA.loadNewSongList:this.loadNewSongList(A[0],A[1]);break;case zA.pause:this.pause();break;case zA.play:this.play(A);break;case zA.stop:this.stop();break;case zA.setTime:this.currentTime=A;break;case zA.changeMIDIMessageSending:this.sendMIDIMessages=A;break;case zA.setPlaybackRate:this.playbackRate=A;break;case zA.setLoop:let[t,n]=A;this.loop=t,n===ie?this.loopCount=1/0:this.loopCount=n;break;case zA.changeSong:switch(A){case wt.forwards:this.nextSong();break;case wt.backwards:this.previousSong();break;case wt.shuffleOff:this.shuffleMode=!1,this.songIndex=this.shuffledSongIndexes[this.songIndex];break;case wt.shuffleOn:this.shuffleMode=!0,this.shuffleSongIndexes(),this.songIndex=0,this.loadCurrentSong()}break;case zA.getMIDI:this.post(UA.getMIDI,this.midiData);break;case zA.setSkipToFirstNote:this._skipToFirstNoteOn=A;break;case zA.setPreservePlaybackState:this.preservePlaybackState=A}}function co(e,A=void 0){this.synth.enableEventSystem&&this.synth.post({messageType:JA.sequencerSpecific,messageData:{messageType:e,messageData:A}})}function lo(e){this.post(UA.midiEvent,e)}function Qo(e,A,t){e%=16,this.sendMIDIMessages&&this.sendMIDIMessage([F.controllerChange|e,A,t])}function uo(e,A){e%=16,this.sendMIDIMessages&&this.sendMIDIMessage([F.programChange|e,A])}function fo(e,A,t){e%=16,this.sendMIDIMessages&&this.sendMIDIMessage([F.pitchBend|e,t,A])}function mo(){if(this.sendMIDIMessages){this.sendMIDIMessage([F.reset]);for(let e=0;e<16;e++)this.sendMIDIMessage([F.controllerChange|e,y.allSoundOff,0]),this.sendMIDIMessage([F.controllerChange|e,y.resetAllControllers,0])}}var kA=class{songs=[];songIndex=0;shuffledSongIndexes=[];synth;isActive=!1;sendMIDIMessages=!1;loopCount=1/0;eventIndex=[];playedTime=0;pausedTime=void 0;absoluteStartTime=currentTime;playingNotes=[];loop=!0;shuffleMode=!1;midiData=void 0;midiPorts=[];midiPortChannelOffset=0;midiPortChannelOffsets={};_skipToFirstNoteOn=!0;preservePlaybackState=!1;constructor(A){this.synth=A}_playbackRate=1;set playbackRate(A){let t=this.currentTime;this._playbackRate=A,this.currentTime=t}get currentTime(){return this.pausedTime!==void 0?this.pausedTime:(currentTime-this.absoluteStartTime)*this._playbackRate}set currentTime(A){if(A>this.duration||A<0){this._skipToFirstNoteOn?this.setTimeTicks(this.midiData.firstNoteOn-1):this.setTimeTicks(0);return}if(this._skipToFirstNoteOn&&A<this.firstNoteTime){this.setTimeTicks(this.midiData.firstNoteOn-1);return}this.stop(),this.playingNotes=[];let t=this.paused&&this.preservePlaybackState;if(this.pausedTime=void 0,this.post(UA.timeChange,currentTime-A),this.midiData.duration===0){Y("No duration!"),this.post(UA.pause,!0);return}this._playTo(A),this._recalculateStartTime(A),t?this.pause():this.play()}get paused(){return this.pausedTime!==void 0}pause(A=!1){if(this.paused){Y("Already paused");return}this.pausedTime=this.currentTime,this.stop(),this.post(UA.pause,A)}stop(){this.clearProcessHandler();for(let A=0;A<16;A++)this.synth.controllerChange(A,y.sustainPedal,0);if(this.synth.stopAllChannels(),this.sendMIDIMessages){for(let A of this.playingNotes)this.sendMIDIMessage([F.noteOff|A.channel%16,A.midiNote]);for(let A=0;A<16;A++)this.sendMIDICC(A,y.allNotesOff,0)}}loadCurrentSong(A=!0){let t=this.songIndex;this.shuffleMode&&(t=this.shuffledSongIndexes[this.songIndex]),this.loadNewSequence(this.songs[t],A)}_resetTimers(){this.playedTime=0,this.eventIndex=Array(this.tracks.length).fill(0)}setProcessHandler(){this.isActive=!0}clearProcessHandler(){this.isActive=!1}shuffleSongIndexes(){let A=this.songs.map((t,n)=>n);for(this.shuffledSongIndexes=[];A.length>0;){let t=A[Math.floor(Math.random()*A.length)];this.shuffledSongIndexes.push(t),A.splice(A.indexOf(t),1)}}};kA.prototype.sendMIDIMessage=lo;kA.prototype.sendMIDIReset=mo;kA.prototype.sendMIDICC=Qo;kA.prototype.sendMIDIProgramChange=uo;kA.prototype.sendMIDIPitchWheel=fo;kA.prototype.assignMIDIPort=eo;kA.prototype.post=co;kA.prototype.processMessage=ho;kA.prototype._processEvent=Hs;kA.prototype._addNewMidiPort=Ys;kA.prototype.processTick=Js;kA.prototype._findFirstEventIndex=Ks;kA.prototype.loadNewSequence=to;kA.prototype.loadNewSongList=no;kA.prototype.nextSong=so;kA.prototype.previousSong=oo;kA.prototype.play=Co;kA.prototype._playTo=go;kA.prototype.setTimeTicks=Eo;kA.prototype._recalculateStartTime=Bo;function li(e,A){let t=0;return e.drumChannel&&(t+=5),A.isInRelease&&(t-=5),t+=A.velocity/25,t-=A.volumeEnvelope.state,A.isInRelease&&(t-=5),t-=A.volumeEnvelope.currentAttenuationDb/50,t}function po(e){let A=[];for(let n of this.workletProcessorChannels)for(let s of n.voices)if(!s.finished){let o=li(n,s);A.push({channel:n,voice:s,priority:o})}A.sort((n,s)=>n.priority-s.priority);let t=A.slice(0,e);for(let{channel:n,voice:s}of t){let o=n.voices.indexOf(s);o>-1&&n.voices.splice(o,1)}}var me=me!==void 0?me:{},yo=!1,So;me.isInitialized=new Promise(e=>So=e);var Qi=function(e){var A,t,n,s,o,r,E,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",g="",d=0;e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");do s=a.indexOf(e.charAt(d++)),o=a.indexOf(e.charAt(d++)),r=a.indexOf(e.charAt(d++)),E=a.indexOf(e.charAt(d++)),A=s<<2|o>>4,t=(15&o)<<4|r>>2,n=(3&r)<<6|E,g+=String.fromCharCode(A),r!==64&&(g+=String.fromCharCode(t)),E!==64&&(g+=String.fromCharCode(n));while(d<e.length);return g};(function(){var e,A,t,n,s,o,r,E,a,g,d,B,l,h,m,u,S,w,k,x,b,G,C=C!==void 0?C:{};C.wasmBinary=Uint8Array.from(Qi("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"),function(c){return c.charCodeAt(0)});var C=C!==void 0?C:{},M={};for(e in C)C.hasOwnProperty(e)&&(M[e]=C[e]);C.arguments=[],C.thisProgram="./this.program",C.quit=function(c,Q){throw Q},C.preRun=[],C.postRun=[];var q=!1,gA=!1,tA=!1,T=!1;q=typeof window=="object",gA=typeof importScripts=="function",tA=typeof process=="object"&&typeof Rs=="function"&&!q&&!gA,T=!q&&!tA&&!gA;var $="";function BA(c){return C.locateFile?C.locateFile(c,$):$+c}tA?($=__dirname+"/",C.read=function(Q,f){var D;return A||(A=void 0),t||(t=void 0),Q=t.normalize(Q),D=A.readFileSync(Q),f?D:D.toString()},C.readBinary=function(Q){var f=C.read(Q,!0);return f.buffer||(f=new Uint8Array(f)),XA(f.buffer),f},process.argv.length>1&&(C.thisProgram=process.argv[1].replace(/\\/g,"/")),C.arguments=process.argv.slice(2),typeof module<"u",process.on("uncaughtException",function(c){if(!(c instanceof St))throw c}),process.on("unhandledRejection",function(c,Q){process.exit(1)}),C.quit=function(c){process.exit(c)},C.inspect=function(){return"[Emscripten Module object]"}):T?(typeof read<"u"&&(C.read=function(Q){return read(Q)}),C.readBinary=function(Q){var f;return typeof readbuffer=="function"?new Uint8Array(readbuffer(Q)):(XA(typeof(f=read(Q,"binary"))=="object"),f)},typeof scriptArgs<"u"?C.arguments=scriptArgs:typeof arguments<"u"&&(C.arguments=arguments),typeof quit=="function"&&(C.quit=function(c){quit(c)})):(q||gA)&&(q?document.currentScript&&($=document.currentScript.src):$=self.location.href,$=$.indexOf("blob:")!==0?$.split("/").slice(0,-1).join("/")+"/":"",C.read=function(Q){var f=new XMLHttpRequest;return f.open("GET",Q,!1),f.send(null),f.responseText},gA&&(C.readBinary=function(Q){var f=new XMLHttpRequest;return f.open("GET",Q,!1),f.responseType="arraybuffer",f.send(null),new Uint8Array(f.response)}),C.readAsync=function(Q,f,D){var K=new XMLHttpRequest;K.open("GET",Q,!0),K.responseType="arraybuffer",K.onload=function(){if(K.status==200||K.status==0&&K.response){f(K.response);return}D()},K.onerror=D,K.send(null)},C.setWindowTitle=function(c){document.title=c});var sA=C.print||(typeof console<"u"?console.log.bind(console):typeof print<"u"?print:null),oA=C.printErr||(typeof printErr<"u"?printErr:typeof console<"u"&&console.warn.bind(console)||sA);for(e in M)M.hasOwnProperty(e)&&(C[e]=M[e]);function EA(c){var Q=h;return h=h+c+15&-16,Q}function v(c){var Q=a[x>>2],f=Q+c+15&-16;return a[x>>2]=f,f>=NA&&!mt()?(a[x>>2]=Q,0):Q}function J(c,Q){return Q||(Q=16),c=Math.ceil(c/Q)*Q}function W(c){switch(c){case"i1":case"i8":return 1;case"i16":return 2;case"i32":case"float":return 4;case"i64":case"double":return 8;default:if(c[c.length-1]==="*")return 4;if(c[0]!=="i")return 0;var Q=parseInt(c.substr(1));return XA(Q%8==0),Q/8}}function O(c){O.shown||(O.shown={}),O.shown[c]||(O.shown[c]=1,oA(c))}M=void 0;var rA={"f64-rem":function(c,Q){return c%Q},debugger:function(){}},KA=[];function Ie(c,Q){for(var f=0,D=f;D<f+0;D++)if(!KA[D])return KA[D]=c,1+D;throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."}function ce(c){KA[c-1]=null}var le={};function $e(c,Q){if(c){XA(Q),le[Q]||(le[Q]={});var f=le[Q];return f[c]||(Q.length===1?f[c]=function(){return aA(Q,c)}:Q.length===2?f[c]=function(K){return aA(Q,c,[K])}:f[c]=function(){return aA(Q,c,Array.prototype.slice.call(arguments))}),f[c]}}function pe(c,Q,f){return f?+(c>>>0)+4294967296*+(Q>>>0):+(c>>>0)+4294967296*+(0|Q)}function aA(c,Q,f){return f&&f.length?C["dynCall_"+c].apply(null,[Q].concat(f)):C["dynCall_"+c].call(null,Q)}var QA=0,Qe=0;function XA(c,Q){c||ke("Assertion failed: "+Q)}function Re(c){var Q=C["_"+c];return XA(Q,"Cannot call unknown function "+c+", make sure it is exported"),Q}var Ge={stackSave:function(){Un()},stackRestore:function(){Ln()},arrayToC:function(c){var Q,f,D=Pt(c.length);return Q=c,f=D,s.set(Q,f),D},stringToC:function(c){var Q=0;if(c!=null&&c!==0){var f=(c.length<<2)+1;Q=Pt(f),ut(c,Q,f)}return Q}},Qt={string:Ge.stringToC,array:Ge.arrayToC};function At(c,Q,f,D,K){var iA=Re(c),CA=[],Z=0;if(D)for(var LA=0;LA<D.length;LA++){var mA=Qt[f[LA]];mA?(Z===0&&(Z=Un()),CA[LA]=mA(D[LA])):CA[LA]=D[LA]}var pA,dA=iA.apply(null,CA);return dA=(pA=dA,Q==="string"?Me(pA):Q==="boolean"?!!pA:pA),Z!==0&&Ln(Z),dA}function dt(c,Q,f,D){switch((f=f||"i8").charAt(f.length-1)==="*"&&(f="i32"),f){case"i1":case"i8":s[c>>0]=Q;break;case"i16":r[c>>1]=Q;break;case"i32":a[c>>2]=Q;break;case"i64":tempI64=[Q>>>0,+ri(tempDouble=Q)>=1?tempDouble>0?(0|ai(+Ss(tempDouble/4294967296),4294967295))>>>0:~~+ii((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0],a[c>>2]=tempI64[0],a[c+4>>2]=tempI64[1];break;case"float":d[c>>2]=Q;break;case"double":B[c>>3]=Q;break;default:ke("invalid type for setValue: "+f)}}function wn(c,Q,f){switch((Q=Q||"i8").charAt(Q.length-1)==="*"&&(Q="i32"),Q){case"i1":case"i8":return s[c>>0];case"i16":return r[c>>1];case"i32":case"i64":return a[c>>2];case"float":return d[c>>2];case"double":return B[c>>3];default:ke("invalid type for getValue: "+Q)}return null}function Fn(c,Q,f,D){typeof c=="number"?(iA=!0,CA=c):(iA=!1,CA=c.length);var K=typeof Q=="string"?Q:null;if(Z=f==4?D:[typeof Ot=="function"?Ot:EA,Pt,EA,v][f===void 0?2:f](Math.max(CA,K?1:Q.length)),iA){for(D=Z,XA((3&Z)==0),LA=Z+(-4&CA);D<LA;D+=4)a[D>>2]=0;for(LA=Z+CA;D<LA;)s[D++>>0]=0;return Z}if(K==="i8")return c.subarray||c.slice?o.set(c,Z):o.set(new Uint8Array(c),Z),Z;for(var iA,CA,Z,LA,mA,pA,dA,nA=0;nA<CA;){var PA=c[nA];if((mA=K||Q[nA])===0){nA++;continue}mA=="i64"&&(mA="i32"),dt(Z+nA,PA,mA),dA!==mA&&(pA=W(mA),dA=mA),nA+=pA}return Z}function et(c){return m?Nn?Ot(c):v(c):EA(c)}function Me(c,Q){if(Q===0||!c)return"";for(var f,D,K,iA=0,CA=0;iA|=D=o[c+CA>>0],(D!=0||Q)&&(CA++,!Q||CA!=Q););Q||(Q=CA);var Z="";if(iA<128){for(;Q>0;)K=String.fromCharCode.apply(String,o.subarray(c,c+Math.min(Q,1024))),Z=Z?Z+K:K,c+=1024,Q-=1024;return Z}return f=c,function(mA,pA){for(var dA=pA;mA[dA];)++dA;if(dA-pA>16&&mA.subarray&&vt)return vt.decode(mA.subarray(pA,dA));for(var nA,PA,$A,Ae,ee,Ze,te="";;){if(!(nA=mA[pA++]))return te;if(!(128&nA)){te+=String.fromCharCode(nA);continue}if(PA=63&mA[pA++],(224&nA)==192){te+=String.fromCharCode((31&nA)<<6|PA);continue}if($A=63&mA[pA++],(240&nA)==224?nA=(15&nA)<<12|PA<<6|$A:(Ae=63&mA[pA++],(248&nA)==240?nA=(7&nA)<<18|PA<<12|$A<<6|Ae:(ee=63&mA[pA++],nA=(252&nA)==248?(3&nA)<<24|PA<<18|$A<<12|Ae<<6|ee:(1&nA)<<30|PA<<24|$A<<18|Ae<<12|ee<<6|(Ze=63&mA[pA++]))),nA<65536)te+=String.fromCharCode(nA);else{var Dt=nA-65536;te+=String.fromCharCode(55296|Dt>>10,56320|1023&Dt)}}}(o,f)}function WA(c){for(var Q="";;){var f=s[c++>>0];if(!f)return Q;Q+=String.fromCharCode(f)}}function Rn(c,Q){return function(D,K,iA){for(var CA=0;CA<D.length;++CA)s[K++>>0]=D.charCodeAt(CA);iA||(s[K>>0]=0)}(c,Q,!1)}var vt=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function tt(c,Q,f,D){if(!(D>0))return 0;for(var K=f,iA=f+D-1,CA=0;CA<c.length;++CA){var Z=c.charCodeAt(CA);if(Z>=55296&&Z<=57343&&(Z=65536+((1023&Z)<<10)|1023&c.charCodeAt(++CA)),Z<=127){if(f>=iA)break;Q[f++]=Z}else if(Z<=2047){if(f+1>=iA)break;Q[f++]=192|Z>>6,Q[f++]=128|63&Z}else if(Z<=65535){if(f+2>=iA)break;Q[f++]=224|Z>>12,Q[f++]=128|Z>>6&63,Q[f++]=128|63&Z}else if(Z<=2097151){if(f+3>=iA)break;Q[f++]=240|Z>>18,Q[f++]=128|Z>>12&63,Q[f++]=128|Z>>6&63,Q[f++]=128|63&Z}else if(Z<=67108863){if(f+4>=iA)break;Q[f++]=248|Z>>24,Q[f++]=128|Z>>18&63,Q[f++]=128|Z>>12&63,Q[f++]=128|Z>>6&63,Q[f++]=128|63&Z}else{if(f+5>=iA)break;Q[f++]=252|Z>>30,Q[f++]=128|Z>>24&63,Q[f++]=128|Z>>18&63,Q[f++]=128|Z>>12&63,Q[f++]=128|Z>>6&63,Q[f++]=128|63&Z}}return Q[f]=0,f-K}function ut(c,Q,f){return tt(c,o,Q,f)}function qA(c){for(var Q=0,f=0;f<c.length;++f){var D=c.charCodeAt(f);D>=55296&&D<=57343&&(D=65536+((1023&D)<<10)|1023&c.charCodeAt(++f)),D<=127?++Q:D<=2047?Q+=2:D<=65535?Q+=3:D<=2097151?Q+=4:D<=67108863?Q+=5:Q+=6}return Q}var ye=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function ft(c){for(var Q=c,f=Q>>1;r[f];)++f;if((Q=f<<1)-c>32&&ye)return ye.decode(o.subarray(c,Q));for(var D=0,K="";;){var iA=r[c+2*D>>1];if(iA==0)return K;++D,K+=String.fromCharCode(iA)}}function Gn(c,Q,f){if(f===void 0&&(f=2147483647),f<2)return 0;for(var D=Q,K=(f-=2)<2*c.length?f/2:c.length,iA=0;iA<K;++iA){var CA=c.charCodeAt(iA);r[Q>>1]=CA,Q+=2}return r[Q>>1]=0,Q-D}function Se(c){return 2*c.length}function Ht(c){for(var Q=0,f="";;){var D=a[c+4*Q>>2];if(D==0)return f;if(++Q,D>=65536){var K=D-65536;f+=String.fromCharCode(55296|K>>10,56320|1023&K)}else f+=String.fromCharCode(D)}}function Yt(c,Q,f){if(f===void 0&&(f=2147483647),f<4)return 0;for(var D=Q,K=D+f-4,iA=0;iA<c.length;++iA){var CA=c.charCodeAt(iA);if(CA>=55296&&CA<=57343&&(CA=65536+((1023&CA)<<10)|1023&c.charCodeAt(++iA)),a[Q>>2]=CA,(Q+=4)+4>K)break}return a[Q>>2]=0,Q-D}function us(c){for(var Q=0,f=0;f<c.length;++f){var D=c.charCodeAt(f);D>=55296&&D<=57343&&++f,Q+=4}return Q}function fs(c){var Q=qA(c)+1,f=Ot(Q);return f&&tt(c,s,f,Q),f}function Jt(c){var Q=qA(c)+1,f=Pt(Q);return tt(c,s,f,Q),f}function Oe(c){return c}function Mn(){var c,Q=function(){var D=Error();if(!D.stack){try{throw Error(0)}catch(K){D=K}if(!D.stack)return"(no stack trace available)"}return D.stack.toString()}();return C.extraStackTrace&&(Q+=`
`+C.extraStackTrace()),(c=Q).replace(/__Z[\w\d_]+/g,function(f){var D,K=D=f;return f===K?f:f+" ["+K+"]"})}function De(c,Q){return c%Q>0&&(c+=Q-c%Q),c}function nt(c){C.buffer=n=c}function Pe(){C.HEAP8=s=new Int8Array(n),C.HEAP16=r=new Int16Array(n),C.HEAP32=a=new Int32Array(n),C.HEAPU8=o=new Uint8Array(n),C.HEAPU16=E=new Uint16Array(n),C.HEAPU32=g=new Uint32Array(n),C.HEAPF32=d=new Float32Array(n),C.HEAPF64=B=new Float64Array(n)}function mt(){var c=C.usingWasm?65536:16777216,Q=2147483648-c;if(a[x>>2]>Q)return!1;var f=NA;for(NA=Math.max(NA,16777216);NA<a[x>>2];)NA=NA<=536870912?De(2*NA,c):Math.min(De((3*NA+2147483648)/4,c),Q);var D=C.reallocBuffer(NA);return D&&D.byteLength==NA?(nt(D),Pe(),!0):(NA=f,!1)}l=h=u=S=w=k=x=0,m=!1,C.reallocBuffer||(C.reallocBuffer=function(c){try{if(ArrayBuffer.transfer)Q=ArrayBuffer.transfer(n,c);else{var Q,f=s;Q=new ArrayBuffer(c),new Int8Array(Q).set(f)}}catch{return!1}return!!Ii(Q)&&Q});try{(b=Function.prototype.call.bind(Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get))(new ArrayBuffer(4))}catch{b=function(Q){return Q.byteLength}}var st=C.TOTAL_STACK||5242880,NA=C.TOTAL_MEMORY||16777216;function Kt(){return NA}function xe(c){for(;c.length>0;){var Q=c.shift();if(typeof Q=="function"){Q();continue}var f=Q.func;typeof f=="number"?Q.arg===void 0?C.dynCall_v(f):C.dynCall_vi(f,Q.arg):f(Q.arg===void 0?null:Q.arg)}}NA<st&&oA("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+NA+"! (TOTAL_STACK="+st+")"),C.buffer?n=C.buffer:(typeof WebAssembly=="object"&&typeof WebAssembly.Memory=="function"?(C.wasmMemory=new WebAssembly.Memory({initial:NA/65536}),n=C.wasmMemory.buffer):n=new ArrayBuffer(NA),C.buffer=n),Pe();var pt=[],xn=[],ms=[],ps=[],ys=[],Nn=!1,ni=!1;function si(c){pt.unshift(c)}function Ti(c){xn.unshift(c)}function vi(c){ms.unshift(c)}function Hi(c){ps.unshift(c)}function oi(c){ys.unshift(c)}function Yi(c,Q,f){var D,K;O("writeStringToMemory is deprecated and should not be called! Use stringToUTF8() instead!"),f&&(D=s[K=Q+qA(c)]),ut(c,Q,1/0),f&&(s[K]=D)}function Ji(c,Q,f){return c>=0?c:Q<=32?2*Math.abs(1<<Q-1)+c:Math.pow(2,Q)+c}function Ki(c,Q,f){if(c<=0)return c;var D=Q<=32?Math.abs(1<<Q-1):Math.pow(2,Q-1);return c>=D&&(Q<=32||c>D)&&(c=-2*D+c),c}var ri=Math.abs,ii=Math.ceil,Ss=Math.floor,ai=Math.min,Ve=0,bn=null,yt=null;function qi(c){return c}C.preloadedImages={},C.preloadedAudios={};var Ds="data:application/octet-stream;base64,";function qt(c){return String.prototype.startsWith?c.startsWith(Ds):c.indexOf(Ds)===0}(function(){var Q="main.wast",f="main.wasm",D="main.temp.asm.js";qt(Q)||(Q=BA(Q)),qt(f)||(f=BA(f)),qt(D)||(D=BA(D));var K={global:null,env:null,asm2wasm:rA,parent:C},iA=null;function CA(dA){return dA}function Z(){try{if(C.wasmBinary)return new Uint8Array(C.wasmBinary);if(C.readBinary)return C.readBinary(f);throw"both async and sync fetching of the wasm failed"}catch(dA){ke(dA)}}C.asmPreload=C.asm;var LA=C.reallocBuffer,mA=function(dA){dA=De(dA,C.usingWasm?65536:16777216);var nA=C.buffer.byteLength;if(C.usingWasm)try{var PA=C.wasmMemory.grow((dA-nA)/65536);return PA!==-1?C.buffer=C.wasmMemory.buffer:null}catch{return null}};C.reallocBuffer=function(dA){return pA==="asmjs"?LA(dA):mA(dA)};var pA="";C.asm=function(dA,nA,PA){var $A;if(!(nA=$A=nA).table){var Ae,ee=C.wasmTableSize;ee===void 0&&(ee=1024);var Ze=C.wasmMaxTableSize;typeof WebAssembly=="object"&&typeof WebAssembly.Table=="function"?Ze!==void 0?nA.table=new WebAssembly.Table({initial:ee,maximum:Ze,element:"anyfunc"}):nA.table=new WebAssembly.Table({initial:ee,element:"anyfunc"}):nA.table=Array(ee),C.wasmTable=nA.table}return nA.memoryBase||(nA.memoryBase=C.STATIC_BASE),nA.tableBase||(nA.tableBase=0),Ae=function(Dt,ot,vn){if(typeof WebAssembly!="object")return oA("no native wasm support detected"),!1;if(!(C.wasmMemory instanceof WebAssembly.Memory))return oA("no native wasm Memory in use"),!1;function Vt(de,ue){if((iA=de.exports).memory){var kt,Hn,Fs;kt=iA.memory,Hn=C.buffer,kt.byteLength<Hn.byteLength&&oA("the new buffer in mergeMemory is smaller than the previous one. in native wasm, we should grow memory here"),Fs=new Int8Array(Hn),new Int8Array(kt).set(Fs),nt(kt),Pe()}C.asm=iA,C.usingWasm=!0,function(_i){if(Ve--,C.monitorRunDependencies&&C.monitorRunDependencies(Ve),Ve==0&&(bn!==null&&(clearInterval(bn),bn=null),yt)){var gi=yt;yt=null,gi()}}("wasm-instantiate")}if(ot.memory=C.wasmMemory,K.global={NaN:NaN,Infinity:1/0},K["global.Math"]=Math,K.env=ot,Ve++,C.monitorRunDependencies&&C.monitorRunDependencies(Ve),C.instantiateWasm)try{return C.instantiateWasm(K,Vt)}catch(de){return oA("Module.instantiateWasm callback failed with error: "+de),!1}function rt(de){Vt(de.instance,de.module)}function ws(de){(!C.wasmBinary&&(q||gA)&&typeof fetch=="function"?fetch(f,{credentials:"same-origin"}).then(function(ue){if(!ue.ok)throw"failed to load wasm binary file at '"+f+"'";return ue.arrayBuffer()}).catch(function(){return Z()}):new Promise(function(ue,kt){ue(Z())})).then(function(ue){return WebAssembly.instantiate(ue,K)}).then(de).catch(function(ue){oA("failed to asynchronously prepare wasm: "+ue),ke(ue)})}return C.wasmBinary||typeof WebAssembly.instantiateStreaming!="function"||qt(f)||typeof fetch!="function"?ws(rt):WebAssembly.instantiateStreaming(fetch(f,{credentials:"same-origin"}),K).then(rt).catch(function(de){oA("wasm streaming compile failed: "+de),oA("falling back to ArrayBuffer instantiation"),ws(rt)}),{}}(dA,nA,PA),XA(Ae,"no binaryen method succeeded."),Ae},C.asm})(),h=(l=1024)+4816,xn.push(),C.STATIC_BASE=l,C.STATIC_BUMP=4816;var _A=h;function Oi(c){s[_A]=s[c],s[_A+1]=s[c+1],s[_A+2]=s[c+2],s[_A+3]=s[c+3]}function Pi(c){s[_A]=s[c],s[_A+1]=s[c+1],s[_A+2]=s[c+2],s[_A+3]=s[c+3],s[_A+4]=s[c+4],s[_A+5]=s[c+5],s[_A+6]=s[c+6],s[_A+7]=s[c+7]}function Vi(c,Q,f){var D=f>0?f:qA(c)+1,K=Array(D),iA=tt(c,K,0,K.length);return Q&&(K.length=iA),K}function Zi(c){for(var Q=[],f=0;f<c.length;f++){var D=c[f];D>255&&(D&=255),Q.push(String.fromCharCode(D))}return Q.join("")}h+=16,x=EA(4),w=(u=S=J(h))+st,k=J(w),a[x>>2]=k,m=!0,C.wasmTableSize=4,C.wasmMaxTableSize=4,C.asmGlobalArg={},C.asmLibraryArg={abort:ke,assert:XA,enlargeMemory:mt,getTotalMemory:Kt,abortOnCannotGrowMemory:function(){ke("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+NA+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")},invoke_iii:function(Q,f,D){var K=Un();try{return C.dynCall_iii(Q,f,D)}catch(iA){if(Ln(K),typeof iA!="number"&&iA!=="longjmp")throw iA;C.setThrew(1,0)}},___assert_fail:function(Q,f,D,K){ke("Assertion failed: "+Me(Q)+", at: "+[f?Me(f):"unknown filename",D,K?Me(K):"unknown function"])},___setErrNo:function(Q){return C.___errno_location&&(a[C.___errno_location()>>2]=Q),Q},_abort:function(){C.abort()},_emscripten_memcpy_big:function(Q,f,D){return o.set(o.subarray(f,f+D),Q),Q},_llvm_floor_f64:Ss,DYNAMICTOP_PTR:x,tempDoublePtr:_A,ABORT:QA,STACKTOP:S,STACK_MAX:w};var ks=C.asm(C.asmGlobalArg,C.asmLibraryArg,n);C.asm=ks,C.___errno_location=function(){return C.asm.___errno_location.apply(null,arguments)};var Ii=C._emscripten_replace_memory=function(){return C.asm._emscripten_replace_memory.apply(null,arguments)};C._free=function(){return C.asm._free.apply(null,arguments)};var Ot=C._malloc=function(){return C.asm._malloc.apply(null,arguments)};C._memcpy=function(){return C.asm._memcpy.apply(null,arguments)},C._memset=function(){return C.asm._memset.apply(null,arguments)},C._sbrk=function(){return C.asm._sbrk.apply(null,arguments)},C._stb_vorbis_js_channels=function(){return C.asm._stb_vorbis_js_channels.apply(null,arguments)},C._stb_vorbis_js_close=function(){return C.asm._stb_vorbis_js_close.apply(null,arguments)},C._stb_vorbis_js_decode=function(){return C.asm._stb_vorbis_js_decode.apply(null,arguments)},C._stb_vorbis_js_open=function(){return C.asm._stb_vorbis_js_open.apply(null,arguments)},C._stb_vorbis_js_sample_rate=function(){return C.asm._stb_vorbis_js_sample_rate.apply(null,arguments)},C.establishStackSpace=function(){return C.asm.establishStackSpace.apply(null,arguments)},C.getTempRet0=function(){return C.asm.getTempRet0.apply(null,arguments)},C.runPostSets=function(){return C.asm.runPostSets.apply(null,arguments)},C.setTempRet0=function(){return C.asm.setTempRet0.apply(null,arguments)},C.setThrew=function(){return C.asm.setThrew.apply(null,arguments)};var Pt=C.stackAlloc=function(){return C.asm.stackAlloc.apply(null,arguments)},Ln=C.stackRestore=function(){return C.asm.stackRestore.apply(null,arguments)},Un=C.stackSave=function(){return C.asm.stackSave.apply(null,arguments)};function St(c){this.name="ExitStatus",this.message="Program terminated with exit("+c+")",this.status=c}function Tn(c){c=c||C.arguments,!(Ve>0)&&(function(){if(C.preRun)for(typeof C.preRun=="function"&&(C.preRun=[C.preRun]);C.preRun.length;)si(C.preRun.shift());xe(pt)}(),!(Ve>0)&&(C.calledRun||(C.setStatus?(C.setStatus("Running..."),setTimeout(function(){setTimeout(function(){C.setStatus("")},1),Q()},1)):Q())));function Q(){!C.calledRun&&(C.calledRun=!0,QA||(Nn||(Nn=!0,xe(xn)),xe(ms),C.onRuntimeInitialized&&C.onRuntimeInitialized(),function(){if(C.postRun)for(typeof C.postRun=="function"&&(C.postRun=[C.postRun]);C.postRun.length;)oi(C.postRun.shift());xe(ys)}()))}}function Xi(c,Q){(!Q||!C.noExitRuntime||c!==0)&&(C.noExitRuntime||(QA=!0,Qe=c,S=G,xe(ps),ni=!0,C.onExit&&C.onExit(c)),C.quit(c,new St(c)))}function ke(c){throw C.onAbort&&C.onAbort(c),c!==void 0?(sA(c),oA(c),c=JSON.stringify(c)):c="",QA=!0,Qe=1,"abort("+c+"). Build with -s ASSERTIONS=1 for more info."}if(C.dynCall_iii=function(){return C.asm.dynCall_iii.apply(null,arguments)},C.asm=ks,C.ccall=At,C.cwrap=function(Q,f,D,K){var iA=(D=D||[]).every(function(CA){return CA==="number"});return f!=="string"&&iA&&!K?Re(Q):function(){return At(Q,f,D,arguments,K)}},St.prototype=Error(),St.prototype.constructor=St,yt=function c(){C.calledRun||Tn(),C.calledRun||(yt=c)},C.run=Tn,C.abort=ke,C.preInit)for(typeof C.preInit=="function"&&(C.preInit=[C.preInit]);C.preInit.length>0;)C.preInit.pop()();C.noExitRuntime=!0,Tn(),C.onRuntimeInitialized=()=>{yo=!0,So()},me.decode=function(c){return function(f){if(!yo)throw Error("Not initialized");var D={};function K(ot){return new Int32Array(C.HEAPU8.buffer,ot,1)[0]}function iA(ot,vn){var Vt=new ArrayBuffer(vn*Float32Array.BYTES_PER_ELEMENT),rt=new Float32Array(Vt);return rt.set(new Float32Array(C.HEAPU8.buffer,ot,vn)),rt}D.open=C.cwrap("stb_vorbis_js_open","number",[]),D.close=C.cwrap("stb_vorbis_js_close","void",["number"]),D.channels=C.cwrap("stb_vorbis_js_channels","number",["number"]),D.sampleRate=C.cwrap("stb_vorbis_js_sample_rate","number",["number"]),D.decode=C.cwrap("stb_vorbis_js_decode","number",["number","number","number","number","number"]);var CA,Z,LA,mA,pA=D.open(),dA=(CA=f,Z=f.byteLength,LA=C._malloc(Z),(mA=new Uint8Array(C.HEAPU8.buffer,LA,Z)).set(new Uint8Array(CA,0,Z)),mA),nA=C._malloc(4),PA=C._malloc(4),$A=D.decode(pA,dA.byteOffset,dA.byteLength,nA,PA);if(C._free(dA.byteOffset),$A<0)throw D.close(pA),C._free(nA),Error("stbvorbis decode failed: "+$A);for(var Ae=D.channels(pA),ee=Array(Ae),Ze=new Int32Array(C.HEAPU32.buffer,K(nA),Ae),te=0;te<Ae;te++)ee[te]=iA(Ze[te],$A),C._free(Ze[te]);var Dt=D.sampleRate(pA);return D.close(pA),C._free(K(nA)),C._free(nA),{data:ee,sampleRate:Dt,eof:!0,error:null}}(c)}})();var jn=new Float32Array(30001);for(let e=0;e<jn.length;e++){let A=-15e3+e;jn[e]=Math.pow(2,A/1200)}function Ce(e){return e<=-32767?0:jn[e- -15e3]}var gn=-2e4,Do=16500,$n=new Float32Array(Do-gn+1);for(let e=0;e<$n.length;e++){let A=gn+e;$n[e]=440*Math.pow(2,(A-6900)/1200)}function Rt(e){return e<gn||e>Do?440*Math.pow(2,(e-6900)/1200):$n[~~e-gn]}var es=-1660,di=1600,As=new Float32Array((di-es)*100+1);for(let e=0;e<As.length;e++){let A=(es*100+e)/100;As[e]=Math.pow(10,-A/20)}function ae(e){return As[Math.floor((e-es)*100)]}var ko=.01,jA=100,ts=90,ui=15e-6,Ee=class e{currentSampleTime=0;sampleRate;currentAttenuationDb=jA;state=0;releaseStartDb=jA;releaseStartTimeSamples=0;currentReleaseGain=1;attackDuration=0;decayDuration=0;releaseDuration=0;attenuation=0;attenuationTargetGain=0;attenuationTarget=0;sustainDbRelative=0;delayEnd=0;attackEnd=0;holdEnd=0;decayEnd=0;constructor(A,t){this.sampleRate=A,this.canEndOnSilentSustain=t/10>=ts}static startRelease(A){A.volumeEnvelope.releaseStartTimeSamples=A.volumeEnvelope.currentSampleTime,A.volumeEnvelope.currentReleaseGain=ae(A.volumeEnvelope.currentAttenuationDb),e.recalculate(A)}static recalculate(A){let t=A.volumeEnvelope,n=g=>Math.max(0,Math.floor(Ce(g)*t.sampleRate));t.attenuationTarget=Math.max(0,Math.min(A.modulatedGenerators[i.initialAttenuation],1440))/10,t.attenuationTargetGain=ae(t.attenuationTarget),t.sustainDbRelative=Math.min(jA,A.modulatedGenerators[i.sustainVolEnv]/10);let s=Math.min(jA,t.sustainDbRelative);t.attackDuration=n(A.modulatedGenerators[i.attackVolEnv]);let o=A.modulatedGenerators[i.decayVolEnv],r=(60-A.targetKey)*A.modulatedGenerators[i.keyNumToVolEnvDecay],E=s/jA;t.decayDuration=n(o+r)*E,t.releaseDuration=n(A.modulatedGenerators[i.releaseVolEnv]),t.delayEnd=n(A.modulatedGenerators[i.delayVolEnv]),t.attackEnd=t.attackDuration+t.delayEnd;let a=(60-A.targetKey)*A.modulatedGenerators[i.keyNumToVolEnvHold];if(t.holdEnd=n(A.modulatedGenerators[i.holdVolEnv]+a)+t.attackEnd,t.decayEnd=t.decayDuration+t.holdEnd,t.state===0&&t.attackEnd===0&&(t.state=2),A.isInRelease){let g=Math.max(0,Math.min(jA,t.sustainDbRelative)),d=g/jA;switch(t.decayDuration=n(o+r)*d,t.state){case 0:t.releaseStartDb=jA;break;case 1:let l=1-(t.attackEnd-t.releaseStartTimeSamples)/t.attackDuration;t.releaseStartDb=20*Math.log10(l)*-1;break;case 2:t.releaseStartDb=0;break;case 3:t.releaseStartDb=(1-(t.decayEnd-t.releaseStartTimeSamples)/t.decayDuration)*g;break;case 4:t.releaseStartDb=g;break}t.releaseStartDb=Math.max(0,Math.min(t.releaseStartDb,jA)),t.releaseStartDb>=ts&&(A.finished=!0),t.currentReleaseGain=ae(t.releaseStartDb);let B=(jA-t.releaseStartDb)/jA;t.releaseDuration*=B}}static apply(A,t,n,s){let o=A.volumeEnvelope,r=n/10,E=s;if(A.isInRelease){let g=o.currentSampleTime-o.releaseStartTimeSamples;if(g>=o.releaseDuration){for(let B=0;B<t.length;B++)t[B]=0;A.finished=!0;return}let d=jA-o.releaseStartDb;for(let B=0;B<t.length;B++){o.attenuation+=(o.attenuationTargetGain-o.attenuation)*E;let l=g/o.releaseDuration*d+o.releaseStartDb;o.currentReleaseGain=o.attenuation*ae(l+r),t[B]*=o.currentReleaseGain,o.currentSampleTime++,g++}o.currentReleaseGain<=ui&&(A.finished=!0);return}let a=0;switch(o.state){case 0:for(;o.currentSampleTime<o.delayEnd;)if(o.currentAttenuationDb=jA,t[a]=0,o.currentSampleTime++,++a>=t.length)return;o.state++;case 1:for(;o.currentSampleTime<o.attackEnd;){o.attenuation+=(o.attenuationTargetGain-o.attenuation)*E;let g=1-(o.attackEnd-o.currentSampleTime)/o.attackDuration;if(t[a]*=g*o.attenuation*ae(r),o.currentAttenuationDb=0,o.currentSampleTime++,++a>=t.length)return}o.state++;case 2:for(;o.currentSampleTime<o.holdEnd;)if(o.attenuation+=(o.attenuationTargetGain-o.attenuation)*E,t[a]*=o.attenuation*ae(r),o.currentAttenuationDb=0,o.currentSampleTime++,++a>=t.length)return;o.state++;case 3:for(;o.currentSampleTime<o.decayEnd;)if(o.attenuation+=(o.attenuationTargetGain-o.attenuation)*E,o.currentAttenuationDb=(1-(o.decayEnd-o.currentSampleTime)/o.decayDuration)*o.sustainDbRelative,t[a]*=o.attenuation*ae(o.currentAttenuationDb+r),o.currentSampleTime++,++a>=t.length)return;o.state++;case 4:for(o.canEndOnSilentSustain&&o.sustainDbRelative>=ts&&(A.finished=!0);;)if(o.attenuation+=(o.attenuationTargetGain-o.attenuation)*E,t[a]*=o.attenuation*ae(o.sustainDbRelative+r),o.currentAttenuationDb=o.sustainDbRelative,o.currentSampleTime++,++a>=t.length)return}}};function wo(e){let A=e.messageData,t=e.channelNumber,n;if(t>=0&&(n=this.workletProcessorChannels[t],n===void 0)){Y(`Trying to access channel ${t} which does not exist... ignoring!`);return}switch(e.messageType){case lA.noteOn:n.noteOn(...A);break;case lA.noteOff:n.noteOff(A);break;case lA.pitchWheel:n.pitchWheel(...A);break;case lA.ccChange:n.controllerChange(...A);break;case lA.customcCcChange:n.setCustomController(A[0],A[1]),n.updateChannelTuning();break;case lA.killNote:n.killNote(A);break;case lA.programChange:this.programChange(t,A[0],A[1]);break;case lA.channelPressure:n.channelPressure(A);break;case lA.polyPressure:n.polyPressure(...A);break;case lA.ccReset:t===ie?this.resetAllControllers():n.resetControllers();break;case lA.systemExclusive:this.systemExclusive(A[0],A[1]);break;case lA.setChannelVibrato:if(t===ie)for(let r=0;r<this.workletProcessorChannels.length;r++){let E=this.workletProcessorChannels[r];A.rate===-1?E.disableAndLockGSNRPN():E.setVibrato(A.depth,A.rate,A.delay)}else A.rate===-1?n.disableAndLockGSNRPN():n.setVibrato(A.depth,A.rate,A.delay);break;case lA.stopAll:t===ie?this.stopAllChannels(A===1):n.stopAllNotes(A===1);break;case lA.killNotes:this.voiceKilling(A);break;case lA.muteChannel:n.muteChannel(A);break;case lA.addNewChannel:this.createWorkletChannel(!0);break;case lA.debugMessage:this.debugMessage();break;case lA.setMasterParameter:let s=A[0],o=A[1];switch(s){case Ue.masterPan:this.setMasterPan(o);break;case Ue.mainVolume:this.setMasterGain(o);break;case Ue.voicesCap:this.voiceCap=o;break;case Ue.interpolationType:this.interpolationType=o;break;case Ue.midiSystem:this.setSystem(o)}break;case lA.setDrums:n.setDrums(A);break;case lA.transpose:t===ie?this.transposeAllChannels(A[0],A[1]):n.transposeChannel(A[0],A[1]);break;case lA.highPerformanceMode:this.highPerformanceMode=A;break;case lA.lockController:A[0]===ie?n.setPresetLock(A[1]):n.lockedControllers[A[0]]=A[1];break;case lA.sequencerSpecific:this.sequencer.processMessage(A.messageType,A.messageData);break;case lA.soundFontManager:try{this.soundfontManager.handleMessage(A[0],A[1])}catch(r){this.post({messageType:JA.soundfontError,messageData:r})}this.clearSoundFont(!0,!1);break;case lA.keyModifierManager:this.keyModifierManager.handleMessage(A[0],A[1]);break;case lA.requestSynthesizerSnapshot:this.sendSynthesizerSnapshot();break;case lA.setLogLevel:Ts(A[0],A[1],A[2],A[3]);break;case lA.setEffectsGain:this.reverbGain=A[0],this.chorusGain=A[1];break;case lA.destroyWorklet:this.alive=!1,this.destroyWorkletProcessor();break;default:Y("Unrecognized event:",A);break}}function Fo(e,A){this.enableEventSystem&&this.post({messageType:JA.eventCall,messageData:{eventName:e,eventData:A}})}function Ro(){if(!this.enableEventSystem)return;let e=this.workletProcessorChannels.map(A=>({voicesAmount:A.voices.length,pitchBend:A.midiControllers[MA+j.pitchWheel],pitchBendRangeSemitones:A.midiControllers[MA+j.pitchWheelRange]/128,isMuted:A.isMuted,isDrum:A.drumChannel,transposition:A.channelTransposeKeyShift+A.customControllers[cA.channelTransposeFine]/100}));this.post({messageType:JA.channelProperties,messageData:e})}function fi(e,A,t){let n=e,s=A<<7|t;return e===127&&A===127&&t===127?{midiNote:-1,centTuning:null}:{midiNote:n,centTuning:s*.0061}}var ns={SoundCanvasText:0,XGText:1,SoundCanvasDotDisplay:2};function Go(e,A=0){let t=e[0];if(!(this.deviceID!==ie&&e[1]!==127&&this.deviceID!==e[1]))switch(t){default:Y(`%cUnrecognized SysEx: %c${HA(e)}`,I.warn,I.unrecognized);break;case 126:case 127:switch(e[2]){case 4:let s;switch(e[3]){case 1:let o=e[5]<<7|e[4];this.setMIDIVolume(o/16384),p(`%cMaster Volume. Volume: %c${o}`,I.info,I.value);break;case 2:let E=((e[5]<<7|e[4])-8192)/8192;this.setMasterPan(E),p(`%cMaster Pan. Pan: %c${E}`,I.info,I.value);break;case 3:let a=(e[5]<<7|e[6])-8192;s=Math.floor(a/81.92),this.setMasterTuning(s),p(`%cMaster Fine Tuning. Cents: %c${s}`,I.info,I.value);break;case 4:s=(e[5]-64)*100,this.setMasterTuning(s),p(`%cMaster Coarse Tuning. Cents: %c${s}`,I.info,I.value);break;default:Y(`%cUnrecognized MIDI Device Control Real-time message: %c${HA(e)}`,I.warn,I.unrecognized)}break;case 9:e[3]===1?(p("%cGM1 system on",I.info),this.setSystem("gm")):e[3]===3?(p("%cGM2 system on",I.info),this.setSystem("gm2")):(p("%cGM system off, defaulting to GS",I.info),this.setSystem("gs"));break;case 8:switch(e[3]){case 2:case 7:let o=4;e[3]===7&&o++;let r=e[o++],E=e[o++];for(let g=0;g<E;g++)this.tunings[r][e[o++]]=fi(e[o++],e[o++],e[o++]);p(`%cSingle Note Tuning. Program: %c${r}%c Keys affected: %c${E}`,I.info,I.recognized,I.info,I.recognized);break;case 9:case 8:let a=new Int8Array(12);if(e[3]===8)for(let g=0;g<12;g++)a[g]=e[7+g]-64;else for(let g=0;g<24;g+=2){let d=(e[7+g]<<7|e[8+g])-8192;a[g/2]=Math.floor(d/81.92)}(e[4]&1)===1&&this.workletProcessorChannels[14+A].setOctaveTuning(a),(e[4]>>1&1)===1&&this.workletProcessorChannels[15+A].setOctaveTuning(a);for(let g=0;g<7;g++)(e[5]>>g&1)===1&&this.workletProcessorChannels[7+g+A].setOctaveTuning(a);for(let g=0;g<7;g++)(e[6]>>g&1)===1&&this.workletProcessorChannels[g+A].setOctaveTuning(a);p(`%cMIDI Octave Scale ${e[3]===8?"(1 byte)":"(2 bytes)"} tuning via Tuning: %c${a.join(" ")}`,I.info,I.value);break;default:Y(`%cUnrecognized MIDI Tuning standard message: %c${HA(e)}`,I.warn,I.unrecognized);break}break;default:Y(`%cUnrecognized MIDI Realtime/non realtime message: %c${HA(e)}`,I.warn,I.unrecognized)}break;case 65:let n=function(){Y(`%cUnrecognized Roland %cGS %cSysEx: %c${HA(e)}`,I.warn,I.recognized,I.warn,I.unrecognized)};if(e[2]===66&&e[3]===18){let s=e[7];if(e[6]===127){s===0?(p("%cGS Reset received!",I.info),this.resetAllControllers(!1),this.setSystem("gs")):s===127&&(p("%cGS system off, switching to GM2",I.info),this.resetAllControllers(!1),this.setSystem("gm2"));return}else if(e[4]===64){if((e[5]&16)>0){let o=[9,0,1,2,3,4,5,6,7,8,10,11,12,13,14,15][e[5]&15]+A,r=this.workletProcessorChannels[o];switch(e[6]){default:n();break;case 21:let E=s>0&&e[5]>>4;r.setDrums(E),p(`%cChannel %c${o}%c ${E?"is now a drum channel":"now isn't a drum channel"}%c via: %c${HA(e)}`,I.info,I.value,I.recognized,I.info,I.value);return;case 22:let a=s-64;r.transposeChannel(a),p(`%cChannel %c${o}%c pitch shift. Semitones %c${a}%c, with %c${HA(e)}`,I.info,I.recognized,I.info,I.value,I.info,I.value);return;case 28:let g=s;g===0?(r.randomPan=!0,p(`%cRandom pan is set to %cON%c for %c${o}`,I.info,I.recognized,I.info,I.value)):(r.randomPan=!1,r.controllerChange(y.pan,g));break;case 33:r.controllerChange(y.chorusDepth,s);break;case 34:r.controllerChange(y.reverbDepth,s);break;case 64:case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:let d=e.length-9,B=new Int8Array(12);for(let h=0;h<d;h++)B[h]=e[h+7]-64;r.setOctaveTuning(B);let l=s-64;p(`%cChannel %c${o}%c octave scale tuning. Cents %c${B.join(" ")}%c, with %c${HA(e)}`,I.info,I.recognized,I.info,I.value,I.info,I.value),r.setTuning(l);break}return}else if(e[5]===0&&e[6]===6){p(`%cRoland GS Master Pan set to: %c${s}%c with: %c${HA(e)}`,I.info,I.value,I.info,I.value),this.setMasterPan((s-64)/64);return}else if(e[5]===0&&e[6]===5){let o=s-64;p(`%cRoland GS Master Key-Shift set to: %c${o}%c with: %c${HA(e)}`,I.info,I.value,I.info,I.value),this.setMasterTuning(o*100);return}else if(e[5]===0&&e[6]===4){p(`%cRoland GS Master Volume set to: %c${s}%c with: %c${HA(e)}`,I.info,I.value,I.info,I.value),this.setMIDIVolume(s/127);return}}n();return}else if(e[2]===69&&e[3]===18){if(e[4]===16&&e[6]===0)if(e[5]===0){let s=new Uint8Array(e.slice(7,e.length-2));this.callEvent("synthdisplay",{displayData:s,displayType:ns.SoundCanvasText})}else if(e[5]===1){let s=new Uint8Array(e.slice(7,e.length-3));this.callEvent("synthdisplay",{displayData:s,displayType:ns.SoundCanvasDotDisplay}),p(`%cRoland SC Display Dot Matrix via: %c${HA(e)}`,I.info,I.value)}else n()}else if(e[2]===22&&e[3]===18&&e[4]===16){this.setMIDIVolume(e[7]/100),p(`%cRoland Master Volume control set to: %c${e[7]}%c via: %c${HA(e)}`,I.info,I.value,I.info,I.value);return}else{Y(`%cUnrecognized Roland SysEx: %c${HA(e)}`,I.warn,I.unrecognized);return}break;case 67:if(e[2]===76)if(e[3]===0&&e[4]===0)switch(e[5]){case 4:let s=e[6];this.setMIDIVolume(s/127),p(`%cXG master volume. Volume: %c${s}`,I.info,I.recognized);break;case 6:let o=e[6]-64;this.transposeAllChannels(o),p(`%cXG master transpose. Volume: %c${o}`,I.info,I.recognized);break;case 126:p("%cXG system on",I.info),this.resetAllControllers(!1),this.setSystem("xg");break}else if(e[3]===8){if(!RA(this.system))return;let s=e[4]+A;if(s>=this.workletProcessorChannels.length)return;let o=this.workletProcessorChannels[s],r=e[6];switch(e[5]){case 1:o.controllerChange(y.bankSelect,r);break;case 2:o.controllerChange(y.lsbForControl0BankSelect,r);break;case 3:o.programChange(r);break;case 8:if(o.drumChannel)return;let E=r-64;o.channelTransposeKeyShift=E;break;case 11:o.controllerChange(y.mainVolume,r);break;case 14:let a=r;a===0?(o.randomPan=!0,p(`%cRandom pan is set to %cON%c for %c${s}`,I.info,I.recognized,I.info,I.value)):o.controllerChange(y.pan,a);break;case 19:o.controllerChange(y.reverbDepth,r);break;case 18:o.controllerChange(y.chorusDepth,r);break;default:Y(`%cUnrecognized Yamaha XG Part Setup: %c${e[5].toString(16).toUpperCase()}`,I.warn,I.unrecognized)}}else if(e[3]===6&&e[4]===0){let s=new Uint8Array(e.slice(5,e.length-1));this.callEvent("synthdisplay",{displayData:s,displayType:ns.XGText})}else RA(this.system)&&Y(`%cUnrecognized Yamaha XG SysEx: %c${HA(e)}`,I.warn,I.unrecognized);else RA(this.system)&&Y(`%cUnrecognized Yamaha SysEx: %c${HA(e)}`,I.warn,I.unrecognized);break}}function Mo(e){this.midiVolume=e,this.setMasterPan(this.pan)}function xo(e){this.masterGain=e*ss,this.setMasterPan(this.pan)}function No(e){this.pan=e,e=e/2+.5,this.panLeft=1-e,this.panRight=e}var Gt={reloadSoundFont:0,addNewSoundFont:2,deleteSoundFont:3,rearrangeSoundFonts:4};function bo(){let e=4;for(let n of this.instruments)e+=n.instrumentZones.reduce((s,o)=>(o.generators=o.generators.filter(r=>r.generatorType!==i.sampleID&&r.generatorType!==i.keyRange&&r.generatorType!==i.velRange),(o.velRange.max!==127||o.velRange.min!==0)&&o.generators.unshift(new U(i.velRange,o.velRange.max<<8|Math.max(o.velRange.min,0),!1)),(o.keyRange.max!==127||o.keyRange.min!==0)&&o.generators.unshift(new U(i.keyRange,o.keyRange.max<<8|Math.max(o.keyRange.min,0),!1)),o.isGlobal||o.generators.push(new U(i.sampleID,this.samples.indexOf(o.sample),!1)),o.generators.length*4+s),0);let A=new L(e),t=0;for(let n of this.instruments)for(let s of n.instrumentZones){s.generatorZoneStartIndex=t;for(let o of s.generators)H(A,o.generatorType),H(A,o.generatorValue),t++}return AA(A,0),uA(new hA("igen",A.length,A))}function Lo(e,A,t,n,s){let o=this.samples.map((g,d)=>{t&&g.compressSample(n,s);let B=g.getRawData();return p(`%cEncoded sample %c${d}. ${g.sampleName}%c of %c${this.samples.length}%c. Compressed: %c${g.isCompressed}%c.`,I.info,I.recognized,I.info,I.recognized,I.info,g.isCompressed?I.recognized:I.unrecognized,I.info),B}),r=this.samples.reduce((g,d,B)=>g+o[B].length+46,0),E=new L(r);this.samples.forEach((g,d)=>{let B=o[d],l,h,m=B.length;g.isCompressed?(l=E.currentIndex,h=l+B.length):(l=E.currentIndex/2,h=l+B.length/2,m+=46),e.push(l),E.set(B,E.currentIndex),E.currentIndex+=m,A.push(h)});let a=uA(new hA("smpl",E.length,E),new L([115,100,116,97]));return uA(new hA("LIST",a.length,a))}function Uo(e,A){let n=new L(46*(this.samples.length+1));return this.samples.forEach((s,o)=>{TA(n,s.sampleName,20);let r=e[o];AA(n,r);let E=A[o];AA(n,E);let a=s.sampleLoopStartIndex+r,g=s.sampleLoopEndIndex+r;s.isCompressed&&(a-=r,g-=r),AA(n,a),AA(n,g),AA(n,s.sampleRate),n[n.currentIndex++]=s.samplePitch,n[n.currentIndex++]=s.samplePitchCorrection,H(n,s.sampleLink),H(n,s.sampleType)}),TA(n,"EOS",46),uA(new hA("shdr",n.length,n))}function To(){let e=10;for(let n of this.instruments)e+=n.instrumentZones.reduce((s,o)=>o.modulators.length*10+s,0);let A=new L(e),t=0;for(let n of this.instruments)for(let s of n.instrumentZones){s.modulatorZoneStartIndex=t;for(let o of s.modulators)H(A,o.sourceEnum),H(A,o.modulatorDestination),H(A,o.transformAmount),H(A,o.secondarySourceEnum),H(A,o.transformType),t++}return Xe(A,0,10),uA(new hA("imod",A.length,A))}function vo(){let e=this.instruments.reduce((o,r)=>r.instrumentZones.length*4+o,4),A=new L(e),t=0,n=0,s=0;for(let o of this.instruments){o.instrumentZoneIndex=t;for(let r of o.instrumentZones)r.zoneID=t,H(A,n),H(A,s),n+=r.generators.length,s+=r.modulators.length,t++}return H(A,n),H(A,s),uA(new hA("ibag",A.length,A))}function Ho(){let e=this.instruments.length*22+22,A=new L(e),t=0,n=0;for(let s of this.instruments)TA(A,s.instrumentName,20),H(A,t),t+=s.instrumentZones.length,s.instrumentID=n,n++;return TA(A,"EOI",20),H(A,t),uA(new hA("inst",A.length,A))}function Yo(){let e=4;for(let n of this.presets)e+=n.presetZones.reduce((s,o)=>(o.generators=o.generators.filter(r=>r.generatorType!==i.instrument&&r.generatorType!==i.keyRange&&r.generatorType!==i.velRange),(o.velRange.max!==127||o.velRange.min!==0)&&o.generators.unshift(new U(i.velRange,o.velRange.max<<8|Math.max(o.velRange.min,0),!1)),(o.keyRange.max!==127||o.keyRange.min!==0)&&o.generators.unshift(new U(i.keyRange,o.keyRange.max<<8|Math.max(o.keyRange.min,0),!1)),o.isGlobal||o.generators.push(new U(i.instrument,this.instruments.indexOf(o.instrument),!1)),o.generators.length*4+s),0);let A=new L(e),t=0;for(let n of this.presets)for(let s of n.presetZones){s.generatorZoneStartIndex=t;for(let o of s.generators)H(A,o.generatorType),H(A,o.generatorValue);t+=s.generators.length}return H(A,0),H(A,0),uA(new hA("pgen",A.length,A))}function Jo(){let e=10;for(let n of this.presets)e+=n.presetZones.reduce((s,o)=>o.modulators.length*10+s,0);let A=new L(e),t=0;for(let n of this.presets)for(let s of n.presetZones){s.modulatorZoneStartIndex=t;for(let o of s.modulators)H(A,o.sourceEnum),H(A,o.modulatorDestination),H(A,o.transformAmount),H(A,o.secondarySourceEnum),H(A,o.transformType),t++}return Xe(A,0,10),uA(new hA("pmod",A.length,A))}function Ko(){let e=this.presets.reduce((o,r)=>r.presetZones.length*4+o,4),A=new L(e),t=0,n=0,s=0;for(let o of this.presets){o.presetZoneStartIndex=t;for(let r of o.presetZones)r.zoneID=t,H(A,n),H(A,s),n+=r.generators.length,s+=r.modulators.length,t++}return H(A,n),H(A,s),uA(new hA("pbag",A.length,A))}function qo(){let e=this.presets.length*38+38,A=new L(e),t=0;for(let n of this.presets)TA(A,n.presetName,20),H(A,n.program),H(A,n.bank),H(A,t),AA(A,n.library),AA(A,n.genre),AA(A,n.morphology),t+=n.presetZones.length;return TA(A,"EOP",20),H(A,0),H(A,0),H(A,t),AA(A,0),AA(A,0),AA(A,0),uA(new hA("phdr",A.length,A))}var mi={compress:!1,compressionQuality:.5,compressionFunction:void 0};function Oo(e=mi){if(e.compress&&typeof e.compressionFunction!="function")throw new TypeError("No compression function supplied but compression enabled.");FA("%cSaving soundfont...",I.info),p(`%cCompression: %c${e?.compress||"false"}%c quality: %c${e?.compressionQuality||"none"}`,I.info,I.recognized,I.info,I.recognized),p("%cWriting INFO...",I.info);let A=[];this.soundFontInfo.ISFT="SpessaSynth",e?.compress&&(this.soundFontInfo.ifil="3.0");for(let[b,G]of Object.entries(this.soundFontInfo))if(b==="ifil"||b==="iver"){let C=parseInt(G.split(".")[0]),M=parseInt(G.split(".")[1]),q=new L(4);H(q,C),H(q,M),A.push(uA(new hA(b,4,q)))}else if(b==="DMOD")A.push(uA(new hA(b,G.length,G)));else{let C=new L(G.length);TA(C,G),A.push(uA(new hA(b,G.length,C)))}let t=wA([new L([73,78,70,79]),...A]),n=uA(new hA("LIST",t.length,t));p("%cWriting SDTA...",I.info);let s=[],o=[],r=Lo.call(this,s,o,e?.compress,e?.compressionQuality??.5,e.compressionFunction);p("%cWriting PDTA...",I.info),p("%cWriting SHDR...",I.info);let E=Uo.call(this,s,o);p("%cWriting IGEN...",I.info);let a=bo.call(this);p("%cWriting IMOD...",I.info);let g=To.call(this);p("%cWriting IBAG...",I.info);let d=vo.call(this);p("%cWriting INST...",I.info);let B=Ho.call(this),l=Yo.call(this);p("%cWriting PMOD...",I.info);let h=Jo.call(this);p("%cWriting PBAG...",I.info);let m=Ko.call(this);p("%cWriting PHDR...",I.info);let u=qo.call(this),S=wA([new L([112,100,116,97]),u,m,h,l,B,d,g,a,E]),w=uA(new hA("LIST",S.length,S));p("%cWriting the output file...",I.info);let k=wA([new L([115,102,98,107]),n,r,w]),x=uA(new hA("RIFF",k.length,k));return p(`%cSaved succesfully! Final file size: %c${x.length}`,I.info,I.recognized),V(),x}var Mt=class{velRange={min:-1,max:127};keyRange={min:-1,max:127};isGlobal=!1;generators=[];modulators=[];get hasKeyRange(){return this.keyRange.min!==-1}get hasVelRange(){return this.velRange.min!==-1}getGeneratorValue(A,t){return this.generators.find(n=>n.generatorType===A)?.generatorValue??t}};var OA=class extends Mt{sample=void 0;useCount=0;deleteZone(){this.useCount--,!this.isGlobal&&this.sample.useCount--}},Te=class extends Mt{instrument=void 0;deleteZone(){this.isGlobal||this.instrument.removeUseCount()}};var pi=new Set([i.velRange,i.keyRange,i.instrument,i.exclusiveClass,i.endOper,i.sampleModes,i.startloopAddrsOffset,i.startloopAddrsCoarseOffset,i.endloopAddrsOffset,i.endloopAddrsCoarseOffset,i.startAddrsOffset,i.startAddrsCoarseOffset,i.endAddrOffset,i.endAddrsCoarseOffset,i.initialAttenuation,i.fineTune,i.coarseTune,i.keyNumToVolEnvHold,i.keyNumToVolEnvDecay,i.keyNumToModEnvHold,i.keyNumToModEnvDecay]);function Po(e,A=!0){function t(B,l){B.push(...l.filter(h=>!B.find(m=>m.generatorType===h.generatorType)))}function n(B,l){return{min:Math.max(B.min,l.min),max:Math.min(B.max,l.max)}}function s(B,l){B.push(...l.filter(h=>!B.find(m=>_.isIdentical(h,m))))}let o=[],r=[],E=[],a={min:0,max:127},g={min:0,max:127},d=e.presetZones.find(B=>B.isGlobal);d&&(r.push(...d.generators),E.push(...d.modulators),a=d.keyRange,g=d.velRange);for(let B of e.presetZones){if(B.isGlobal)continue;let l=B.keyRange;B.hasKeyRange||(l=a);let h=B.velRange;B.hasVelRange||(h=g);let m=B.generators.map(C=>new U(C.generatorType,C.generatorValue));t(m,r);let u=[...B.modulators];s(u,E);let S=B.instrument.instrumentZones,w=[],k=[],x={min:0,max:127},b={min:0,max:127},G=S.find(C=>C.isGlobal);G&&(w.push(...G.generators),k.push(...G.modulators),x=G.keyRange,b=G.velRange);for(let C of S){if(C.isGlobal)continue;let M=C.keyRange;C.hasKeyRange||(M=x);let q=C.velRange;if(C.hasVelRange||(q=b),M=n(M,l),q=n(q,h),M.max<M.min||q.max<q.min)continue;let gA=C.generators.map(sA=>new U(sA.generatorType,sA.generatorValue));t(gA,w);let tA=[...C.modulators];s(tA,k);let T=[...tA];for(let sA of u){let oA=T.findIndex(EA=>_.isIdentical(sA,EA));oA!==-1?T[oA]=T[oA].sumTransform(sA):T.push(sA)}let $=gA.map(sA=>new U(sA.generatorType,sA.generatorValue));for(let sA of m){if(sA.generatorType===i.velRange||sA.generatorType===i.keyRange||sA.generatorType===i.instrument||sA.generatorType===i.endOper||sA.generatorType===i.sampleModes)continue;let oA=gA.findIndex(EA=>EA.generatorType===sA.generatorType);if(oA!==-1){let EA=$[oA].generatorValue+sA.generatorValue;$[oA]=new U(sA.generatorType,EA)}else{let EA=X[sA.generatorType].def+sA.generatorValue;$.push(new U(sA.generatorType,EA))}}$=$.filter(sA=>sA.generatorType!==i.sampleID&&sA.generatorType!==i.keyRange&&sA.generatorType!==i.velRange&&sA.generatorType!==i.endOper&&sA.generatorType!==i.instrument&&sA.generatorValue!==X[sA.generatorType].def);let BA=new OA;BA.keyRange=M,BA.velRange=q,BA.keyRange.min===0&&BA.keyRange.max===127&&(BA.keyRange.min=-1),BA.velRange.min===0&&BA.velRange.max===127&&(BA.velRange.min=-1),BA.isGlobal=!1,BA.sample=C.sample,BA.generators=$,BA.modulators=T,o.push(BA)}}if(A){let B=new OA;B.isGlobal=!0;for(let m=0;m<58;m++){if(pi.has(m))continue;let u={},S=X[m]?.def||0;u[S]=0;for(let w of o){let k=w.generators.find(G=>G.generatorType===m);if(k){let G=k.generatorValue;u[G]===void 0?u[G]=1:u[G]++}else u[S]++;let x;switch(m){default:continue;case i.decayVolEnv:x=i.keyNumToVolEnvDecay;break;case i.holdVolEnv:x=i.keyNumToVolEnvHold;break;case i.decayModEnv:x=i.keyNumToModEnvDecay;break;case i.holdModEnv:x=i.keyNumToModEnvHold}if(w.generators.find(G=>G.generatorType===x)!==void 0){u={};break}}if(Object.keys(u).length>0){let w=Object.entries(u).reduce((x,b)=>x[1]<b[1]?b:x,[0,0]),k=parseInt(w[0]);k!==S&&B.generators.push(new U(m,k)),o.forEach(x=>{let b=x.generators.findIndex(G=>G.generatorType===m);b!==-1?x.generators[b].generatorValue===k&&x.generators.splice(b,1):k!==S&&x.generators.push(new U(m,S))})}}let h=o.find(m=>!m.isGlobal).modulators.map(m=>_.copy(m));for(let m of h){let u=!0;for(let S of o){if(S.isGlobal||!u)continue;S.modulators.find(k=>_.isIdentical(k,m))||(u=!1)}if(u===!0){B.modulators.push(_.copy(m));for(let S of o){let w=S.modulators.find(k=>_.isIdentical(k,m));w.transformAmount===m.transformAmount&&S.modulators.splice(S.modulators.indexOf(w),1)}}}o.splice(0,0,B)}return o}var Vo=20;function Cn(e,A,t,n,s,o,r){let E=r===0?0:1,a=new L(Vo+E*16);AA(a,Vo),H(a,A),H(a,t);let g=n*.4,d=Math.floor(g*-65536);AA(a,d),AA(a,2);let B=o-s,l=0;switch(r){default:case 0:E=0;break;case 1:l=0,E=1;break;case 3:l=1,E=1}return AA(a,E),E===1&&(AA(a,16),AA(a,l),AA(a,s),AA(a,B)),z("wsmp",a)}var P={none:0,modLfo:1,velocity:2,keyNum:3,volEnv:4,modEnv:5,pitchWheel:6,polyPressure:7,channelPressure:8,vibratoLfo:9,modulationWheel:129,volume:135,pan:138,expression:139,chorus:219,reverb:221,pitchWheelRange:256,fineTune:257,coarseTune:258},En=new _(219,0,i.reverbEffectsSend,1e3,0),Bn=new _(221,0,i.chorusEffectsSend,1e3,0),hn=new _(129,0,i.vibLfoToPitch,0,0),cn=new _(13,0,i.vibLfoToPitch,0,0);var R={none:0,gain:1,reserved:2,pitch:3,pan:4,keyNum:5,chorusSend:128,reverbSend:129,modLfoFreq:260,modLfoDelay:261,vibLfoFreq:276,vibLfoDelay:277,volEnvAttack:518,volEnvDecay:519,volEnvRelease:521,volEnvSustain:522,volEnvDelay:523,volEnvHold:524,modEnvAttack:778,modEnvDecay:779,modEnvRelease:781,modEnvSustain:782,modEnvDelay:783,modEnvHold:784,filterCutoff:1280,filterQ:1281};var xt=class{source;control;destination;scale;transform;constructor(A,t,n,s,o){this.source=A,this.control=t,this.destination=n,this.scale=s,this.transform=o}writeArticulator(){let A=new L(12);return H(A,this.source),H(A,this.control),H(A,this.destination),H(A,this.transform),AA(A,this.scale<<16),A}};function Zo(e,A){if(e)switch(A){default:return;case y.modulationWheel:return P.modulationWheel;case y.mainVolume:return P.volume;case y.pan:return P.pan;case y.expressionController:return P.expression;case y.chorusDepth:return P.chorus;case y.reverbDepth:return P.reverb}else switch(A){default:return;case j.noteOnKeyNum:return P.keyNum;case j.noteOnVelocity:return P.velocity;case j.noController:return P.none;case j.polyPressure:return P.polyPressure;case j.channelPressure:return P.channelPressure;case j.pitchWheel:return P.pitchWheel;case j.pitchWheelRange:return P.pitchWheelRange}}function Xo(e,A){switch(e){default:return;case i.initialAttenuation:return{dest:R.gain,amount:-A};case i.fineTune:return R.pitch;case i.pan:return R.pan;case i.keyNum:return R.keyNum;case i.reverbEffectsSend:return R.reverbSend;case i.chorusEffectsSend:return R.chorusSend;case i.freqModLFO:return R.modLfoFreq;case i.delayModLFO:return R.modLfoDelay;case i.delayVibLFO:return R.vibLfoDelay;case i.freqVibLFO:return R.vibLfoFreq;case i.delayVolEnv:return R.volEnvDelay;case i.attackVolEnv:return R.volEnvAttack;case i.holdVolEnv:return R.volEnvHold;case i.decayVolEnv:return R.volEnvDecay;case i.sustainVolEnv:return{dest:R.volEnvSustain,amount:1e3-A};case i.releaseVolEnv:return R.volEnvRelease;case i.delayModEnv:return R.modEnvDelay;case i.attackModEnv:return R.modEnvAttack;case i.holdModEnv:return R.modEnvHold;case i.decayModEnv:return R.modEnvDecay;case i.sustainModEnv:return{dest:R.modEnvSustain,amount:1e3-A};case i.releaseModEnv:return R.modEnvRelease;case i.initialFilterFc:return R.filterCutoff;case i.initialFilterQ:return R.filterQ}}function Wo(e,A){switch(e){default:return;case i.modEnvToFilterFc:return{source:P.modEnv,dest:R.filterCutoff,amt:A,isBipolar:!1};case i.modEnvToPitch:return{source:P.modEnv,dest:R.pitch,amt:A,isBipolar:!1};case i.modLfoToFilterFc:return{source:P.modLfo,dest:R.filterCutoff,amt:A,isBipolar:!0};case i.modLfoToVolume:return{source:P.modLfo,dest:R.gain,amt:A,isBipolar:!0};case i.modLfoToPitch:return{source:P.modLfo,dest:R.pitch,amt:A,isBipolar:!0};case i.vibLfoToPitch:return{source:P.vibratoLfo,dest:R.pitch,amt:A,isBipolar:!0};case i.keyNumToVolEnvHold:return{source:P.keyNum,dest:R.volEnvHold,amt:A,isBipolar:!0};case i.keyNumToVolEnvDecay:return{source:P.keyNum,dest:R.volEnvDecay,amt:A,isBipolar:!0};case i.keyNumToModEnvHold:return{source:P.keyNum,dest:R.modEnvHold,amt:A,isBipolar:!0};case i.keyNumToModEnvDecay:return{source:P.keyNum,dest:R.modEnvDecay,amt:A,isBipolar:!0};case i.scaleTuning:return{source:P.keyNum,dest:R.pitch,amt:A*128,isBipolar:!1}}}function _o(e){let A=Xo(e.generatorType,e.generatorValue),t=A,n=0,s=e.generatorValue;A?.amount!==void 0&&(s=A.amount,t=A.dest);let o=Wo(e.generatorType,e.generatorValue);if(o!==void 0)s=o.amt,t=o.dest,n=o.source;else if(t===void 0){Y(`Invalid generator type: ${e.generatorType}`);return}return new xt(n,0,t,s,0)}function zo(e){if(e.transformType!==0){Y("Other transform types are not supported.");return}let A=Zo(e.sourceUsesCC,e.sourceIndex),t=e.sourceCurveType,n=e.sourcePolarity,s=e.sourceDirection;if(A===void 0){Y(`Invalid source: ${e.sourceIndex}, CC: ${e.sourceUsesCC}`);return}e.modulatorDestination===i.initialAttenuation&&(s=s===1?0:1);let o=Zo(e.secSrcUsesCC,e.secSrcIndex),r=e.secSrcCurveType,E=e.secSrcPolarity,a=e.secSrcDirection;if(o===void 0){Y(`Invalid secondary source: ${e.secSrcIndex}, CC: ${e.secSrcUsesCC}`);return}let g=Xo(e.modulatorDestination,e.transformAmount),d=g,B=e.transformAmount;g?.dest!==void 0&&(d=g.dest,B=g.amount);let l=Wo(e.modulatorDestination,e.transformAmount);if(l!==void 0)B=l.amt,o=A,r=t,E=n,a=s,t=GA.linear,n=l.isBipolar?1:0,s=0,A=l.source,d=l.dest;else if(d===void 0){Y(`Invalid destination: ${e.modulatorDestination}`);return}let h=0;return h|=r<<4,h|=E<<8,h|=a<<9,h|=t,h|=n<<14,h|=s<<15,new xt(A,o,d,B,h)}var yi=new Set([i.sampleModes,i.initialAttenuation,i.keyRange,i.velRange,i.sampleID,i.fineTune,i.coarseTune,i.startAddrsOffset,i.startAddrsCoarseOffset,i.endAddrOffset,i.endAddrsCoarseOffset,i.startloopAddrsOffset,i.startloopAddrsCoarseOffset,i.endloopAddrsOffset,i.endloopAddrsCoarseOffset,i.overridingRootKey,i.exclusiveClass]);function ln(e){for(let o=0;o<e.generators.length;o++){let r=e.generators[o];(r.generatorType===i.delayVolEnv||r.generatorType===i.attackVolEnv||r.generatorType===i.holdVolEnv||r.generatorType===i.decayVolEnv||r.generatorType===i.releaseVolEnv||r.generatorType===i.delayModEnv||r.generatorType===i.attackModEnv||r.generatorType===i.holdModEnv||r.generatorType===i.decayModEnv)&&(e.generators[o]=new U(r.generatorType,Math.min(r.generatorValue,6386),!1))}for(let o=0;o<e.generators.length;o++){let r=e.generators[o],E;switch(r.generatorType){default:continue;case i.keyNumToVolEnvDecay:E=i.decayVolEnv;break;case i.keyNumToVolEnvHold:E=i.holdVolEnv;break;case i.keyNumToModEnvDecay:E=i.decayModEnv;break;case i.keyNumToModEnvHold:E=i.holdModEnv}let a=e.generators.find(m=>m.generatorType===E);if(a===void 0)continue;let g=r.generatorValue*-128,d=60/128*g,B=a.generatorValue-d,l=e.generators.indexOf(r),h=e.generators.indexOf(a);e.generators[h]=new U(E,B,!1),e.generators[l]=new U(r.generatorType,g,!1)}let A=e.generators.reduce((o,r)=>{if(yi.has(r.generatorType))return o;let E=_o(r);return E!==void 0?(o.push(E),p("%cSucceeded converting to DLS Articulator!",I.recognized)):Y("Failed converting to DLS Articulator!"),o},[]),t=e.modulators.reduce((o,r)=>{if(_.isIdentical(r,Bn,!0)||_.isIdentical(r,En,!0)||_.isIdentical(r,hn,!0)||_.isIdentical(r,cn,!0))return o;let E=zo(r);return E!==void 0?(o.push(E),p("%cSucceeded converting to DLS Articulator!",I.recognized)):Y("Failed converting to DLS Articulator!"),o},[]);A.push(...t);let n=new L(8);AA(n,8),AA(n,A.length);let s=A.map(o=>o.writeArticulator());return z("art2",wA([n,...s]))}function jo(e,A){let t=new L(12);H(t,Math.max(e.keyRange.min,0)),H(t,e.keyRange.max),H(t,Math.max(e.velRange.min,0)),H(t,e.velRange.max),H(t,0);let n=e.getGeneratorValue(i.exclusiveClass,0);H(t,n),H(t,0);let s=z("rgnh",t),o=e.getGeneratorValue(i.overridingRootKey,e.sample.samplePitch);e.getGeneratorValue(i.scaleTuning,A.getGeneratorValue(i.scaleTuning,100))===0&&e.keyRange.max-e.keyRange.min===0&&(o=e.keyRange.min);let E=Cn(e.sample,o,e.getGeneratorValue(i.fineTune,0)+e.getGeneratorValue(i.coarseTune,0)*100+e.sample.samplePitchCorrection,e.getGeneratorValue(i.initialAttenuation,0),e.sample.sampleLoopStartIndex+e.getGeneratorValue(i.startloopAddrsOffset,0)+e.getGeneratorValue(i.startloopAddrsCoarseOffset,0)*32768,e.sample.sampleLoopEndIndex+e.getGeneratorValue(i.endloopAddrsOffset,0)+e.getGeneratorValue(i.endloopAddrsCoarseOffset,0)*32768,e.getGeneratorValue(i.sampleModes,0)),a=new L(12);H(a,0),H(a,0),AA(a,1),AA(a,this.samples.indexOf(e.sample));let g=z("wlnk",a),d=new L(0);if(e.modulators.length+e.generators.length>0){let B=ln(e);d=z("lar2",B,!1,!0)}return z("rgn2",wA([s,E,g,d]),!1,!0)}function $o(e){FA(`%cWriting %c${e.presetName}%c...`,I.info,I.recognized,I.info);let A=Po(e),t=A.reduce((l,h)=>h.isGlobal?l:l+1,0),n=new L(12);AA(n,t);let s=(e.bank&127)<<8;e.bank===128&&(s|=1<<31),AA(n,s),AA(n,e.program&127);let o=z("insh",n),r=new L(0),E=A.find(l=>l.isGlobal===!0);if(E){let l=ln(E);r=z("lar2",l,!1,!0)}let a=wA(A.reduce((l,h)=>(h.isGlobal||l.push(jo.apply(this,[h,E])),l),[])),g=z("lrgn",a,!1,!0),d=z("INAM",fe(e.presetName)),B=z("INFO",d,!1,!0);return V(),z("ins ",wA([o,g,r,B]),!1,!0)}function Ar(){let e=wA(this.presets.map(A=>$o.apply(this,[A])));return z("lins",e,!1,!0)}function er(e){let A=new L(18);H(A,1),H(A,1),AA(A,e.sampleRate),AA(A,e.sampleRate*2),H(A,2),H(A,16);let t=z("fmt ",A),n=1;e.sampleLoopStartIndex+Math.abs(e.getAudioData().length-e.sampleLoopEndIndex)<2&&(n=0);let s=Cn(e,e.samplePitch,e.samplePitchCorrection,0,e.sampleLoopStartIndex,e.sampleLoopEndIndex,n),o=e.getAudioData(),r;if(e.isCompressed){let g=new Int16Array(o.length);for(let d=0;d<o.length;d++)g[d]=o[d]*32767;r=z("data",new L(g.buffer))}else r=z("data",e.getRawData());let E=z("INAM",fe(e.sampleName)),a=z("INFO",E,!1,!0);return p(`%cSaved %c${e.sampleName}%c succesfully!`,I.recognized,I.value,I.recognized),z("wave",wA([t,s,r,a]),!1,!0)}function tr(){let e=0,A=[],t=this.samples.map(n=>{let s=er(n);return A.push(e),e+=s.length,s});return{data:z("wvpl",wA(t),!1,!0),indexes:A}}function nr(){FA("%cSaving DLS...",I.info);let e=new L(4);AA(e,this.presets.length);let A=z("colh",e);FA("%cWriting instruments...",I.info);let t=Ar.apply(this);p("%cSuccess!",I.recognized),V(),FA("%cWriting WAVE samples...",I.info);let n=tr.apply(this),s=n.data,o=n.indexes;p("%cSucceeded!",I.recognized),V();let r=new L(8+4*o.length);AA(r,8),AA(r,o.length);for(let B of o)AA(r,B);let E=z("ptbl",r);this.soundFontInfo.ICMT=(this.soundFontInfo.ICMT||"Soundfont")+`
Converted from SF2 to DLS using SpessaSynth`,this.soundFontInfo.ISFT="SpessaSynth";let a=[];for(let[B,l]of Object.entries(this.soundFontInfo))B!=="ICMT"&&B!=="INAM"&&B!=="ICRD"&&B!=="IENG"&&B!=="ICOP"&&B!=="ISFT"&&B!=="ISBJ"||a.push(z(B,fe(l),!0));let g=z("INFO",wA(a),!1,!0),d=new L(A.length+t.length+E.length+s.length+g.length+4);return TA(d,"DLS "),d.set(wA([A,t,E,s,g]),4),p("%cSaved succesfully!",I.recognized),V(),z("RIFF",d)}var Si=48e3,ve=class{constructor(A,t,n,s,o,r,E,a){this.sampleName=A,this.sampleRate=t,this.samplePitch=n,this.samplePitchCorrection=s,this.sampleLink=o,this.sampleType=r,this.sampleLoopStartIndex=E,this.sampleLoopEndIndex=a,this.isCompressed=(r&16)>0,this.compressedData=void 0,this.useCount=0,this.sampleData=void 0}getRawData(){let A=new Uint8Array(this.sampleData.length*2);for(let t=0;t<this.sampleData.length;t++){let n=Math.floor(this.sampleData[t]*32768);A[t*2]=n&255,A[t*2+1]=n>>8&255}return A}resampleData(A){let t=this.getAudioData(),n=A/this.sampleRate,s=new Float32Array(Math.floor(t.length*n));for(let o=0;o<s.length;o++)s[o]=t[Math.floor(o*(1/n))];t=s,this.sampleRate=A,this.sampleLoopStartIndex=Math.floor(this.sampleLoopStartIndex*n),this.sampleLoopEndIndex=Math.floor(this.sampleLoopEndIndex*n),this.sampleData=t}compressSample(A,t){if(!this.isCompressed)try{let n=this.getAudioData();(this.sampleRate<8e3||this.sampleRate>96e3)&&(this.resampleData(Si),n=this.getAudioData()),this.compressedData=t([n],1,this.sampleRate,A),this.sampleType|=16,this.isCompressed=!0}catch{Y(`Failed to compress ${this.sampleName}. Leaving as uncompressed!`),this.isCompressed=!1,this.compressedData=void 0,this.sampleType&=239}}getAudioData(){return this.sampleData}};var He=class{constructor(){this.instrumentName="",this.instrumentZones=[],this._useCount=0}get useCount(){return this._useCount}addUseCount(){this._useCount++,this.instrumentZones.forEach(A=>A.useCount++)}removeUseCount(){this._useCount--;for(let A=0;A<this.instrumentZones.length;A++)this.safeDeleteZone(A)&&A--}deleteInstrument(){this.instrumentZones.forEach(A=>A.deleteZone()),this.instrumentZones.length=0}safeDeleteZone(A){return this.instrumentZones[A].useCount--,this.instrumentZones[A].useCount<1?(this.deleteZone(A),!0):!1}deleteZone(A){this.instrumentZones[A].deleteZone(),this.instrumentZones.splice(A,1)}};var Ye=class{constructor(A){this.presetName="",this.program=0,this.bank=0,this.presetZones=[],this.sampleIDOffset=0,this.foundSamplesAndGenerators=[];for(let t=0;t<128;t++)this.foundSamplesAndGenerators[t]=[];this.library=0,this.genre=0,this.morphology=0,this.defaultModulators=A}isDrumPreset(A,t=!1){return this.bank===128||A&&re(this.bank)&&(this.bank!==126||t)}deletePreset(){this.presetZones.forEach(A=>A.deleteZone()),this.presetZones.length=0}deleteZone(A){this.presetZones[A].deleteZone(),this.presetZones.splice(A,1)}preload(A,t){for(let n=A;n<t+1;n++)for(let s=0;s<128;s++)this.getSamplesAndGenerators(n,s).forEach(o=>{o.sample.isSampleLoaded||o.sample.getAudioData()})}preloadSpecific(A,t){this.getSamplesAndGenerators(A,t).forEach(n=>{n.sample.isSampleLoaded||n.sample.getAudioData()})}getSamplesAndGenerators(A,t){let n=this.foundSamplesAndGenerators[A][t];if(n)return n;if(this.presetZones.length<1)return[];function s(h,m){return m>=h.min&&m<=h.max}function o(h,m){h.push(...m.filter(u=>!h.find(S=>S.generatorType===u.generatorType)))}function r(h,m){h.push(...m.filter(u=>!h.find(S=>_.isIdentical(u,S))))}let E=[],a=this.presetZones[0].isGlobal?[...this.presetZones[0].generators]:[],g=this.presetZones[0].isGlobal?[...this.presetZones[0].modulators]:[],d=this.presetZones[0].isGlobal?this.presetZones[0].keyRange:{min:0,max:127},B=this.presetZones[0].isGlobal?this.presetZones[0].velRange:{min:0,max:127};return this.presetZones.filter(h=>s(h.hasKeyRange?h.keyRange:d,A)&&s(h.hasVelRange?h.velRange:B,t)&&!h.isGlobal).forEach(h=>{if(h.instrument.instrumentZones.length<1)return;let m=h.generators,u=h.modulators,S=h.instrument.instrumentZones[0],w=S.isGlobal?[...S.generators]:[],k=S.isGlobal?[...S.modulators]:[],x=S.isGlobal?S.keyRange:{min:0,max:127},b=S.isGlobal?S.velRange:{min:0,max:127};h.instrument.instrumentZones.filter(C=>s(C.hasKeyRange?C.keyRange:x,A)&&s(C.hasVelRange?C.velRange:b,t)&&!C.isGlobal).forEach(C=>{let M=[...C.generators],q=[...C.modulators];o(m,a),o(M,w),r(u,g),r(q,k),r(q,this.defaultModulators);let gA=[...q];for(let tA=0;tA<u.length;tA++){let T=u[tA],$=gA.findIndex(BA=>_.isIdentical(T,BA));$!==-1?gA[$]=gA[$].sumTransform(T):gA.push(T)}E.push({instrumentGenerators:M,presetGenerators:m,modulators:gA,sample:C.sample,sampleID:C.generators.find(tA=>tA.generatorType===i.sampleID).generatorValue})})}),this.foundSamplesAndGenerators[A][t]=E,E}};var Je=class e{constructor(A=void 0){this.soundFontInfo={},this.presets=[],this.samples=[],this.instruments=[],this.defaultModulators=zt.map(t=>_.copy(t)),A?.presets&&(this.presets.push(...A.presets),this.soundFontInfo=A.info)}static mergeSoundBanks(...A){let t=A.shift(),n=t.presets;for(;A.length;)A.shift().presets.forEach(o=>{n.find(r=>r.bank===o.bank&&r.program===o.program)===void 0&&n.push(o)});return new e({presets:n,info:t.soundFontInfo})}static getDummySoundfontFile(){let A=new e,t=new ve("Saw",44100,65,20,0,0,0,127);t.sampleData=new Float32Array(128);for(let g=0;g<128;g++)t.sampleData[g]=g/128*2-1;A.samples.push(t);let n=new OA;n.isGlobal=!0,n.generators.push(new U(i.initialAttenuation,375)),n.generators.push(new U(i.releaseVolEnv,-1e3)),n.generators.push(new U(i.sampleModes,1));let s=new OA;s.sample=t;let o=new OA;o.sample=t,o.generators.push(new U(i.fineTune,-9));let r=new He;r.instrumentName="Saw Wave",r.instrumentZones.push(n),r.instrumentZones.push(s),r.instrumentZones.push(o),A.instruments.push(r);let E=new Te;E.instrument=r;let a=new Ye(A.defaultModulators);return a.presetName="Saw Wave",a.presetZones.push(E),A.presets.push(a),A.soundFontInfo.ifil="2.1",A.soundFontInfo.isng="EMU8000",A.soundFontInfo.INAM="Dummy",A.write().buffer}trimSoundBank(A){let t=this;function n(o,r){let E=0;for(let a=0;a<o.instrumentZones.length;a++){let g=o.instrumentZones[a];if(g.isGlobal)continue;let d=g.keyRange,B=g.velRange,l=!1;for(let h of r)if(h.key>=d.min&&h.key<=d.max&&h.velocity>=B.min&&h.velocity<=B.max){l=!0;break}l||(p(`%c${g.sample.sampleName} %cremoved from %c${o.instrumentName}%c. Use count: %c${g.useCount-1}`,I.recognized,I.info,I.recognized,I.info,I.recognized),o.safeDeleteZone(a)&&(E++,a--,p(`%c${g.sample.sampleName} %cdeleted`,I.recognized,I.info)),g.sample.useCount<1&&t.deleteSample(g.sample))}return E}VA("%cTrimming soundfont...",I.info);let s=A.getUsedProgramsAndKeys(t);FA("%cModifying soundfont...",I.info),p("Detected keys for midi:",s);for(let o=0;o<t.presets.length;o++){let r=t.presets[o],E=r.bank+":"+r.program,a=s[E];if(a===void 0)p(`%cDeleting preset %c${r.presetName}%c and its zones`,I.info,I.recognized,I.info),t.deletePreset(r),o--;else{let g=[...a].map(B=>{let l=B.split("-");return{key:parseInt(l[0]),velocity:parseInt(l[1])}});FA(`%cTrimming %c${r.presetName}`,I.info,I.recognized),p(`Keys for ${r.presetName}:`,g);let d=0;for(let B=0;B<r.presetZones.length;B++){let l=r.presetZones[B];if(l.isGlobal)continue;let h=l.keyRange,m=l.velRange,u=!1;for(let S of g)if(S.key>=h.min&&S.key<=h.max&&S.velocity>=m.min&&S.velocity<=m.max){u=!0;let w=n(l.instrument,g);p(`%cTrimmed off %c${w}%c zones from %c${l.instrument.instrumentName}`,I.info,I.recognized,I.info,I.recognized);break}u||(d++,r.deleteZone(B),l.instrument.useCount<1&&t.deleteInstrument(l.instrument),B--)}p(`%cTrimmed off %c${d}%c zones from %c${r.presetName}`,I.info,I.recognized,I.info,I.recognized),V()}}t.removeUnusedElements(),t.soundFontInfo.ICMT=`NOTE: This soundfont was trimmed by SpessaSynth to only contain presets used in "${A.midiName}"

`+t.soundFontInfo.ICMT,p("%cSoundfont modified!",I.recognized),V(),V()}removeUnusedElements(){this.instruments.forEach(A=>{A.useCount<1&&A.instrumentZones.forEach(t=>{t.isGlobal||t.sample.useCount--})}),this.instruments=this.instruments.filter(A=>A.useCount>0),this.samples=this.samples.filter(A=>A.useCount>0)}deleteInstrument(A){if(A.useCount>0)throw new Error(`Cannot delete an instrument that has ${A.useCount} usages.`);this.instruments.splice(this.instruments.indexOf(A),1),A.deleteInstrument(),this.removeUnusedElements()}deletePreset(A){A.deletePreset(),this.presets.splice(this.presets.indexOf(A),1),this.removeUnusedElements()}deleteSample(A){if(A.useCount>0)throw new Error(`Cannot delete sample that has ${A.useCount} usages.`);this.samples.splice(this.samples.indexOf(A),1),this.removeUnusedElements()}setSampleIDOffset(A){this.presets.forEach(t=>t.sampleIDOffset=A)}getPresetNoFallback(A,t,n=!1){let s=this.presets.find(r=>r.bank===A&&r.program===t);if(s)return s;if((A===128||n&&re(A))&&n){let r=this.presets.find(E=>E.isDrumPreset(n)&&E.program===t);if(r)return r}}getPreset(A,t,n=!1){let s=this.presets.find(r=>r.bank===A&&r.program===t),o=A===128||n&&re(A);return s||(o?(s=this.presets.find(r=>r.isDrumPreset(n)&&r.program===t),s||(s=this.presets.find(r=>r.isDrumPreset(n)))):s=this.presets.find(r=>r.program===t&&!r.isDrumPreset(n)),s&&Y(`%cPreset ${A}.${t} not found. Replaced with %c${s.presetName} (${s.bank}.${s.program})`,I.warn,I.recognized)),s||(Y(`Preset ${t} not found. Defaulting to`,this.presets[0].presetName),s=this.presets[0]),s}getPresetByName(A){let t=this.presets.find(n=>n.presetName===A);return t||(Y("Preset not found. Defaulting to:",this.presets[0].presetName),t=this.presets[0]),t}parsingError(A){throw new Error(`SF parsing error: ${A} The file may be corrupted.`)}destroySoundBank(){delete this.presets,delete this.instruments,delete this.samples}};Je.prototype.write=Oo;Je.prototype.writeDLS=nr;function sr(e){FA("%cLoading instruments...",I.info);for(let A=0;A<this.instrumentAmount;A++)this.readDLSInstrument(IA(e.chunkData));V()}var Qn=class extends Ye{constructor(A,t){super(zt),this.program=t&127;let n=A>>8&127,s=A&127;n>0?this.bank=n:this.bank=s,A>>31&&(this.bank=128),this.DLSInstrument=new He,this.DLSInstrument.addUseCount();let r=new Te;r.instrument=this.DLSInstrument,this.presetZones=[r]}};function or(e){this.verifyHeader(e,"LIST"),this.verifyText(eA(e.chunkData,4),"ins ");let A=[];for(;e.chunkData.length>e.chunkData.currentIndex;)A.push(IA(e.chunkData));let t=A.find(h=>h.header==="insh");if(!t)throw V(),new Error("No instrument header!");let n=N(t.chunkData,4),s=N(t.chunkData,4),o=N(t.chunkData,4),r=new Qn(s,o),E="unnamedPreset",a=ZA(A,"INFO");if(a){let h=IA(a.chunkData);for(;h.header!=="INAM";)h=IA(a.chunkData);E=eA(h.chunkData,h.chunkData.length).trim()}r.presetName=E,r.DLSInstrument.instrumentName=E,VA(`%cParsing %c"${E}"%c...`,I.info,I.recognized,I.info);let g=ZA(A,"lrgn");if(!g)throw V(),new Error("No region list!");let d=new OA;d.isGlobal=!0;let B=ZA(A,"lart"),l=ZA(A,"lar2");(l!==void 0||B!==void 0)&&this.readLart(B,l,d),d.generators=d.generators.filter(h=>h.generatorValue!==X[h.generatorType].def),d.modulators.find(h=>h.modulatorDestination===i.reverbEffectsSend)===void 0&&d.modulators.push(_.copy(En)),d.modulators.find(h=>h.modulatorDestination===i.chorusEffectsSend)===void 0&&d.modulators.push(_.copy(Bn)),r.DLSInstrument.instrumentZones.push(d);for(let h=0;h<n;h++){let m=IA(g.chunkData);this.verifyHeader(m,"LIST");let u=eA(m.chunkData,4);u!=="rgn "&&u!=="rgn2"&&(V(),this.parsingError(`Invalid DLS region! Expected "rgn " or "rgn2" got "${u}"`));let S=this.readRegion(m);S&&r.DLSInstrument.instrumentZones.push(S)}this.presets.push(r),this.instruments.push(r.DLSInstrument),V()}function rr(e){let A,t=!1;switch(e){default:case P.modLfo:case P.vibratoLfo:case P.coarseTune:case P.fineTune:case P.modEnv:return;case P.keyNum:A=j.noteOnKeyNum;break;case P.none:A=j.noController;break;case P.modulationWheel:A=y.modulationWheel,t=!0;break;case P.pan:A=y.pan,t=!0;break;case P.reverb:A=y.reverbDepth,t=!0;break;case P.chorus:A=y.chorusDepth,t=!0;break;case P.expression:A=y.expressionController,t=!0;break;case P.volume:A=y.mainVolume,t=!0;break;case P.velocity:A=j.noteOnVelocity;break;case P.polyPressure:A=j.polyPressure;break;case P.channelPressure:A=j.channelPressure;break;case P.pitchWheel:A=j.pitchWheel;break;case P.pitchWheelRange:A=j.pitchWheelRange;break}if(A===void 0)throw new Error(`Unknown DLS Source: ${e}`);return{enum:A,isCC:t}}function Di(e,A){switch(e){default:case R.none:return;case R.pan:return i.pan;case R.gain:return{gen:i.initialAttenuation,newAmount:A*-1};case R.pitch:return i.fineTune;case R.keyNum:return i.overridingRootKey;case R.volEnvDelay:return i.delayVolEnv;case R.volEnvAttack:return i.attackVolEnv;case R.volEnvHold:return i.holdVolEnv;case R.volEnvDecay:return i.decayVolEnv;case R.volEnvSustain:return{gen:i.sustainVolEnv,newAmount:1e3-A};case R.volEnvRelease:return i.releaseVolEnv;case R.modEnvDelay:return i.delayModEnv;case R.modEnvAttack:return i.attackModEnv;case R.modEnvHold:return i.holdModEnv;case R.modEnvDecay:return i.decayModEnv;case R.modEnvSustain:return{gen:i.sustainModEnv,newAmount:(1e3-A)/10};case R.modEnvRelease:return i.releaseModEnv;case R.filterCutoff:return i.initialFilterFc;case R.filterQ:return i.initialFilterQ;case R.chorusSend:return i.chorusEffectsSend;case R.reverbSend:return i.reverbEffectsSend;case R.modLfoFreq:return i.freqModLFO;case R.modLfoDelay:return i.delayModLFO;case R.vibLfoFreq:return i.freqVibLFO;case R.vibLfoDelay:return i.delayVibLFO}}function ki(e,A){return e===P.vibratoLfo&&A===R.pitch?i.vibLfoToPitch:e===P.modLfo&&A===R.pitch?i.modLfoToPitch:e===P.modLfo&&A===R.filterCutoff?i.modLfoToFilterFc:e===P.modLfo&&A===R.gain?i.modLfoToVolume:e===P.modEnv&&A===R.filterCutoff?i.modEnvToFilterFc:e===P.modEnv&&A===R.pitch?i.modEnvToPitch:void 0}function ir(e,A,t,n,s){let o=ki(e,t),r,E,a=!1,g=!1,d=s;if(o===void 0){let w=Di(t,s);if(w===void 0){Y(`Invalid destination: ${t}`);return}if(r=w,w.newAmount!==void 0&&(d=w.newAmount,r=w.gen),E=rr(e),E===void 0){Y(`Invalid source: ${e}`);return}}else r=o,a=!0,E={enum:j.noController,isCC:!1},g=!0;let B=rr(A);if(B===void 0){Y(`Invalid control: ${A}`);return}let l;if(g)l=0;else{let w=n&15,k=n>>10&15;k===GA.linear&&w!==GA.linear&&(k=w);let x=n>>14&1,b=n>>15&1;r===i.initialAttenuation&&s<0&&(b=1),l=oe(k,x,b,E.isCC,E.enum)}r===i.initialAttenuation&&(d=Math.max(960,Math.min(0,d)));let h=n>>4&15,m=n>>8&1,u=n>>9&1,S=oe(h,m,u,B.isCC,B.enum);if(a){let w=S;S=l,l=w}return new _(l,S,r,d,0)}function os(e,A){let t=e.chunkData,n=[],s=[];N(t,4);let o=N(t,4);for(let r=0;r<o;r++){let E=N(t,2),a=N(t,2),g=N(t,2),d=N(t,2),l=(N(t,4)|0)>>16;if(E===0&&a===0&&d===0){let h;switch(g){case R.pan:h=new U(i.pan,l);break;case R.gain:h=new U(i.initialAttenuation,-l*10/.4);break;case R.filterCutoff:h=new U(i.initialFilterFc,l);break;case R.filterQ:h=new U(i.initialFilterQ,l);break;case R.modLfoFreq:h=new U(i.freqModLFO,l);break;case R.modLfoDelay:h=new U(i.delayModLFO,l);break;case R.vibLfoFreq:h=new U(i.freqVibLFO,l);break;case R.vibLfoDelay:h=new U(i.delayVibLFO,l);break;case R.volEnvDelay:h=new U(i.delayVolEnv,l);break;case R.volEnvAttack:h=new U(i.attackVolEnv,l);break;case R.volEnvHold:h=new U(i.holdVolEnv,l,!1);break;case R.volEnvDecay:h=new U(i.decayVolEnv,l,!1);break;case R.volEnvRelease:h=new U(i.releaseVolEnv,l);break;case R.volEnvSustain:let m=1e3-l;h=new U(i.sustainVolEnv,m);break;case R.modEnvDelay:h=new U(i.delayModEnv,l);break;case R.modEnvAttack:h=new U(i.attackModEnv,l);break;case R.modEnvHold:h=new U(i.holdModEnv,l,!1);break;case R.modEnvDecay:h=new U(i.decayModEnv,l,!1);break;case R.modEnvRelease:h=new U(i.releaseModEnv,l);break;case R.modEnvSustain:let u=1e3-l;h=new U(i.sustainModEnv,u);break;case R.reverbSend:h=new U(i.reverbEffectsSend,l);break;case R.chorusSend:h=new U(i.chorusEffectsSend,l);break;case R.pitch:let S=Math.floor(l/100),w=Math.floor(l-S*100);h=new U(i.fineTune,w),n.push(new U(i.coarseTune,S));break}h&&n.push(h)}else{let h=!0;if(a===P.none)if(E===P.modLfo&&g===R.pitch)n.push(new U(i.modLfoToPitch,l));else if(E===P.modLfo&&g===R.gain)n.push(new U(i.modLfoToVolume,l));else if(E===P.modLfo&&g===R.filterCutoff)n.push(new U(i.modLfoToFilterFc,l));else if(E===P.vibratoLfo&&g===R.pitch)n.push(new U(i.vibLfoToPitch,l));else if(E===P.modEnv&&g===R.pitch)n.push(new U(i.modEnvToPitch,l));else if(E===P.modEnv&&g===R.filterCutoff)n.push(new U(i.modEnvToFilterFc,l));else if(E===P.keyNum&&g===R.pitch)n.push(new U(i.scaleTuning,l/128));else if(E===P.keyNum&&g===R.volEnvHold){n.push(new U(i.keyNumToVolEnvHold,l/-128));let m=Math.round(60/128*l);n.forEach(u=>{u.generatorType===i.holdVolEnv&&(u.generatorValue+=m)})}else if(E===P.keyNum&&g===R.volEnvDecay){n.push(new U(i.keyNumToVolEnvDecay,l/-128));let m=Math.round(60/128*l);n.forEach(u=>{u.generatorType===i.decayVolEnv&&(u.generatorValue+=m)})}else if(E===P.keyNum&&g===R.modEnvHold){n.push(new U(i.keyNumToModEnvHold,l/-128));let m=Math.round(60/128*l);n.forEach(u=>{u.generatorType===i.holdModEnv&&(u.generatorValue+=m)})}else if(E===P.keyNum&&g===R.modEnvDecay){n.push(new U(i.keyNumToModEnvDecay,l/-128));let m=Math.round(60/128*l);n.forEach(u=>{u.generatorType===i.decayModEnv&&(u.generatorValue+=m)})}else h=!1;else h=!1;if(h===!1){let m=ir(E,a,g,d,l);m?(s.push(m),p("%cSucceeded converting to SF2 Modulator!",I.recognized)):Y("Failed converting to SF2 Modulator!")}}}return A&&s.push(_.copy(hn),_.copy(cn)),{modulators:s,generators:n}}function ar(e,A,t){if(e)for(;e.chunkData.currentIndex<e.chunkData.length;){let n=IA(e.chunkData);this.verifyHeader(n,"art1","art2");let s=os(n,!0);t.generators.push(...s.generators),t.modulators.push(...s.modulators)}if(A)for(;A.chunkData.currentIndex<A.chunkData.length;){let n=IA(A.chunkData);this.verifyHeader(n,"art2","art1");let s=os(n,!1);t.generators.push(...s.generators),t.modulators.push(...s.modulators)}}var dn=class extends OA{constructor(A,t){super(),this.keyRange=A,this.velRange=t,this.isGlobal=!0}setWavesample(A,t,n,s,o,r,E){t!==0&&this.generators.push(new U(i.sampleModes,t)),this.generators.push(new U(i.initialAttenuation,A)),this.isGlobal=!1,E-=o.samplePitchCorrection;let a=Math.trunc(E/100);a!==0&&this.generators.push(new U(i.coarseTune,a));let g=E-a*100;if(g!==0&&this.generators.push(new U(i.fineTune,g)),t!==0){let d=n.start-o.sampleLoopStartIndex,B=n.end-o.sampleLoopEndIndex;if(d!==0){let l=d%32768;this.generators.push(new U(i.startloopAddrsOffset,l));let h=Math.trunc(d/32768);h!==0&&this.generators.push(new U(i.startloopAddrsCoarseOffset,h))}if(B!==0){let l=B%32768;this.generators.push(new U(i.endloopAddrsOffset,l));let h=Math.trunc(B/32768);h!==0&&this.generators.push(new U(i.endloopAddrsCoarseOffset,h))}}s!==o.samplePitch&&this.generators.push(new U(i.overridingRootKey,s)),this.generators.push(new U(i.sampleID,r)),this.sample=o,o.useCount++}};function Ir(e){let A=[];for(;e.chunkData.length>e.chunkData.currentIndex;)A.push(IA(e.chunkData));let t=A.find(q=>q.header==="rgnh"),n=N(t.chunkData,2),s=N(t.chunkData,2),o=N(t.chunkData,2),r=N(t.chunkData,2);o===0&&r===0&&(r=127,o=0),n===0&&s===0&&(s=127,n=0);let E=new dn({min:n,max:s},{min:o,max:r});N(t.chunkData,2);let a=N(t.chunkData,2);a!==0&&E.generators.push(new U(i.exclusiveClass,a));let g=ZA(A,"lart"),d=ZA(A,"lar2");this.readLart(g,d,E),E.isGlobal=!1;let B=A.find(q=>q.header==="wsmp");N(B.chunkData,4);let l=N(B.chunkData,2),h=Ne(B.chunkData[B.chunkData.currentIndex++],B.chunkData[B.chunkData.currentIndex++]),u=(N(B.chunkData,4)|0)/-655360;N(B.chunkData,4);let S=N(B.chunkData,4),w,k={start:0,end:0};if(S===0)w=0;else{N(B.chunkData,4),N(B.chunkData,4)===0?w=1:w=3,k.start=N(B.chunkData,4);let gA=N(B.chunkData,4);k.end=k.start+gA}let x=A.find(q=>q.header==="wlnk");if(x===void 0)return;N(x.chunkData,2),N(x.chunkData,2),N(x.chunkData,4);let b=N(x.chunkData,4),G=this.samples[b];if(G===void 0)throw new Error("Invalid sample ID!");let M=(u||G.sampleDbAttenuation)*10/.4;return E.setWavesample(M,w,k,l,G,b,h),E}var un=class extends ve{sampleDbAttenuation;sampleData;constructor(A,t,n,s,o,r,E,a){super(A,t,n,s,0,1,o,r),this.sampleData=E,this.sampleDbAttenuation=a}getAudioData(){return this.sampleData}getRawData(){if(this.isCompressed){if(!this.compressedData)throw new Error("Compressed but no data?? This shouldn't happen!!");return this.compressedData}return super.getRawData()}};var gr={PCM:1,ALAW:6};function wi(e,A){let t=Math.pow(2,A*8-1),n=Math.pow(2,A*8),s,o=!1;A===1?(s=255,o=!0):s=t;let r=e.size/A,E=new Float32Array(r);for(let a=0;a<E.length;a++){let g=N(e.chunkData,A);o?E[a]=g/s-.5:(g>=t&&(g-=n),E[a]=g/s)}return E}function Fi(e,A){let t=e.size/A,n=new Float32Array(t);for(let s=0;s<n.length;s++){let o=N(e.chunkData,A),r=o^85;r&=127;let E=r>>4,a=r&15;E>0&&(a+=16),a=(a<<4)+8,E>1&&(a=a<<E-1);let g=o>127?a:-a;n[s]=g/32678}return n}function Cr(e){FA("%cLoading Wave samples...",I.recognized);let A=0;for(;e.chunkData.currentIndex<e.chunkData.length;){let t=IA(e.chunkData);this.verifyHeader(t,"LIST"),this.verifyText(eA(t.chunkData,4),"wave");let n=[];for(;t.chunkData.currentIndex<t.chunkData.length;)n.push(IA(t.chunkData));let s=n.find(G=>G.header==="fmt ");if(!s)throw new Error("No fmt chunk in the wave file!");let o=N(s.chunkData,2),r=N(s.chunkData,2);if(r!==1)throw new Error(`Only mono samples are supported. Fmt reports ${r} channels`);let E=N(s.chunkData,4);N(s.chunkData,4),N(s.chunkData,2);let g=N(s.chunkData,2)/8,d=!1,B=n.find(G=>G.header==="data");B||this.parsingError("No data chunk in the WAVE chunk!");let l;switch(o){default:d=!0,l=new Float32Array(B.size/g);break;case gr.PCM:l=wi(B,g);break;case gr.ALAW:l=Fi(B,g);break}let h=ZA(n,"INFO"),m=`Unnamed ${A}`;if(h){let G=IA(h.chunkData);for(;G.header!=="INAM"&&h.chunkData.currentIndex<h.chunkData.length;)G=IA(h.chunkData);G.header==="INAM"&&(m=eA(G.chunkData,G.size).trim())}let u=60,S=0,w=0,k=l.length-1,x=0,b=n.find(G=>G.header==="wsmp");if(b){N(b.chunkData,4),u=N(b.chunkData,2),S=Ne(b.chunkData[b.chunkData.currentIndex++],b.chunkData[b.chunkData.currentIndex++]);let G=Math.trunc(S/100);if(u+=G,S-=G*100,x=(N(b.chunkData,4)|0)/-655360,N(b.chunkData,4),N(b.chunkData,4)===1){N(b.chunkData,8),w=N(b.chunkData,4);let q=N(b.chunkData,4);k=w+q}}else Y("No wsmp chunk in wave... using sane defaults.");d&&console.error(`Failed to load '${m}': Unsupported format: (${o})`),this.samples.push(new un(m,E,u,S,w,k,l,x)),A++,p(`%cLoaded sample %c${m}`,I.info,I.recognized)}V()}var we=class extends Je{constructor(A){super(),this.dataArray=new L(A),VA("%cParsing DLS...",I.info),this.dataArray||(V(),this.parsingError("No data provided!"));let t=IA(this.dataArray,!1);this.verifyHeader(t,"riff"),this.verifyText(eA(this.dataArray,4).toLowerCase(),"dls ");let n=[];for(;this.dataArray.currentIndex<this.dataArray.length;)n.push(IA(this.dataArray));this.soundFontInfo.ifil="2.1",this.soundFontInfo.isng="EMU8000",this.soundFontInfo.INAM="Unnamed DLS",this.soundFontInfo.IENG="Unknown",this.soundFontInfo.IPRD="SpessaSynth DLS",this.soundFontInfo.ICRD=new Date().toDateString();let s=ZA(n,"INFO");if(s)for(;s.chunkData.currentIndex<s.chunkData.length;){let a=IA(s.chunkData);this.soundFontInfo[a.header]=eA(a.chunkData,a.size)}this.soundFontInfo.ICMT=this.soundFontInfo.ICMT||"(No description)",this.soundFontInfo.ISBJ&&(this.soundFontInfo.ICMT+=`
`+this.soundFontInfo.ISBJ,delete this.soundFontInfo.ISBJ),this.soundFontInfo.ICMT+=`
Converted from DLS to SF2 with SpessaSynth`;for(let[a,g]of Object.entries(this.soundFontInfo))p(`%c"${a}": %c"${g}"`,I.info,I.recognized);let o=n.find(a=>a.header==="colh");o||(V(),this.parsingError("No colh chunk!")),this.instrumentAmount=N(o.chunkData,4),p(`%cInstruments amount: %c${this.instrumentAmount}`,I.info,I.recognized);let r=ZA(n,"wvpl");r||(V(),this.parsingError("No wvpl chunk!")),this.readDLSSamples(r);let E=ZA(n,"lins");E||(V(),this.parsingError("No lins chunk!")),this.readDLSInstrumentList(E),this.presets.sort((a,g)=>a.program-g.program+(a.bank-g.bank)),p(`%cParsing finished! %c"${this.soundFontInfo.INAM||"UNNAMED"}"%c has %c${this.presets.length} %cpresets,
        %c${this.instruments.length}%c instruments and %c${this.samples.length}%c samples.`,I.info,I.recognized,I.info,I.recognized,I.info,I.recognized,I.info,I.recognized,I.info),V()}verifyHeader(A,...t){for(let n of t)if(A.header.toLowerCase()===n.toLowerCase())return;V(),this.parsingError(`Invalid DLS chunk header! Expected "${t.toString()}" got "${A.header.toLowerCase()}"`)}verifyText(A,t){A.toLowerCase()!==t.toLowerCase()&&(V(),this.parsingError(`FourCC error: Expected "${t.toLowerCase()}" got "${A.toLowerCase()}"`))}parsingError(A){throw new Error(`DLS parse error: ${A} The file may be corrupted.`)}destroySoundBank(){super.destroySoundBank(),delete this.dataArray}};we.prototype.readDLSInstrumentList=sr;we.prototype.readDLSInstrument=or;we.prototype.readRegion=Ir;we.prototype.readLart=ar;we.prototype.readDLSSamples=Cr;var rs=class extends ve{constructor(A,t,n,s,o,r,E,a,g,d,B,l,h){super(A,r,E,a,g,d,s-t/2,o-t/2),this.sampleName=A,this.sampleStartIndex=t,this.sampleEndIndex=n,this.isSampleLoaded=!1,this.sampleID=l,this.sampleLength=this.sampleEndIndex-this.sampleStartIndex,this.sampleDataArray=B,this.sampleData=new Float32Array(0),this.isCompressed&&(this.sampleLoopStartIndex+=this.sampleStartIndex/2,this.sampleLoopEndIndex+=this.sampleStartIndex/2,this.sampleLength=********),this.isDataRaw=h}getRawData(){let A=this.sampleDataArray;if(this.isCompressed){if(this.compressedData)return this.compressedData;let t=A.currentIndex;return A.slice(this.sampleStartIndex/2+t,this.sampleEndIndex/2+t)}else{this.isDataRaw||super.getRawData();let t=A.currentIndex;return A.slice(t+this.sampleStartIndex,t+this.sampleEndIndex)}}decodeVorbis(){if(this.sampleLength<1)return;let A=this.sampleDataArray,t=A.currentIndex,n=A.slice(this.sampleStartIndex/2+t,this.sampleEndIndex/2+t);this.sampleData=new Float32Array(0);try{let s=me.decode(n.buffer);this.sampleData=s.data[0],this.sampleData===void 0&&Y(`Error decoding sample ${this.sampleName}: Vorbis decode returned undefined.`)}catch(s){Y(`Error decoding sample ${this.sampleName}: ${s}`),this.sampleData=new Float32Array(this.sampleLoopEndIndex+1)}}getAudioData(){return this.isSampleLoaded?this.sampleData:this.sampleLength<1?(Y(`Invalid sample ${this.sampleName}! Invalid length: ${this.sampleLength}`),new Float32Array(1)):this.isCompressed?(this.decodeVorbis(),this.isSampleLoaded=!0,this.sampleData):this.isDataRaw?this.loadUncompressedData():this.getUncompressedReadyData()}loadUncompressedData(){if(this.isCompressed)return Y("Trying to load a compressed sample via loadUncompressedData()... aborting!"),new Float32Array(0);let A=new Float32Array(this.sampleLength/2),t=this.sampleDataArray.currentIndex,n=new Int16Array(this.sampleDataArray.slice(t+this.sampleStartIndex,t+this.sampleEndIndex).buffer);for(let s=0;s<n.length;s++)A[s]=n[s]/32768;return this.sampleData=A,this.isSampleLoaded=!0,A}getUncompressedReadyData(){let A=this.sampleDataArray.slice(this.sampleStartIndex/2,this.sampleEndIndex/2);return this.sampleData=A,this.isSampleLoaded=!0,A}};function Er(e,A,t=!0){let n=[],s=0;for(;e.chunkData.length>e.chunkData.currentIndex;){let o=Ri(s,e.chunkData,A,t);n.push(o),s++}return n.length>1&&n.pop(),n}function Ri(e,A,t,n){let s=eA(A,20),o=N(A,4)*2,r=N(A,4)*2,E=N(A,4),a=N(A,4),g=N(A,4),d=A[A.currentIndex++];d===255&&(d=60);let B=qs(A[A.currentIndex++]),l=N(A,2),h=N(A,2);return new rs(s,o,r,E,a,g,d,B,l,h,t,e,n)}var is=class extends U{constructor(A){super();let t=A.currentIndex;this.generatorType=A[t+1]<<8|A[t],this.generatorValue=Ne(A[t+2],A[t+3]),A.currentIndex+=4}};function as(e){let A=[];for(;e.chunkData.length>e.chunkData.currentIndex;)A.push(new is(e.chunkData));return A.length>1&&A.pop(),A}var Is=class extends He{constructor(A){super(),this.instrumentName=eA(A.chunkData,20).trim(),this.instrumentZoneIndex=N(A.chunkData,2),this.instrumentZonesAmount=0}getInstrumentZones(A,t){this.instrumentZonesAmount=A;for(let n=this.instrumentZoneIndex;n<this.instrumentZonesAmount+this.instrumentZoneIndex;n++)this.instrumentZones.push(t[n])}};function Br(e,A){let t=[];for(;e.chunkData.length>e.chunkData.currentIndex;){let n=new Is(e);if(t.length>0){let s=n.instrumentZoneIndex-t[t.length-1].instrumentZoneIndex;t[t.length-1].getInstrumentZones(s,A)}t.push(n)}return t.length>1&&t.pop(),t}var gs=class extends OA{constructor(A){super(),this.generatorZoneStartIndex=N(A,2),this.modulatorZoneStartIndex=N(A,2),this.modulatorZoneSize=0,this.generatorZoneSize=0,this.isGlobal=!0}setZoneSize(A,t){this.modulatorZoneSize=A,this.generatorZoneSize=t}getGenerators(A){for(let t=this.generatorZoneStartIndex;t<this.generatorZoneStartIndex+this.generatorZoneSize;t++)this.generators.push(A[t])}getModulators(A){for(let t=this.modulatorZoneStartIndex;t<this.modulatorZoneStartIndex+this.modulatorZoneSize;t++)this.modulators.push(A[t])}getSample(A){let t=this.generators.find(n=>n.generatorType===i.sampleID);t&&(this.sample=A[t.generatorValue],this.isGlobal=!1,this.sample.useCount++)}getKeyRange(){let A=this.generators.find(t=>t.generatorType===i.keyRange);A&&(this.keyRange.min=A.generatorValue&127,this.keyRange.max=A.generatorValue>>8&127)}getVelRange(){let A=this.generators.find(t=>t.generatorType===i.velRange);A&&(this.velRange.min=A.generatorValue&127,this.velRange.max=A.generatorValue>>8&127)}};function hr(e,A,t,n){let s=[];for(;e.chunkData.length>e.chunkData.currentIndex;){let o=new gs(e.chunkData);if(s.length>0){let r=o.modulatorZoneStartIndex-s[s.length-1].modulatorZoneStartIndex,E=o.generatorZoneStartIndex-s[s.length-1].generatorZoneStartIndex;s[s.length-1].setZoneSize(r,E),s[s.length-1].getGenerators(A),s[s.length-1].getModulators(t),s[s.length-1].getSample(n),s[s.length-1].getKeyRange(),s[s.length-1].getVelRange()}s.push(o)}return s.length>1&&s.pop(),s}var Cs=class extends Te{constructor(A){super(),this.generatorZoneStartIndex=N(A,2),this.modulatorZoneStartIndex=N(A,2),this.modulatorZoneSize=0,this.generatorZoneSize=0,this.isGlobal=!0}setZoneSize(A,t){this.modulatorZoneSize=A,this.generatorZoneSize=t}getGenerators(A){for(let t=this.generatorZoneStartIndex;t<this.generatorZoneStartIndex+this.generatorZoneSize;t++)this.generators.push(A[t])}getModulators(A){for(let t=this.modulatorZoneStartIndex;t<this.modulatorZoneStartIndex+this.modulatorZoneSize;t++)this.modulators.push(A[t])}getInstrument(A){let t=this.generators.find(n=>n.generatorType===i.instrument);t&&(this.instrument=A[t.generatorValue],this.instrument.addUseCount(),this.isGlobal=!1)}getKeyRange(){let A=this.generators.find(t=>t.generatorType===i.keyRange);A&&(this.keyRange.min=A.generatorValue&127,this.keyRange.max=A.generatorValue>>8&127)}getVelRange(){let A=this.generators.find(t=>t.generatorType===i.velRange);A&&(this.velRange.min=A.generatorValue&127,this.velRange.max=A.generatorValue>>8&127)}};function cr(e,A,t,n){let s=[];for(;e.chunkData.length>e.chunkData.currentIndex;){let o=new Cs(e.chunkData);if(s.length>0){let r=o.modulatorZoneStartIndex-s[s.length-1].modulatorZoneStartIndex,E=o.generatorZoneStartIndex-s[s.length-1].generatorZoneStartIndex;s[s.length-1].setZoneSize(r,E),s[s.length-1].getGenerators(A),s[s.length-1].getModulators(t),s[s.length-1].getInstrument(n),s[s.length-1].getKeyRange(),s[s.length-1].getVelRange()}s.push(o)}return s.length>1&&s.pop(),s}var Es=class extends Ye{constructor(A,t){super(t),this.presetName=eA(A.chunkData,20).trim().replace(/\d{3}:\d{3}/,""),this.program=N(A.chunkData,2),this.bank=N(A.chunkData,2),this.presetZoneStartIndex=N(A.chunkData,2),this.library=N(A.chunkData,4),this.genre=N(A.chunkData,4),this.morphology=N(A.chunkData,4),this.presetZonesAmount=0}getPresetZones(A,t){this.presetZonesAmount=A;for(let n=this.presetZoneStartIndex;n<this.presetZonesAmount+this.presetZoneStartIndex;n++)this.presetZones.push(t[n])}};function lr(e,A,t){let n=[];for(;e.chunkData.length>e.chunkData.currentIndex;){let s=new Es(e,t);if(n.length>0){let o=s.presetZoneStartIndex-n[n.length-1].presetZoneStartIndex;n[n.length-1].getPresetZones(o,A)}n.push(s)}return n.length>1&&n.pop(),n}var Bs=class extends _{constructor(A){let t=N(A,2),n=N(A,2),s=Ne(A[A.currentIndex++],A[A.currentIndex++]),o=N(A,2),r=N(A,2);super(t,o,n,s,r)}};function fn(e){let A=[];for(;e.chunkData.length>e.chunkData.currentIndex;)A.push(new Bs(e.chunkData));return A}var mn=class extends Je{constructor(A,t=!0){super(),t&&console.warn("Using the constructor directly is deprecated. Use loadSoundFont instead."),this.dataArray=new L(A),VA("%cParsing SoundFont...",I.info),this.dataArray||(V(),this.parsingError("No data provided!"));let n=IA(this.dataArray,!1);this.verifyHeader(n,"riff");let s=eA(this.dataArray,4).toLowerCase();if(s!=="sfbk"&&s!=="sfpk")throw V(),new SyntaxError(`Invalid soundFont! Expected "sfbk" or "sfpk" got "${s}"`);let o=s==="sfpk",r=IA(this.dataArray);for(this.verifyHeader(r,"list"),eA(r.chunkData,4);r.chunkData.length>r.chunkData.currentIndex;){let tA=IA(r.chunkData),T;switch(tA.header.toLowerCase()){case"ifil":case"iver":T=`${N(tA.chunkData,2)}.${N(tA.chunkData,2)}`,this.soundFontInfo[tA.header]=T;break;case"icmt":T=eA(tA.chunkData,tA.chunkData.length,void 0,!1),this.soundFontInfo[tA.header]=T;break;case"dmod":let $=fn(tA);$.pop(),T=`Modulators: ${$.length}`;let BA=this.defaultModulators;this.defaultModulators=$,this.defaultModulators.push(...BA.filter(sA=>!this.defaultModulators.find(oA=>_.isIdentical(sA,oA)))),this.soundFontInfo[tA.header]=tA.chunkData;break;default:T=eA(tA.chunkData,tA.chunkData.length),this.soundFontInfo[tA.header]=T}p(`%c"${tA.header}": %c"${T}"`,I.info,I.recognized)}let E=IA(this.dataArray,!1);this.verifyHeader(E,"list"),this.verifyText(eA(this.dataArray,4),"sdta"),p("%cVerifying smpl chunk...",I.warn);let a=IA(this.dataArray,!1);this.verifyHeader(a,"smpl");let g;if(o){p("%cSF2Pack detected, attempting to decode the smpl chunk...",I.info);try{g=me.decode(this.dataArray.buffer.slice(this.dataArray.currentIndex,this.dataArray.currentIndex+E.size-12)).data[0]}catch(tA){throw V(),new Error(`SF2Pack Ogg Vorbis decode error: ${tA}`)}p(`%cDecoded the smpl chunk! Length: %c${g.length}`,I.info,I.value)}else g=this.dataArray,this.sampleDataStartIndex=this.dataArray.currentIndex;p(`%cSkipping sample chunk, length: %c${E.size-12}`,I.info,I.value),this.dataArray.currentIndex+=E.size-12,p("%cLoading preset data chunk...",I.warn);let d=IA(this.dataArray);this.verifyHeader(d,"list"),eA(d.chunkData,4);let B=IA(d.chunkData);this.verifyHeader(B,"phdr");let l=IA(d.chunkData);this.verifyHeader(l,"pbag");let h=IA(d.chunkData);this.verifyHeader(h,"pmod");let m=IA(d.chunkData);this.verifyHeader(m,"pgen");let u=IA(d.chunkData);this.verifyHeader(u,"inst");let S=IA(d.chunkData);this.verifyHeader(S,"ibag");let w=IA(d.chunkData);this.verifyHeader(w,"imod");let k=IA(d.chunkData);this.verifyHeader(k,"igen");let x=IA(d.chunkData);this.verifyHeader(x,"shdr"),this.dataArray.currentIndex=this.sampleDataStartIndex,this.samples.push(...Er(x,g,!o));let b=as(k),G=fn(w),C=hr(S,b,G,this.samples);this.instruments=Br(u,C);let M=as(m),q=fn(h),gA=cr(l,M,q,this.instruments);this.presets.push(...lr(B,gA,this.defaultModulators)),this.presets.sort((tA,T)=>tA.program-T.program+(tA.bank-T.bank)),p(`%cParsing finished! %c"${this.soundFontInfo.INAM}"%c has %c${this.presets.length} %cpresets,
        %c${this.instruments.length}%c instruments and %c${this.samples.length}%c samples.`,I.info,I.recognized,I.info,I.recognized,I.info,I.recognized,I.info,I.recognized,I.info),V(),o&&delete this.dataArray}verifyHeader(A,t){A.header.toLowerCase()!==t.toLowerCase()&&(V(),this.parsingError(`Invalid chunk header! Expected "${t.toLowerCase()}" got "${A.header.toLowerCase()}"`))}verifyText(A,t){A.toLowerCase()!==t.toLowerCase()&&(V(),this.parsingError(`Invalid FourCC: Expected "${t.toLowerCase()}" got "${A.toLowerCase()}"\``))}destroySoundBank(){super.destroySoundBank(),delete this.dataArray}};function Nt(e){let A=e.slice(8,12),t=new L(A);return eA(t,4,void 0,!1).toLowerCase()==="dls "?new we(e):new mn(e,!1)}var pn=class{constructor(A,t){this.ready=t,this.totalSoundfontOffset=0,this.reloadManager(A)}_assingSampleOffsets(){let A=0;this.soundfontList.forEach(t=>{t.soundfont.setSampleIDOffset(A),A+=t.soundfont.samples.length}),this.totalSoundfontOffset=A}generatePresetList(){this._assingSampleOffsets();let A={};for(let t=this.soundfontList.length-1;t>=0;t--){let n=this.soundfontList[t],s=new Set;for(let o of n.soundfont.presets){let r=`${o.bank+n.bankOffset}-${o.program}`;s.has(r)||(s.add(r),A[r]=o.presetName)}}this.presetList=[];for(let[t,n]of Object.entries(A)){let s=t.split("-");this.presetList.push({presetName:n,program:parseInt(s[1]),bank:parseInt(s[0])})}}handleMessage(A,t){switch(A){case Gt.addNewSoundFont:this.addNewSoundFont(t[0],t[1],t[2]);break;case Gt.reloadSoundFont:this.reloadManager(t);break;case Gt.deleteSoundFont:this.deleteSoundFont(t);break;case Gt.rearrangeSoundFonts:this.rearrangeSoundFonts(t)}}getPresetList(){return this.presetList.slice()}reloadManager(A){let t=Nt(A);this.soundfontList=[],this.soundfontList.push({id:"main",bankOffset:0,soundfont:t}),this.generatePresetList(),this.ready()}deleteSoundFont(A){if(this.soundfontList.length===0){Y("1 soundfont left. Aborting!");return}let t=this.soundfontList.findIndex(n=>n.id===A);if(t===-1){Y(`No soundfont with id of "${A}" found. Aborting!`);return}delete this.soundfontList[t].soundfont.presets,delete this.soundfontList[t].soundfont.instruments,delete this.soundfontList[t].soundfont.samples,this.soundfontList.splice(t,1),this.generatePresetList()}addNewSoundFont(A,t,n){if(this.soundfontList.find(s=>s.id===t)!==void 0)throw new Error("Cannot overwrite the existing soundfont. Use soundfontManager.delete(id) instead.");this.soundfontList.push({id:t,soundfont:Nt(A),bankOffset:n}),this.generatePresetList(),this.ready()}rearrangeSoundFonts(A){this.soundfontList.sort((t,n)=>A.indexOf(t.id)-A.indexOf(n.id)),this.generatePresetList()}getPreset(A,t,n=!1){if(this.soundfontList.length<1)throw new Error("No soundfonts! This should never happen.");for(let o of this.soundfontList){let r=o.soundfont.getPresetNoFallback(A-o.bankOffset,t,n);if(r!==void 0)return r}if(A===128||n&&re(A)){for(let o of this.soundfontList){let r=o.soundfont.presets.find(a=>a.isDrumPreset(n)&&a.program===t);if(r)return r;let E=o.soundfont.presets.find(a=>a.isDrumPreset(n));if(E)return E}return this.soundfontList[0].soundfont.presets[0]}else{for(let o of this.soundfontList){let r=o.soundfont.presets.find(E=>E.program===t&&!E.isDrumPreset(n));if(r)return r}return this.soundfontList[0].soundfont.presets[0]}}destroyManager(){this.soundfontList.forEach(A=>{A.soundfont.destroySoundBank()}),delete this.soundfontList}};var ct={linear:0,nearestNeighbor:1,fourthOrder:2},ht=class{static getSampleLinear(A,t){let n=A.sample,s=n.cursor,o=n.sampleData;if(n.isLooping){let r=n.loopEnd-n.loopStart;for(let E=0;E<t.length;E++){for(;s>=n.loopEnd;)s-=r;let a=~~s,g=a+1;for(;g>=n.loopEnd;)g-=r;let d=s-a,B=o[g],l=o[a];t[E]=l+(B-l)*d,s+=n.playbackStep*A.currentTuningCalculated}}else{if(n.loopingMode===2&&!A.isInRelease)return;for(let r=0;r<t.length;r++){let E=~~s,a=E+1;if(a>=n.end){A.finished=!0;return}let g=s-E,d=o[a],B=o[E];t[r]=B+(d-B)*g,s+=n.playbackStep*A.currentTuningCalculated}}A.sample.cursor=s}static getSampleNearest(A,t){let n=A.sample,s=n.cursor,o=n.loopEnd-n.loopStart,r=n.sampleData;if(A.sample.isLooping)for(let E=0;E<t.length;E++){for(;s>=n.loopEnd;)s-=o;let a=~~s+1;for(;a>=n.loopEnd;)a-=o;t[E]=r[a],s+=n.playbackStep*A.currentTuningCalculated}else{if(n.loopingMode===2&&!A.isInRelease)return;for(let E=0;E<t.length;E++){let a=~~s+1;if(a>=n.end){A.finished=!0;return}t[E]=r[a],s+=n.playbackStep*A.currentTuningCalculated}}n.cursor=s}static getSampleCubic(A,t){let n=A.sample,s=n.cursor,o=n.sampleData;if(n.isLooping){let r=n.loopEnd-n.loopStart;for(let E=0;E<t.length;E++){for(;s>=n.loopEnd;)s-=r;let a=~~s,g=a+1,d=g+1,B=d+1,l=s-a;g>=n.loopEnd&&(g-=r),d>=n.loopEnd&&(d-=r),B>=n.loopEnd&&(B-=r);let h=o[a],m=o[g],u=o[d],S=o[B],w=.5*(u-h),k=h-2.5*m+2*u-.5*S,x=.5*(S-h)+1.5*(m-u);t[E]=((x*l+k)*l+w)*l+m,s+=n.playbackStep*A.currentTuningCalculated}}else{if(n.loopingMode===2&&!A.isInRelease)return;for(let r=0;r<t.length;r++){let E=~~s,a=E+1,g=a+1,d=g+1,B=s-E;if(a>=n.end||g>=n.end||d>=n.end){A.finished=!0;return}let l=o[E],h=o[a],m=o[g],u=o[d],S=.5*(m-l),w=l-2.5*h+2*m-.5*u,k=.5*(u-l)+1.5*(h-m);t[r]=((k*B+w)*B+S)*B+h,s+=n.playbackStep*A.currentTuningCalculated}}A.sample.cursor=s}};var hs={addMapping:0,deleteMapping:1,clearMappings:2},yn=class{_keyMappings=[];handleMessage(A,t){switch(A){default:return;case hs.addMapping:this.addMapping(...t);break;case hs.clearMappings:this.clearMappings();break;case hs.deleteMapping:this.deleteMapping(...t)}}addMapping(A,t,n){this._keyMappings[A]===void 0&&(this._keyMappings[A]=[]),this._keyMappings[A][t]=n}deleteMapping(A,t){this._keyMappings[A]?.[t]!==void 0&&(this._keyMappings[A][t]=void 0)}clearMappings(){this._keyMappings=[]}setMappings(A){this._keyMappings=A}getMappings(){return this._keyMappings}getVelocity(A,t){let n=this._keyMappings[A]?.[t];return n?n.velocity:-1}hasOverridePatch(A,t){let n=this._keyMappings[A]?.[t]?.patch?.bank;return n!==void 0&&n>=0}getPatch(A,t){let n=this._keyMappings[A]?.[t];if(n)return n.patch;throw new Error("No modifier.")}};var Qr=.1,Ke=class e{static cachedCoefficients=[];a0=0;a1=0;a2=0;a3=0;a4=0;x1=0;x2=0;y1=0;y2=0;resonanceCb=0;currentInitialFc=13500;lastTargetCutoff=1/0;initialized=!1;static apply(A,t,n,s){let o=A.modulatedGenerators[i.initialFilterFc],r=A.filter;r.initialized?r.currentInitialFc+=(o-r.currentInitialFc)*s:(r.initialized=!0,r.currentInitialFc=o);let E=r.currentInitialFc+n;if(r.currentInitialFc>13499&&E>13499&&r.resonanceCb===0){r.currentInitialFc=13500;return}let a=A.modulatedGenerators[i.initialFilterQ];(Math.abs(r.lastTargetCutoff-E)>1||r.resonanceCb!==a)&&(r.lastTargetCutoff=E,r.resonanceCb=a,e.calculateCoefficients(r,E));for(let g=0;g<t.length;g++){let d=t[g],B=r.a0*d+r.a1*r.x1+r.a2*r.x2-r.a3*r.y1-r.a4*r.y2;r.x2=r.x1,r.x1=d,r.y2=r.y1,r.y1=B,t[g]=B}}static calculateCoefficients(A,t){t=~~t;let n=A.resonanceCb,s=e.cachedCoefficients?.[n]?.[t];if(s!==void 0){A.a0=s.a0,A.a1=s.a1,A.a2=s.a2,A.a3=s.a3,A.a4=s.a4;return}let o=Rt(t);o=Math.min(o,.45*sampleRate);let r=n/10,E=ae(-1*(r-3.01)),a=1/Math.sqrt(ae(-r)),g=2*Math.PI*o/sampleRate,d=Math.cos(g),B=Math.sin(g)/(2*E),l=(1-d)*a,h=l/2,m=h,u=1+B,S=-2*d,w=1-B,k={};k.a0=h/u,k.a1=l/u,k.a2=m/u,k.a3=S/u,k.a4=w/u,A.a0=k.a0,A.a1=k.a1,A.a2=k.a2,A.a3=k.a3,A.a4=k.a4,e.cachedCoefficients[n]===void 0&&(e.cachedCoefficients[n]=[]),e.cachedCoefficients[n][t]=k}},cs=new Ke;cs.resonanceCb=0;for(let e=1500;e<13500;e++)cs.currentInitialFc=e,Ke.calculateCoefficients(cs,e);var bA=16384,Fe=new Float32Array(bA+1),_e=new Float32Array(bA+1);Fe[0]=0;Fe[Fe.length-1]=1;_e[0]=0;_e[_e.length-1]=1;for(let e=1;e<bA-1;e++){let A=-.4166666666666667*Math.log(e/(Fe.length-1))/Math.LN10;_e[e]=1-A,Fe[Fe.length-1-e]=A}function ze(e,A,t,n){switch(e&&(t=1-t),A){case GA.linear:return n?t*2-1:t;case GA.switch:return t=t>.5?1:0,n?t*2-1:t;case GA.concave:return n?(t=t*2-1,t<0?-Fe[~~(t*-bA)]:Fe[~~(t*bA)]):Fe[~~(t*bA)];case GA.convex:return n?(t=t*2-1,t<0?-_e[~~(t*-bA)]:_e[~~(t*bA)]):_e[~~(t*bA)]}}var ls=1,Qs=new Float32Array(1e3);for(let e=0;e<Qs.length;e++)Qs[e]=ze(0,GA.convex,e/1e3,0);var Be=class e{attackDuration=0;decayDuration=0;holdDuration=0;releaseDuration=0;sustainLevel=0;delayEnd=0;attackEnd=0;holdEnd=0;decayEnd=0;releaseStartLevel=0;currentValue=0;static startRelease(A){e.recalculate(A)}static recalculate(A){let t=A.modulationEnvelope;A.isInRelease&&(t.releaseStartLevel=e.getValue(A,A.releaseStartTime,!0)),t.sustainLevel=1-A.modulatedGenerators[i.sustainModEnv]/1e3,t.attackDuration=Ce(A.modulatedGenerators[i.attackModEnv]);let n=(60-A.midiNote)*A.modulatedGenerators[i.keyNumToModEnvDecay],s=Ce(A.modulatedGenerators[i.decayModEnv]+n);t.decayDuration=s*(1-t.sustainLevel);let o=(60-A.midiNote)*A.modulatedGenerators[i.keyNumToModEnvHold];t.holdDuration=Ce(o+A.modulatedGenerators[i.holdModEnv]);let r=Ce(A.modulatedGenerators[i.releaseModEnv]);t.releaseDuration=r*t.releaseStartLevel,t.delayEnd=A.startTime+Ce(A.modulatedGenerators[i.delayModEnv]),t.attackEnd=t.delayEnd+t.attackDuration,t.holdEnd=t.attackEnd+t.holdDuration,t.decayEnd=t.holdEnd+t.decayDuration}static getValue(A,t,n=!1){let s=A.modulationEnvelope;return A.isInRelease&&!n?s.releaseStartLevel===0?0:Math.max(0,(1-(t-A.releaseStartTime)/s.releaseDuration)*s.releaseStartLevel):(t<s.delayEnd?s.currentValue=0:t<s.attackEnd?s.currentValue=Qs[~~((1-(s.attackEnd-t)/s.attackDuration)*1e3)]:t<s.holdEnd?s.currentValue=ls:t<s.decayEnd?s.currentValue=(1-(s.decayEnd-t)/s.decayDuration)*(s.sustainLevel-ls)+ls:s.currentValue=s.sustainLevel,s.currentValue)}};var Gi=-2320,Mi=-1130,Sn=class{sampleData;playbackStep=0;cursor=0;rootKey=0;loopStart=0;loopEnd=0;end=0;loopingMode=0;isLooping=!1;constructor(A,t,n,s,o,r,E,a){this.sampleData=A,this.playbackStep=t,this.cursor=n,this.rootKey=s,this.loopStart=o,this.loopEnd=r,this.end=E,this.loopingMode=a,this.isLooping=this.loopingMode===1||this.loopingMode===3}},bt=class e{sample;filter=new Ke;generators;modulators=[];modulatedGenerators;finished=!1;isInRelease=!1;channelNumber=0;velocity=0;midiNote=0;pressure=0;targetKey=0;modulationEnvelope=new Be;volumeEnvelope;startTime=0;releaseStartTime=1/0;currentTuningCents=0;currentTuningCalculated=1;currentPan=0;realKey;portamentoFromKey=-1;portamentoDuration=0;overridePan=0;exclusiveClass=0;constructor(A,t,n,s,o,r,E,a,g,d){this.sample=t,this.generators=g,this.exclusiveClass=this.generators[i.exclusiveClass],this.modulatedGenerators=new Int16Array(g),this.modulators=d,this.velocity=s,this.midiNote=n,this.channelNumber=o,this.startTime=r,this.targetKey=E,this.realKey=a,this.volumeEnvelope=new Ee(A,g[i.sustainVolEnv])}static copy(A,t){let n=A.sample,s=new Sn(n.sampleData,n.playbackStep,n.cursor,n.rootKey,n.loopStart,n.loopEnd,n.end,n.loopingMode);return new e(A.volumeEnvelope.sampleRate,s,A.midiNote,A.velocity,A.channelNumber,t,A.targetKey,A.realKey,A.generators,A.modulators.map(o=>_.copy(o)))}exclusiveRelease(){this.release(fr),this.modulatedGenerators[i.releaseVolEnv]=Gi,this.modulatedGenerators[i.releaseModEnv]=Mi,Ee.recalculate(this),Be.recalculate(this)}release(A=ur){this.releaseStartTime=currentTime,this.releaseStartTime-this.startTime<A&&(this.releaseStartTime=this.startTime+A)}};function dr(e,A,t,n,s,o=!1){let r,E=this.workletProcessorChannels[e],a=this.keyModifierManager.hasOverridePatch(e,A),g=E.getBankSelect(),d=E.preset.program;if(a){let h=this.keyModifierManager.getPatch(e,A);g=h.bank,d=h.program}let B=this.getCachedVoice(g,d,A,t);if(B!==void 0)return B.map(h=>bt.copy(h,n));let l=E.preset;return a&&(l=this.soundfontManager.getPreset(g,d,RA(this.system))),r=l.getSamplesAndGenerators(A,t).reduce((h,m)=>{if(m.sample.getAudioData()===void 0)return Y(`Discarding invalid sample: ${m.sample.sampleName}`),h;let u=new Int16Array(60);for(let C=0;C<60;C++)u[C]=Os(C,m.presetGenerators,m.instrumentGenerators);u[i.initialAttenuation]=Math.floor(u[i.initialAttenuation]*.4);let S=m.sample.samplePitch;u[i.overridingRootKey]>-1&&(S=u[i.overridingRootKey]);let w=A;u[i.keyNum]>-1&&(w=u[i.keyNum]);let k=m.sample.sampleLoopStartIndex,x=m.sample.sampleLoopEndIndex,b=u[i.sampleModes],G=new Sn(m.sample.sampleData,m.sample.sampleRate/sampleRate*Math.pow(2,m.sample.samplePitchCorrection/1200),0,S,k,x,Math.floor(m.sample.sampleData.length)-1,b);return u[i.velocity]>-1&&(t=u[i.velocity]),o&&vs([{Sample:m.sample.sampleName,Generators:u,Modulators:m.modulators.map(C=>_.debugString(C)),Velocity:t,TargetKey:w,MidiNote:A,WorkletSample:G}]),h.push(new bt(sampleRate,G,A,t,e,n,w,s,u,m.modulators.map(C=>_.copy(C)))),h},[]),this.setCachedVoice(g,d,A,t,r.map(h=>bt.copy(h,n))),r}var pr=.05,xi=4600,Ni=2e3,mr=Math.PI/2,Dn=-500,yr=500,ds=yr-Dn,Sr=new Float32Array(ds+1),Dr=new Float32Array(ds+1);for(let e=Dn;e<=yr;e++){let A=(e-Dn)/ds,t=e-Dn;Sr[t]=Math.cos(mr*A),Dr[t]=Math.sin(mr*A)}function kr(e,A,t,n,s,o,r,E){if(isNaN(A[0]))return;let a;e.overridePan?a=e.overridePan:(e.currentPan+=(e.modulatedGenerators[i.pan]-e.currentPan)*this.synth.panSmoothingFactor,a=e.currentPan);let g=this.synth.currentGain,d=~~(a+500),B=Sr[d]*g*this.synth.panLeft,l=Dr[d]*g*this.synth.panRight;if(!this.synth.oneOutputMode){let h=e.modulatedGenerators[i.reverbEffectsSend];if(h>0){let u=this.synth.reverbGain*g*(h/xi);for(let S=0;S<A.length;S++)s[S]+=u*A[S];o.set(s)}let m=e.modulatedGenerators[i.chorusEffectsSend];if(m>0){let u=this.synth.chorusGain*m/Ni,S=B*u,w=l*u;for(let k=0;k<A.length;k++)r[k]+=S*A[k],E[k]+=w*A[k]}}if(B>0)for(let h=0;h<A.length;h++)t[h]+=B*A[h];if(l>0)for(let h=0;h<A.length;h++)n[h]+=l*A[h]}function wr(e=!1){p("%cStop all received!",I.info);for(let A=0;A<this.workletProcessorChannels.length;A++)this.workletProcessorChannels[A].stopAllNotes(e);this.callEvent("stopall",void 0)}function Fr(e,A){this.soundfontBankOffset=A,this.reloadSoundFont(e,!0),this.overrideSoundfont.samples.forEach(t=>t.getAudioData()),this._snapshot!==void 0&&(this.applySynthesizerSnapshot(this._snapshot),this.resetAllControllers())}function Rr(e,A=!1){this.clearSoundFont(!1,A);try{A?(this.overrideSoundfont=Nt(e),this.overrideSoundfont.setSampleIDOffset(this.soundfontManager.totalSoundfontOffset)):this.soundfontManager.reloadManager(e)}catch(t){this.post({messageType:JA.soundfontError,messageData:t});return}this.getDefaultPresets(),this.workletProcessorChannels.forEach(t=>t.programChange(t.preset.program)),this.post({messageType:JA.ready,messageData:void 0}),this.sendPresetList(),p("%cSpessaSynth is ready!",I.recognized)}function Gr(e=!0,A=!0){this.stopAllChannels(!0),A&&(delete this.overrideSoundfont,this.overrideSoundfont=void 0),this.getDefaultPresets(),this.cachedVoices=[];for(let t=0;t<this.workletProcessorChannels.length;t++){let n=this.workletProcessorChannels[t];(!A||A&&n.presetUsesOverride)&&n.setPresetLock(!1),n.programChange(n.preset.program)}e&&this.sendPresetList()}function Mr(){let e=this.soundfontManager.getPresetList();this.overrideSoundfont!==void 0&&this.overrideSoundfont.presets.forEach(A=>{let t=A.bank===128?128:A.bank+this.soundfontBankOffset,n=e.find(s=>s.bank===t&&s.program===A.program);n!==void 0?n.presetName=A.presetName:e.push({presetName:A.presetName,bank:t,program:A.program})}),this.callEvent("presetlistchange",e)}function xr(e,A){if(this.overrideSoundfont){let t=e===128?128:e-this.soundfontBankOffset,n=this.overrideSoundfont.getPresetNoFallback(t,A,RA(this.system));if(n)return n}return this.soundfontManager.getPreset(e,A,RA(this.system))}function Nr(e,A=!1){this.transposition=0;for(let t=0;t<this.workletProcessorChannels.length;t++)this.workletProcessorChannels[t].transposeChannel(e,A);this.transposition=e}function br(e){e=Math.round(e);for(let A=0;A<this.workletProcessorChannels.length;A++)this.workletProcessorChannels[A].setCustomController(cA.masterTuning,e)}var Lt=class e{program;bank;isBankLSB;patchName;lockPreset;lockedSystem;midiControllers;lockedControllers;customControllers;lockVibrato;channelVibrato;channelTransposeKeyShift;channelOctaveTuning;isMuted;velocityOverride;drumChannel;static getChannelSnapshot(A,t){let n=A.workletProcessorChannels[t],s=new e;return s.program=n.preset.program,s.bank=n.getBankSelect(),s.isBankLSB=s.bank!==n.bank,s.lockPreset=n.lockPreset,s.lockedSystem=n.lockedSystem,s.patchName=n.preset.presetName,s.midiControllers=n.midiControllers,s.lockedControllers=n.lockedControllers,s.customControllers=n.customControllers,s.channelVibrato=n.channelVibrato,s.lockVibrato=n.lockGSNRPNParams,s.channelTransposeKeyShift=n.channelTransposeKeyShift,s.channelOctaveTuning=n.channelOctaveTuning,s.isMuted=n.isMuted,s.velocityOverride=n.velocityOverride,s.drumChannel=n.drumChannel,s}static applyChannelSnapshot(A,t,n){let s=A.workletProcessorChannels[t];s.muteChannel(n.isMuted),s.setDrums(n.drumChannel),s.midiControllers=n.midiControllers,s.lockedControllers=n.lockedControllers,s.customControllers=n.customControllers,s.updateChannelTuning(),s.channelVibrato=n.channelVibrato,s.lockGSNRPNParams=n.lockVibrato,s.channelTransposeKeyShift=n.channelTransposeKeyShift,s.channelOctaveTuning=n.channelOctaveTuning,s.velocityOverride=n.velocityOverride,s.setPresetLock(!1),s.setBankSelect(n.bank,n.isBankLSB),s.programChange(n.program),s.setPresetLock(n.lockPreset),s.lockedSystem=n.lockedSystem}};var lt=class e{channelSnapshots;keyMappings;mainVolume;pan;interpolation;system;transposition;effectsConfig;static createSynthesizerSnapshot(A){let t=new e;return t.channelSnapshots=A.workletProcessorChannels.map((n,s)=>Lt.getChannelSnapshot(A,s)),t.keyMappings=A.keyModifierManager.getMappings(),t.mainVolume=A.midiVolume,t.pan=A.pan,t.system=A.system,t.interpolation=A.interpolationType,t.transposition=A.transposition,t.effectsConfig={},t}static applySnapshot(A,t){for(A.setSystem(t.system),A.setMasterGain(t.mainVolume),A.setMasterPan(t.pan),A.transposeAllChannels(t.transposition),A.interpolationType=t.interpolation,A.keyModifierManager.setMappings(t.keyMappings);A.workletProcessorChannels.length<t.channelSnapshots.length;)A.createWorkletChannel();t.channelSnapshots.forEach((n,s)=>{Lt.applyChannelSnapshot(A,s,n)}),p("%cFinished restoring controllers!",I.info)}};function Lr(){this.post({messageType:JA.synthesizerSnapshot,messageData:lt.createSynthesizerSnapshot(this)})}function Ur(e){lt.applySnapshot(this,e),p("%cFinished applying snapshot!",I.info)}function kn(e,A,t){if(t<e)return 0;let n=(t-e)/(1/A)+.25;return Math.abs(n-~~(n+.5))*4-1}function Tr(e,A,t,n,s,o,r){let E=currentTime;if(e.isInRelease||E>=e.releaseStartTime&&(e.isInRelease=!0,Ee.startRelease(e),Be.startRelease(e),e.sample.loopingMode===3&&(e.sample.isLooping=!1)),e.modulatedGenerators[i.initialAttenuation]>2500)return e.isInRelease&&(e.finished=!0),e.finished;let a=e.targetKey,g=e.modulatedGenerators[i.fineTune]+this.channelOctaveTuning[e.midiNote]+this.channelTuningCents,d=e.modulatedGenerators[i.coarseTune],B=this.synth.tunings[this.preset.program]?.[e.realKey];if(B!==void 0&&B?.midiNote>=0&&(a=B.midiNote,g+=B.centTuning),e.portamentoFromKey>-1){let C=Math.min((E-e.startTime)/e.portamentoDuration,1),M=a-e.portamentoFromKey;d-=M*(1-C)}g+=(a-e.sample.rootKey)*e.modulatedGenerators[i.scaleTuning];let l=e.modulatedGenerators[i.vibLfoToPitch];if(l!==0){let C=e.startTime+Ce(e.modulatedGenerators[i.delayVibLFO]),M=Rt(e.modulatedGenerators[i.freqVibLFO]),q=kn(C,M,E);g+=q*(l*this.customControllers[cA.modulationMultiplier])}let h=0,m=e.modulatedGenerators[i.modLfoToPitch],u=e.modulatedGenerators[i.modLfoToVolume],S=e.modulatedGenerators[i.modLfoToFilterFc],w=0;if(m!==0||S!==0||u!==0){let C=e.startTime+Ce(e.modulatedGenerators[i.delayModLFO]),M=Rt(e.modulatedGenerators[i.freqModLFO]),q=kn(C,M,E);g+=q*(m*this.customControllers[cA.modulationMultiplier]),w=-q*u,h+=q*S}if(this.channelVibrato.depth>0){let C=kn(e.startTime+this.channelVibrato.delay,this.channelVibrato.rate,E);C&&(g+=C*this.channelVibrato.depth)}let k=e.modulatedGenerators[i.modEnvToPitch],x=e.modulatedGenerators[i.modEnvToFilterFc];if(x!==0||k!==0){let C=Be.getValue(e,E);h+=C*x,g+=C*k}let b=~~(g+d*100);b!==e.currentTuningCents&&(e.currentTuningCents=b,e.currentTuningCalculated=Math.pow(2,b/1200));let G=new Float32Array(A.length);switch(this.synth.interpolationType){case ct.fourthOrder:ht.getSampleCubic(e,G);break;case ct.linear:default:ht.getSampleLinear(e,G);break;case ct.nearestNeighbor:ht.getSampleNearest(e,G);break}return Ke.apply(e,G,h,this.synth.filterSmoothingFactor),Ee.apply(e,G,w,this.synth.volumeEnvelopeSmoothingFactor),this.panVoice(e,G,A,t,n,s,o,r),e.finished}function vr(e,A=-12e3){this.voices.forEach(t=>{t.realKey===e&&(t.modulatedGenerators[i.releaseVolEnv]=A,t.release())})}function Hr(e,A=!0){e=Math.round(e),this.setCustomController(cA.channelTuning,e),A&&p(`%cFine tuning for %c${this.channelNumber}%c is now set to %c${e}%c cents.`,I.info,I.recognized,I.info,I.value,I.info)}function Yr(e){e=Math.round(e),p(`%cChannel ${this.channelNumber} modulation depth. Cents: %c${e}`,I.info,I.value),this.setCustomController(cA.modulationMultiplier,e/50)}function Jr(e){switch(this.dataEntryState){default:break;case YA.RPCoarse:case YA.RPFine:switch(this.midiControllers[y.RPNMsb]|this.midiControllers[y.RPNLsb]>>7){default:break;case 0:if(e===0)break;this.midiControllers[MA+j.pitchWheelRange]|=e;let t=(this.midiControllers[MA+j.pitchWheelRange]>>7)+e/128;p(`%cChannel ${this.channelNumber} bend range. Semitones: %c${t}`,I.info,I.value);break;case 1:let s=this.customControllers[cA.channelTuning]<<7|e;this.setTuning(s*.01220703125);break;case 5:let r=this.customControllers[cA.modulationMultiplier]*50+e/128*100;this.setModulationDepth(r);break;case 16383:this.resetParameters();break}}}var bi=1e3/200;function Kr(e,A,t){if(A.transformAmount===0)return A.currentValue=0,0;let n;if(A.sourceUsesCC)n=e[A.sourceIndex];else{let g=A.sourceIndex+MA;switch(A.sourceIndex){case j.noController:n=16383;break;case j.noteOnKeyNum:n=t.midiNote<<7;break;case j.noteOnVelocity:n=t.velocity<<7;break;case j.polyPressure:n=t.pressure<<7;break;default:n=e[g];break}}let s=je[A.sourceCurveType][A.sourcePolarity][A.sourceDirection][n],o;if(A.secSrcUsesCC)o=e[A.secSrcIndex];else{let g=A.secSrcIndex+MA;switch(A.secSrcIndex){case j.noController:o=16383;break;case j.noteOnKeyNum:o=t.midiNote<<7;break;case j.noteOnVelocity:o=t.velocity<<7;break;case j.polyPressure:o=t.pressure<<7;break;default:o=e[g]}}let r=je[A.secSrcCurveType][A.secSrcPolarity][A.secSrcDirection][o],E=A.transformAmount;A.isEffectModulator&&E<=1e3&&(E*=bi,E=Math.min(E,1e3));let a=s*r*E;return A.transformType===2&&(a=Math.abs(a)),A.currentValue=a,a}function he(e,A,t=-1,n=0){let s=e.modulators,o=e.generators,r=e.modulatedGenerators;if(t===-1){r.set(o),s.forEach(g=>{let d=X[g.modulatorDestination],B=r[g.modulatorDestination]+Kr(A,g,e);r[g.modulatorDestination]=Math.max(d.min,Math.min(B,d.max))}),Ee.recalculate(e),Be.recalculate(e);return}let E=new Set([i.initialAttenuation,i.delayVolEnv,i.attackVolEnv,i.holdVolEnv,i.decayVolEnv,i.sustainVolEnv,i.releaseVolEnv,i.keyNumToVolEnvHold,i.keyNumToVolEnvDecay]),a=new Set;s.forEach(g=>{if(g.sourceUsesCC===t&&g.sourceIndex===n||g.secSrcUsesCC===t&&g.secSrcIndex===n){let d=g.modulatorDestination;a.has(d)||(r[d]=o[d],Kr(A,g,e),s.forEach(B=>{if(B.modulatorDestination===d){let l=X[g.modulatorDestination],h=r[g.modulatorDestination]+B.currentValue;r[g.modulatorDestination]=Math.max(l.min,Math.min(h,l.max))}}),a.add(d))}}),[...a].some(g=>E.has(g))&&Ee.recalculate(e),Be.recalculate(e)}var je=[];for(let e=0;e<4;e++){je[e]=[[new Float32Array(bA),new Float32Array(bA)],[new Float32Array(bA),new Float32Array(bA)]];for(let A=0;A<bA;A++)je[e][0][0][A]=ze(0,e,A/bA,0),je[e][1][0][A]=ze(0,e,A/bA,1),je[e][0][1][A]=ze(1,e,A/bA,0),je[e][1][1][A]=ze(1,e,A/bA,1)}function qr(e,A,t=!1){if(e>127){if(!t)return;switch(e){default:return;case Ps.velocityOverride:this.velocityOverride=A}}if(e>=y.lsbForControl1ModulationWheel&&e<=y.lsbForControl13EffectControl2&&e!==y.lsbForControl6DataEntry){let n=e-32;if(this.lockedControllers[n])return;this.midiControllers[n]=this.midiControllers[n]&16256|A&127,this.voices.forEach(s=>he(s,this.midiControllers,1,n))}if(!this.lockedControllers[e]){switch(this.midiControllers[e]=A<<7,e){case y.allNotesOff:this.stopAllNotes();break;case y.allSoundOff:this.stopAllNotes(!0);break;case y.bankSelect:this.setBankSelect(A);break;case y.lsbForControl0BankSelect:this.setBankSelect(A,!0);break;case y.RPNLsb:this.dataEntryState=YA.RPFine;break;case y.RPNMsb:this.dataEntryState=YA.RPCoarse;break;case y.NRPNMsb:this.dataEntryState=YA.NRPCoarse;break;case y.NRPNLsb:this.dataEntryState=YA.NRPFine;break;case y.dataEntryMsb:this.dataEntryCoarse(A);break;case y.lsbForControl6DataEntry:this.dataEntryFine(A);break;case y.resetAllControllers:this.resetControllersRP15Compliant();break;case y.sustainPedal:A>=64?this.holdPedal=!0:(this.holdPedal=!1,this.sustainedVoices.forEach(n=>{n.release()}),this.sustainedVoices=[]);break;default:this.voices.forEach(n=>he(n,this.midiControllers,1,e));break}this.synth.callEvent("controllerchange",{channel:this.channelNumber,controllerNumber:e,controllerValue:A})}}function Or(e=!1){e?(this.voices.length=0,this.sustainedVoices.length=0,this.synth.sendChannelProperties()):(this.voices.forEach(A=>{A.isInRelease||A.release()}),this.sustainedVoices.forEach(A=>{A.release()}))}function Pr(e){e&&this.stopAllNotes(!0),this.isMuted=e,this.synth.sendChannelProperties(),this.synth.callEvent("mutechannel",{channel:this.channelNumber,isMuted:e})}function Vr(e,A=!1){this.drumChannel||(e+=this.synth.transposition);let t=Math.trunc(e),n=this.channelTransposeKeyShift+this.customControllers[cA.channelTransposeFine]/100;this.drumChannel&&!A||e===n||(t!==this.channelTransposeKeyShift&&this.controllerChange(y.allNotesOff,127),this.channelTransposeKeyShift=t,this.setCustomController(cA.channelTransposeFine,(e-t)*100),this.synth.sendChannelProperties())}var Ut={pitchBendRange:0,fineTuning:1,coarseTuning:2,modulationDepth:5,resetParameters:16383},qe={partParameter:1,vibratoRate:8,vibratoDepth:9,vibratoDelay:10,EGAttackTime:100,EGReleaseTime:102,TVFFilterCutoff:32,drumReverb:29};function Zr(e){let A=()=>{this.channelVibrato.delay===0&&this.channelVibrato.rate===0&&this.channelVibrato.depth===0&&(this.channelVibrato.depth=50,this.channelVibrato.rate=8,this.channelVibrato.delay=.6)},t=(n,s,o)=>{o.length>0&&(o=" "+o),p(`%c${n} for %c${this.channelNumber}%c is now set to %c${s}%c${o}.`,I.info,I.recognized,I.info,I.value,I.info)};switch(this.dataEntryState){default:case YA.Idle:break;case YA.NRPFine:if(this.lockGSNRPNParams)return;let n=this.midiControllers[y.NRPNMsb]>>7,s=this.midiControllers[y.NRPNLsb]>>7;switch(n){default:if(e===64)return;Y(`%cUnrecognized NRPN for %c${this.channelNumber}%c: %c(0x${s.toString(16).toUpperCase()} 0x${s.toString(16).toUpperCase()})%c data value: %c${e}`,I.warn,I.recognized,I.warn,I.unrecognized,I.warn,I.value);break;case qe.partParameter:switch(s){default:if(e===64)return;Y(`%cUnrecognized NRPN for %c${this.channelNumber}%c: %c(0x${n.toString(16)} 0x${s.toString(16)})%c data value: %c${e}`,I.warn,I.recognized,I.warn,I.unrecognized,I.warn,I.value);break;case qe.vibratoRate:if(e===64)return;A(),this.channelVibrato.rate=e/64*8,t("Vibrato rate",`${e} = ${this.channelVibrato.rate}`,"Hz");break;case qe.vibratoDepth:if(e===64)return;A(),this.channelVibrato.depth=e/2,t("Vibrato depth",`${e} = ${this.channelVibrato.depth}`,"cents of detune");break;case qe.vibratoDelay:if(e===64)return;A(),this.channelVibrato.delay=e/64/3,t("Vibrato delay",`${e} = ${this.channelVibrato.delay}`,"seconds");break;case qe.TVFFilterCutoff:this.controllerChange(y.brightness,e),t("Filter cutoff",e.toString(),"");break;case qe.EGAttackTime:this.controllerChange(y.attackTime,e),t("EG attack time",e.toString(),"");break;case qe.EGReleaseTime:this.controllerChange(y.releaseTime,e),t("EG release time",e.toString(),"");break}break;case qe.drumReverb:let r=e;this.controllerChange(y.reverbDepth,r),t("GS Drum reverb",r.toString(),"percent");break}break;case YA.RPCoarse:case YA.RPFine:let o=this.midiControllers[y.RPNMsb]|this.midiControllers[y.RPNLsb]>>7;switch(o){default:Y(`%cUnrecognized RPN for %c${this.channelNumber}%c: %c(0x${o.toString(16)})%c data value: %c${e}`,I.warn,I.recognized,I.warn,I.unrecognized,I.warn,I.value);break;case Ut.pitchBendRange:this.midiControllers[MA+j.pitchWheelRange]=e<<7,t("Pitch bend range",e.toString(),"semitones");break;case Ut.coarseTuning:let r=e-64;this.setCustomController(cA.channelTuningSemitones,r),t("Coarse tuning",r.toString(),"semitones");break;case Ut.fineTuning:this.setTuning(e-64,!1);break;case Ut.modulationDepth:this.setModulationDepth(e*100);break;case Ut.resetParameters:this.resetParameters();break}}}var Tt={0:0,1:.006,2:.023,4:.05,8:.11,16:.25,32:.5,64:2.06,80:4.2,96:8.4,112:19.5,116:26.7,120:40,124:80,127:480};function Li(e){if(Tt[e]!==void 0)return Tt[e];let A=null,t=null;for(let n of Object.keys(Tt))n=parseInt(n),n<e&&(A===null||n>A)&&(A=n),n>e&&(t===null||n<t)&&(t=n);if(A!==null&&t!==null){let n=Tt[A],s=Tt[t];return n+(e-A)*(s-n)/(t-A)}return 0}function Xr(e,A){return Li(e)*(A/30)}function Wr(e,A,t=!1,n=!0,s=currentTime){if(A<1){this.noteOff(e);return}if(A=Math.min(127,A),this.synth.highPerformanceMode&&this.synth.totalVoicesAmount>200&&A<40||this.synth.highPerformanceMode&&A<10||this.isMuted)return;let o=e+this.channelTransposeKeyShift,r=o;if(o>127||o<0)return;let E=this.preset.program;this.synth.tunings[E]?.[o]?.midiNote>=0&&(r=this.synth.tunings[E]?.[o].midiNote),this.velocityOverride>0&&(A=this.velocityOverride);let a=this.synth.keyModifierManager.getVelocity(this.channelNumber,o);a>-1&&(A=a);let g=-1,d=0,B=this.midiControllers[y.portamentoTime]>>7,l=this.midiControllers[y.portamentoControl],h=l>>7;if(!this.drumChannel&&h!==r&&this.midiControllers[y.portamentoOnOff]>=8192&&B>0){if(l!==1){let w=Math.abs(r-h);d=Xr(B,w),g=h}this.controllerChange(y.portamentoControl,r)}let m=this.synth.getWorkletVoices(this.channelNumber,r,A,s,o,t),u=0;this.randomPan&&(u=Math.round(Math.random()*1e3-500));let S=this.voices;m.forEach(w=>{w.portamentoFromKey=g,w.portamentoDuration=d,w.overridePan=u;let k=w.exclusiveClass;k!==0&&S.forEach(gA=>{gA.exclusiveClass===k&&gA.exclusiveRelease()}),he(w,this.midiControllers);let x=w.modulatedGenerators[i.startAddrsOffset]+w.modulatedGenerators[i.startAddrsCoarseOffset]*32768,b=w.modulatedGenerators[i.endAddrOffset]+w.modulatedGenerators[i.endAddrsCoarseOffset]*32768,G=w.modulatedGenerators[i.startloopAddrsOffset]+w.modulatedGenerators[i.startloopAddrsCoarseOffset]*32768,C=w.modulatedGenerators[i.endloopAddrsOffset]+w.modulatedGenerators[i.endloopAddrsCoarseOffset]*32768,M=w.sample,q=gA=>Math.max(0,Math.min(M.sampleData.length-1,gA));if(M.cursor=q(M.cursor+x),M.end=q(M.end+b),M.loopStart=q(M.loopStart+G),M.loopEnd=q(M.loopEnd+C),M.loopEnd<M.loopStart){let gA=M.loopStart;M.loopStart=M.loopEnd,M.loopEnd=gA}M.loopEnd-M.loopStart<1&&(M.loopingMode=0,M.isLooping=!1),w.volumeEnvelope.attenuation=w.volumeEnvelope.attenuationTargetGain,w.currentPan=Math.max(-500,Math.min(500,w.modulatedGenerators[i.pan]))}),this.synth.totalVoicesAmount+=m.length,this.synth.totalVoicesAmount>this.synth.voiceCap&&this.synth.voiceKilling(m.length),S.push(...m),n&&(this.synth.sendChannelProperties(),this.synth.callEvent("noteon",{midiNote:e,channel:this.channelNumber,velocity:A}))}function _r(e){if(e>127||e<0){Y("Received a noteOn for note",e,"Ignoring.");return}let A=e+this.channelTransposeKeyShift;if(this.synth.highPerformanceMode&&!this.drumChannel){this.killNote(A,-6950),this.synth.callEvent("noteoff",{midiNote:e,channel:this.channelNumber});return}this.voices.forEach(n=>{n.realKey!==A||n.isInRelease===!0||(this.holdPedal?this.sustainedVoices.push(n):n.release())}),this.synth.callEvent("noteoff",{midiNote:e,channel:this.channelNumber})}function zr(e,A){this.voices.forEach(t=>{t.midiNote===e&&(t.pressure=A,he(t,this.midiControllers,0,j.polyPressure))}),this.synth.callEvent("polypressure",{channel:this.channelNumber,midiNote:e,pressure:A})}function jr(e){this.midiControllers[MA+j.channelPressure]=e<<7,this.voices.forEach(A=>he(A,this.midiControllers,0,j.channelPressure)),this.synth.callEvent("channelpressure",{channel:this.channelNumber,pressure:e})}function $r(e,A){if(this.lockedControllers[MA+j.pitchWheel])return;let t=A|e<<7;this.synth.callEvent("pitchwheel",{channel:this.channelNumber,MSB:e,LSB:A}),this.midiControllers[MA+j.pitchWheel]=t,this.voices.forEach(n=>he(n,this.midiControllers,0,j.pitchWheel)),this.synth.sendChannelProperties()}function Ai(e){if(e.length!==12)throw new Error("Tuning is not the length of 12.");this.channelOctaveTuning=new Int8Array(128);for(let A=0;A<128;A++)this.channelOctaveTuning[A]=e[A%12]}function ei(e,A=!1){if(this.lockPreset)return;let t=this.getBankSelect(),n,s,o=this.isXGChannel;if(this.synth.overrideSoundfont){let r=t===128?128:t-this.synth.soundfontBankOffset,E=this.synth.overrideSoundfont.getPresetNoFallback(r,e,o);E?(n=E.bank===128?128:E.bank+this.synth.soundfontBankOffset,s=E,this.presetUsesOverride=!0):(s=this.synth.soundfontManager.getPreset(t,e,o),n=s.bank,this.presetUsesOverride=!1)}else s=this.synth.soundfontManager.getPreset(t,e,o),n=s.bank,this.presetUsesOverride=!1;this.setPreset(s),this.synth.callEvent("programchange",{channel:this.channelNumber,program:s.program,bank:n,userCalled:A})}var SA=class{midiControllers=new Int16Array(jt);lockedControllers=Array(jt).fill(!1);customControllers=new Float32Array(qn);channelTransposeKeyShift=0;channelOctaveTuning=new Int8Array(128);channelTuningCents=0;holdPedal=!1;drumChannel=!1;velocityOverride=0;randomPan=!1;dataEntryState=YA.Idle;bank=0;bankLSB=0;preset=void 0;lockPreset=!1;lockedSystem="gs";presetUsesOverride=!1;lockGSNRPNParams=!1;channelVibrato={delay:0,depth:0,rate:0};isMuted=!1;voices=[];sustainedVoices=[];channelNumber;synth;constructor(A,t,n){this.synth=A,this.preset=t,this.channelNumber=n}get isXGChannel(){return RA(this.synth.system)||this.lockPreset&&RA(this.lockedSystem)}setCustomController(A,t){this.customControllers[A]=t,this.updateChannelTuning()}updateChannelTuning(){this.channelTuningCents=this.customControllers[cA.channelTuning]+this.customControllers[cA.channelTransposeFine]+this.customControllers[cA.masterTuning]+this.customControllers[cA.channelTuningSemitones]*100}renderAudio(A,t,n,s,o,r){this.voices=this.voices.filter(E=>!this.renderVoice(E,A,t,n,s,o,r))}setPresetLock(A){this.lockPreset=A,A&&(this.lockedSystem=this.synth.system)}setBankSelect(A,t=!1){if(!this.lockPreset)if(t)this.bankLSB=A;else switch(this.bank=A,gt(this.getBankSelect(),A,this.synth.system,!1,this.drumChannel,this.channelNumber).drumsStatus){default:case 0:break;case 1:this.channelNumber%16===9&&(this.bank=127);break;case 2:this.setDrums(!0);break}}getBankSelect(){return Ct(this.bank,this.bankLSB,this.drumChannel,this.isXGChannel)}setPreset(A){this.lockPreset||(delete this.preset,this.preset=A)}setDrums(A){this.lockPreset||this.drumChannel!==A&&(A?(this.channelTransposeKeyShift=0,this.drumChannel=!0):this.drumChannel=!1,this.presetUsesOverride=!1,this.synth.callEvent("drumchange",{channel:this.channelNumber,isDrumChannel:this.drumChannel}),this.programChange(this.preset.program),this.synth.sendChannelProperties())}setVibrato(A,t,n){this.lockGSNRPNParams||(this.channelVibrato.rate=t,this.channelVibrato.delay=n,this.channelVibrato.depth=A)}disableAndLockGSNRPN(){this.lockGSNRPNParams=!0,this.channelVibrato.rate=0,this.channelVibrato.delay=0,this.channelVibrato.depth=0}};SA.prototype.renderVoice=Tr;SA.prototype.panVoice=kr;SA.prototype.killNote=vr;SA.prototype.stopAllNotes=Or;SA.prototype.muteChannel=Pr;SA.prototype.noteOn=Wr;SA.prototype.noteOff=_r;SA.prototype.polyPressure=zr;SA.prototype.channelPressure=jr;SA.prototype.pitchWheel=$r;SA.prototype.programChange=ei;SA.prototype.setTuning=Hr;SA.prototype.setOctaveTuning=Ai;SA.prototype.setModulationDepth=Yr;SA.prototype.transposeChannel=Vr;SA.prototype.controllerChange=qr;SA.prototype.resetControllers=io;SA.prototype.resetControllersRP15Compliant=ao;SA.prototype.resetParameters=Io;SA.prototype.dataEntryFine=Jr;SA.prototype.dataEntryCoarse=Zr;function ti(e=!1){let A=new SA(this,this.defaultPreset,this.workletProcessorChannels.length);this.workletProcessorChannels.push(A),A.resetControllers(),this.sendChannelProperties(),e&&this.callEvent("newchannel",void 0),A.channelNumber%16===9&&this.workletProcessorChannels[this.workletProcessorChannels.length-1].setDrums(!0)}var ur=.03,fr=.07,ss=1,DA=class extends AudioWorkletProcessor{cachedVoices=[];alive=!0;deviceID=ie;interpolationType=ct.fourthOrder;sequencer=new kA(this);transposition=0;tunings=[];soundfontBankOffset=0;masterGain=ss;midiVolume=1;reverbGain=1;chorusGain=1;voiceCap=350;pan=0;panLeft=.5;panRight=.5;highPerformanceMode=!1;keyModifierManager=new yn;overrideSoundfont=void 0;workletProcessorChannels=[];system=An;totalVoicesAmount=0;defaultPreset;defaultPresetUsesOverride=!1;drumPreset;defaultDrumsUsesOverride=!1;constructor(A){super(),this.oneOutputMode=A.processorOptions?.startRenderingData?.oneOutput===!0,this._outputsAmount=this.oneOutputMode?1:A.processorOptions.midiChannels,this.enableEventSystem=A.processorOptions.enableEventSystem;for(let t=0;t<127;t++)this.tunings.push([]);try{this.soundfontManager=new pn(A.processorOptions.soundfont,this.postReady.bind(this))}catch(t){throw this.post({messageType:JA.soundfontError,messageData:t}),t}this.sendPresetList(),this.getDefaultPresets();for(let t=0;t<A.processorOptions.midiChannels;t++)this.createWorkletChannel(!1);this.workletProcessorChannels[9].preset=this.drumPreset,this.workletProcessorChannels[9].drumChannel=!0,this.volumeEnvelopeSmoothingFactor=ko*(44100/sampleRate),this.panSmoothingFactor=pr*(44100/sampleRate),this.filterSmoothingFactor=Qr*(44100/sampleRate),this._snapshot=A.processorOptions?.startRenderingData?.snapshot,this.port.onmessage=t=>this.handleMessage(t.data),A.processorOptions.startRenderingData&&(this._snapshot!==void 0&&(this.applySynthesizerSnapshot(this._snapshot),this.resetAllControllers()),p("%cRendering enabled! Starting render.",I.info),A.processorOptions.startRenderingData.parsedMIDI&&(A.processorOptions.startRenderingData?.loopCount!==void 0?(this.sequencer.loopCount=A.processorOptions.startRenderingData?.loopCount,this.sequencer.loop=!0):this.sequencer.loop=!1,this.voiceCap=1/0,this.sequencer.loadNewSongList([A.processorOptions.startRenderingData.parsedMIDI]))),me.isInitialized.then(()=>{this.postReady(),p("%cSpessaSynth is ready!",I.recognized)})}get currentGain(){return this.masterGain*this.midiVolume}getDefaultPresets(){let A=this.system;this.system="xg",this.defaultPreset=this.getPreset(0,0),this.defaultPresetUsesOverride=this.overrideSoundfont?.presets?.indexOf(this.defaultPreset)>=0,this.system=A,this.drumPreset=this.getPreset(128,0),this.defaultDrumsUsesOverride=this.overrideSoundfont?.presets?.indexOf(this.drumPreset)>=0}setSystem(A){this.system=A,this.post({messageType:JA.masterParameterChange,messageData:[Ue.midiSystem,this.system]})}getCachedVoice(A,t,n,s){return this.cachedVoices?.[A]?.[t]?.[n]?.[s]}setCachedVoice(A,t,n,s,o){this.cachedVoices||(this.cachedVoices=[]),this.cachedVoices[A]||(this.cachedVoices[A]=[]),this.cachedVoices[A][t]||(this.cachedVoices[A][t]=[]),this.cachedVoices[A][t][n]||(this.cachedVoices[A][t][n]=[]),this.cachedVoices[A][t][n][s]=o}post(A){this.enableEventSystem&&this.port.postMessage(A)}postReady(){this.enableEventSystem&&this.port.postMessage({messageType:JA.ready,messageData:void 0})}debugMessage(){p({channels:this.workletProcessorChannels,voicesAmount:this.totalVoicesAmount,outputAmount:this._outputsAmount,dumpedSamples:this.workletDumpedSamplesList})}process(A,t){if(!this.alive)return!1;this.sequencer.processTick();let n=0;return this.workletProcessorChannels.forEach((s,o)=>{if(s.voices.length<1||s.isMuted)return;let r,E,a,g,d,B,l;if(this.oneOutputMode){let h=t[0];r=o%16*2,E=h[r],a=h[r+1]}else r=o%this._outputsAmount+2,E=t[r][0],a=t[r][1],g=t[0][0],d=t[0][1],B=t[1][0],l=t[1][1];s.renderAudio(E,a,g,d,B,l),n+=s.voices.length}),n!==this.totalVoicesAmount&&(this.totalVoicesAmount=n,this.sendChannelProperties()),!0}destroyWorkletProcessor(){this.alive=!1,this.workletProcessorChannels.forEach(A=>{delete A.midiControllers,delete A.voices,delete A.sustainedVoices,delete A.lockedControllers,delete A.preset,delete A.customControllers}),delete this.cachedVoices,delete this.workletProcessorChannels,delete this.sequencer.midiData,delete this.sequencer,this.soundfontManager.destroyManager(),delete this.soundfontManager}controllerChange(A,t,n,s=!1){this.workletProcessorChannels[A].controllerChange(t,n,s)}noteOn(A,t,n,s=!1,o=!0){this.workletProcessorChannels[A].noteOn(t,n,s,o)}noteOff(A,t){this.workletProcessorChannels[A].noteOff(t)}polyPressure(A,t,n){this.workletProcessorChannels[A].polyPressure(t,n)}channelPressure(A,t){this.workletProcessorChannels[A].channelPressure(t)}pitchWheel(A,t,n){this.workletProcessorChannels[A].pitchWheel(t,n)}programChange(A,t,n=!1){this.workletProcessorChannels[A].programChange(t,n)}};DA.prototype.voiceKilling=po;DA.prototype.getWorkletVoices=dr;DA.prototype.handleMessage=wo;DA.prototype.sendChannelProperties=Ro;DA.prototype.callEvent=Fo;DA.prototype.systemExclusive=Go;DA.prototype.stopAllChannels=wr;DA.prototype.createWorkletChannel=ti;DA.prototype.resetAllControllers=ro;DA.prototype.setMasterGain=xo;DA.prototype.setMasterPan=No;DA.prototype.setMIDIVolume=Mo;DA.prototype.transposeAllChannels=Nr;DA.prototype.setMasterTuning=br;DA.prototype.getPreset=xr;DA.prototype.reloadSoundFont=Rr;DA.prototype.clearSoundFont=Gr;DA.prototype.setEmbeddedSoundFont=Fr;DA.prototype.sendPresetList=Mr;DA.prototype.sendSynthesizerSnapshot=Lr;DA.prototype.applySynthesizerSnapshot=Ur;registerProcessor(Vs,DA);p("%cProcessor succesfully registered!",I.recognized);
