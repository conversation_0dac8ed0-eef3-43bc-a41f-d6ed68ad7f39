/*!
js-synthesizer version 1.10.0

@license

Copyright (C) 2023 jet
All rights reserved.

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

  1. Redistributions of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.
  3. The name of the author may not be used to endorse or promote products derived
     from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
 */(()=>{"use strict";class e{constructor(e,t){this._ptr=e,this._module=t}getType(){return this._module._fluid_midi_event_get_type(this._ptr)}setType(e){this._module._fluid_midi_event_set_type(this._ptr,e)}getChannel(){return this._module._fluid_midi_event_get_channel(this._ptr)}setChannel(e){this._module._fluid_midi_event_set_channel(this._ptr,e)}getKey(){return this._module._fluid_midi_event_get_key(this._ptr)}setKey(e){this._module._fluid_midi_event_set_key(this._ptr,e)}getVelocity(){return this._module._fluid_midi_event_get_velocity(this._ptr)}setVelocity(e){this._module._fluid_midi_event_set_velocity(this._ptr,e)}getControl(){return this._module._fluid_midi_event_get_control(this._ptr)}setControl(e){this._module._fluid_midi_event_set_control(this._ptr,e)}getValue(){return this._module._fluid_midi_event_get_value(this._ptr)}setValue(e){this._module._fluid_midi_event_set_value(this._ptr,e)}getProgram(){return this._module._fluid_midi_event_get_program(this._ptr)}setProgram(e){this._module._fluid_midi_event_set_program(this._ptr,e)}getPitch(){return this._module._fluid_midi_event_get_pitch(this._ptr)}setPitch(e){this._module._fluid_midi_event_set_pitch(this._ptr,e)}setSysEx(e){const t=e.byteLength,s=this._module._malloc(t);new Uint8Array(this._module.HEAPU8.buffer,s,t).set(e),this._module._fluid_midi_event_set_sysex(this._ptr,s,t,1)}setText(e){const t=e.byteLength,s=this._module._malloc(t);new Uint8Array(this._module.HEAPU8.buffer,s,t).set(e),this._module._fluid_midi_event_set_text(this._ptr,s,t,1)}setLyrics(e){const t=e.byteLength,s=this._module._malloc(t);new Uint8Array(this._module.HEAPU8.buffer,s,t).set(e),this._module._fluid_midi_event_set_lyrics(this._ptr,s,t,1)}}class t{constructor(e,t){this._ptr=e,this._module=t}getRaw(){return this._ptr}dispose(){this._ptr=0}getType(){return 0===this._ptr?-1:this._module._fluid_event_get_type(this._ptr)}getSource(){return 0===this._ptr?-1:this._module._fluid_event_get_source(this._ptr)}getDest(){return 0===this._ptr?-1:this._module._fluid_event_get_dest(this._ptr)}getChannel(){return 0===this._ptr?-1:this._module._fluid_event_get_channel(this._ptr)}getKey(){return 0===this._ptr?-1:this._module._fluid_event_get_key(this._ptr)}getVelocity(){return 0===this._ptr?-1:this._module._fluid_event_get_velocity(this._ptr)}getControl(){return 0===this._ptr?-1:this._module._fluid_event_get_control(this._ptr)}getValue(){return 0===this._ptr?-1:this._module._fluid_event_get_value(this._ptr)}getProgram(){return 0===this._ptr?-1:this._module._fluid_event_get_program(this._ptr)}getData(){return 0===this._ptr?-1:this._module._fluid_event_get_data(this._ptr)}getDuration(){return 0===this._ptr?-1:this._module._fluid_event_get_duration(this._ptr)}getBank(){return 0===this._ptr?-1:this._module._fluid_event_get_bank(this._ptr)}getPitch(){return 0===this._ptr?-1:this._module._fluid_event_get_pitch(this._ptr)}getSFontId(){return 0===this._ptr?-1:this._module._fluid_event_get_sfont_id(this._ptr)}}const s="undefined"!=typeof AudioWorkletGlobalScope?AudioWorkletGlobalScope.wasmModule:Module;function n(e,t){switch(t.type){case 0:case"note":s._fluid_event_note(e,t.channel,t.key,t.vel,t.duration);break;case 1:case"noteon":case"note-on":s._fluid_event_noteon(e,t.channel,t.key,t.vel);break;case 2:case"noteoff":case"note-off":s._fluid_event_noteoff(e,t.channel,t.key);break;case 3:case"allsoundsoff":case"all-sounds-off":s._fluid_event_all_sounds_off(e,t.channel);break;case 4:case"allnotesoff":case"all-notes-off":s._fluid_event_all_notes_off(e,t.channel);break;case 5:case"bankselect":case"bank-select":s._fluid_event_bank_select(e,t.channel,t.bank);break;case 6:case"programchange":case"program-change":s._fluid_event_program_change(e,t.channel,t.preset);break;case 7:case"programselect":case"program-select":s._fluid_event_program_select(e,t.channel,t.sfontId,t.bank,t.preset);break;case 12:case"controlchange":case"control-change":s._fluid_event_control_change(e,t.channel,t.control,t.value);break;case 8:case"pitchbend":case"pitch-bend":s._fluid_event_pitch_bend(e,t.channel,t.value);break;case 9:case"pitchwheelsens":case"pitchwheelsensitivity":case"pitch-wheel-sens":case"pitch-wheel-sensitivity":s._fluid_event_pitch_wheelsens(e,t.channel,t.value);break;case 10:case"modulation":s._fluid_event_modulation(e,t.channel,t.value);break;case 11:case"sustain":s._fluid_event_sustain(e,t.channel,t.value);break;case 13:case"pan":s._fluid_event_pan(e,t.channel,t.value);break;case 14:case"volume":s._fluid_event_volume(e,t.channel,t.value);break;case 15:case"reverb":case"reverbsend":case"reverb-send":s._fluid_event_reverb_send(e,t.channel,t.value);break;case 16:case"chorus":case"chorussend":case"chorus-send":s._fluid_event_chorus_send(e,t.channel,t.value);break;case 20:case"keypressure":case"key-pressure":case"aftertouch":s._fluid_event_key_pressure(e,t.channel,t.key,t.value);break;case 19:case"channelpressure":case"channel-pressure":case"channel-aftertouch":s._fluid_event_channel_pressure(e,t.channel,t.value);break;case 21:case"systemreset":case"system-reset":s._fluid_event_system_reset(e);break;case 17:case"timer":s._fluid_event_timer(e,t.data);break;default:return!1}return!0}let i,r,_,l,o,u,h,a,d,c,f,y,m,p,g,v,b,P,w,k,S,q;function I(e){const t=i._new_fluid_event();return n(t,e)?t:(i._delete_fluid_event(t),null)}class C{constructor(){i||("undefined"!=typeof AudioWorkletGlobalScope?(i=AudioWorkletGlobalScope.wasmModule,r=AudioWorkletGlobalScope.wasmRemoveFunction):(i=Module,r=removeFunction),_=i.cwrap("fluid_sequencer_get_client_name","string",["number","number"])),this._seq=0,this._seqId=-1,this._clientFuncMap={}}_initialize(){return this.close(),this._seq=i._new_fluid_sequencer2(0),this._seqId=-1,Promise.resolve()}getRaw(){return this._seq}close(){0!==this._seq&&(Object.keys(this._clientFuncMap).forEach((e=>{this.unregisterClient(Number(e))})),this.unregisterClient(-1),i._delete_fluid_sequencer(this._seq),this._seq=0)}registerSynthesizer(e){let t;if(-1!==this._seqId&&(i._fluid_sequencer_unregister_client(this._seq,this._seqId),this._seqId=-1),"number"==typeof e)t=e;else{if(!(e instanceof M))return Promise.reject(new TypeError("'synth' is not a compatible type instance"));t=e.getRawSynthesizer()}return this._seqId=i._fluid_sequencer_register_fluidsynth(this._seq,t),Promise.resolve(this._seqId)}unregisterClient(e){if(-1===e&&-1===(e=this._seqId))return;const t=i._new_fluid_event();if(i._fluid_event_set_source(t,-1),i._fluid_event_set_dest(t,e),i._fluid_event_unregistering(t),i._fluid_sequencer_send_now(this._seq,t),i._delete_fluid_event(t),i._fluid_sequencer_unregister_client(this._seq,e),this._seqId===e)this._seqId=-1;else{const t=this._clientFuncMap;t[e]&&(r(t[e]),delete t[e])}}getAllRegisteredClients(){const e=i._fluid_sequencer_count_clients(this._seq),t=[];for(let s=0;s<e;++s){const e=i._fluid_sequencer_get_client_id(this._seq,s),n=_(this._seq,e);t.push({clientId:e,name:n})}return Promise.resolve(t)}getClientCount(){return Promise.resolve(i._fluid_sequencer_count_clients(this._seq))}getClientInfo(e){const t=i._fluid_sequencer_get_client_id(this._seq,e),s=_(this._seq,t);return Promise.resolve({clientId:t,name:s})}setTimeScale(e){i._fluid_sequencer_set_time_scale(this._seq,e)}getTimeScale(){return Promise.resolve(i._fluid_sequencer_get_time_scale(this._seq))}getTick(){return Promise.resolve(i._fluid_sequencer_get_tick(this._seq))}sendEventAt(e,t,s){const n=I(e);if(null!==n){const e=i._fluid_sequencer_count_clients(this._seq);for(let r=0;r<e;++r){const e=i._fluid_sequencer_get_client_id(this._seq,r);i._fluid_event_set_dest(n,e),i._fluid_sequencer_send_at(this._seq,n,t,s?1:0)}i._delete_fluid_event(n)}}sendEventToClientAt(e,t,s,n){const r=I(t);null!==r&&(i._fluid_event_set_dest(r,-1===e?this._seqId:e),i._fluid_sequencer_send_at(this._seq,r,s,n?1:0),i._delete_fluid_event(r))}sendEventToClientNow(e,t){const s=I(t);null!==s&&(i._fluid_event_set_dest(s,-1===e?this._seqId:e),i._fluid_sequencer_send_now(this._seq,s),i._delete_fluid_event(s))}sendEventNow(e,s){if(!(s instanceof t))return;const n=s.getRaw();0!==n&&(i._fluid_event_set_dest(n,-1===e?this._seqId:e),i._fluid_sequencer_send_now(this._seq,n))}removeAllEvents(){i._fluid_sequencer_remove_events(this._seq,-1,-1,-1)}removeAllEventsFromClient(e){i._fluid_sequencer_remove_events(this._seq,-1,-1===e?this._seqId:e,-1)}processSequencer(e){0!==this._seq&&i._fluid_sequencer_process(this._seq,e)}setIntervalForSequencer(e){return setInterval((()=>this.processSequencer(e)),e)}}class E{constructor(e){this._ptr=e}static getSoundfontById(e,t){l||(l="undefined"!=typeof AudioWorkletGlobalScope?AudioWorkletGlobalScope.wasmModule:Module,o=l.cwrap("fluid_sfont_get_name","string",["number"]),u=l.cwrap("fluid_preset_get_name","string",["number"]));const s=l._fluid_synth_get_sfont_by_id(e.getRawSynthesizer(),t);return 0===s?null:new E(s)}getName(){return o(this._ptr)}getPreset(e,t){const s=l._fluid_sfont_get_preset(this._ptr,e,t);if(0===s)return null;return{soundfont:this,name:u(s),bankNum:l._fluid_preset_get_banknum(s),num:l._fluid_preset_get_num(s)}}getPresetIterable(){const e=()=>{l._fluid_sfont_iteration_start(this._ptr)},t=()=>{const e=l._fluid_sfont_iteration_next(this._ptr);if(0===e)return{done:!0,value:void 0};return{done:!1,value:{soundfont:this,name:u(e),bankNum:l._fluid_preset_get_banknum(e),num:l._fluid_preset_get_num(e)}}};return{[Symbol.iterator]:()=>(e(),{next:t})}}}function A(){if(!p){if("undefined"!=typeof AudioWorkletGlobalScope)h=AudioWorkletGlobalScope.wasmModule,a=AudioWorkletGlobalScope.wasmAddFunction,d=AudioWorkletGlobalScope.wasmRemoveFunction;else{if("undefined"==typeof Module)throw new Error("wasm module is not available. libfluidsynth-*.js must be loaded.");h=Module,a=addFunction,d=removeFunction}c=h.FS,f=h.cwrap("fluid_settings_setint","number",["number","string","number"]),y=h.cwrap("fluid_settings_setnum","number",["number","string","number"]),m=h.cwrap("fluid_settings_setstr","number",["number","string","string"]),p=h.cwrap("fluid_synth_error","string",["number"]),g=h.cwrap("fluid_synth_sfload","number",["number","string","number"]),v=h.cwrap("fluid_sequencer_register_client","number",["number","string","number","number"]),b=h._malloc.bind(h),P=h._free.bind(h),w=h._fluid_synth_handle_midi_event.bind(h)}}function z(e,t,s){void 0!==s&&f(e,t,s?1:0)}function F(e,t,s){void 0!==s&&f(e,t,s)}function R(e,t,s){void 0!==s&&y(e,t,s)}class M{constructor(){A(),this._settings=0,this._synth=0,this._player=0,this._playerPlaying=!1,this._playerCallbackPtr=null,this._fluidSynthCallback=null,this._buffer=0,this._bufferSize=0,this._numPtr=0,this._gain=.5}static waitForWasmInitialized(){return function(){if(k)return k;let e,t;if("undefined"!=typeof AudioWorkletGlobalScope)e=AudioWorkletGlobalScope.wasmModule,t=AudioWorkletGlobalScope.addOnPostRun;else{if("undefined"==typeof Module)return Promise.reject(new Error("wasm module is not available. libfluidsynth-*.js must be loaded."));e=Module,t="undefined"!=typeof addOnPostRun?addOnPostRun:void 0}return e.calledRun?(k=Promise.resolve(),k):(k=new Promise(void 0===t?e=>{const t=h.onRuntimeInitialized;h.onRuntimeInitialized=()=>{e(),t&&t()}}:e=>{t(e)}),k)}()}isInitialized(){return 0!==this._synth}getRawSynthesizer(){return this._synth}createAudioNode(e,t){const s=e.createScriptProcessor(t,0,2);return s.addEventListener("audioprocess",(e=>{this.render(e.outputBuffer)})),s}init(e,t){this.close();const s=this._settings=h._new_fluid_settings();y(s,"synth.sample-rate",e),t&&(void 0!==t.initialGain&&(this._gain=t.initialGain),z(s,"synth.chorus.active",t.chorusActive),R(s,"synth.chorus.depth",t.chorusDepth),R(s,"synth.chorus.level",t.chorusLevel),F(s,"synth.chorus.nr",t.chorusNr),R(s,"synth.chorus.speed",t.chorusSpeed),F(s,"synth.midi-channels",t.midiChannelCount),function(e,t,s){void 0!==s&&m(e,t,s)}(s,"synth.midi-bank-select",t.midiBankSelect),F(s,"synth.min-note-length",t.minNoteLength),R(s,"synth.overflow.age",t.overflowAge),R(s,"synth.overflow.important",t.overflowImportantValue),void 0!==t.overflowImportantChannels&&m(s,"synth.overflow.important-channels",t.overflowImportantChannels.join(",")),R(s,"synth.overflow.percussion",t.overflowPercussion),R(s,"synth.overflow.released",t.overflowReleased),R(s,"synth.overflow.sustained",t.overflowSustained),R(s,"synth.overflow.volume",t.overflowVolume),F(s,"synth.polyphony",t.polyphony),z(s,"synth.reverb.active",t.reverbActive),R(s,"synth.reverb.damp",t.reverbDamp),R(s,"synth.reverb.level",t.reverbLevel),R(s,"synth.reverb.room-size",t.reverbRoomSize),R(s,"synth.reverb.width",t.reverbWidth)),y(s,"synth.gain",this._gain),this._synth=h._new_fluid_synth(this._settings),this._numPtr=b(8)}close(){0!==this._synth&&(this._closePlayer(),h._delete_fluid_synth(this._synth),this._synth=0,h._delete_fluid_settings(this._settings),this._settings=0,P(this._numPtr),this._numPtr=0)}isPlaying(){return 0!==this._synth&&function(e){const t=h._fluid_synth_get_active_voice_count(e);if(!t)return 0;let s=140,n=e+s+4>>2,i=h.HEAPU32[n];if(i!==t&&(s+=4,n=e+s+4>>2,i=h.HEAPU32[n],i!==t))return console.warn("js-synthesizer: cannot check synthesizer internal data (may be changed)"),t;const r=h.HEAPU32[e+s>>2];if(!r||r>=h.HEAPU32.byteLength)return console.warn("js-synthesizer: cannot check synthesizer internal data (may be changed)"),t;const _=h._fluid_synth_get_polyphony(e);let l=!1;for(let e=0;e<_;++e){const t=h.HEAPU32[(r>>2)+e];if(t&&4!==h.HEAPU8[t+4]){l=!0;break}}if(!l)return 0!==i&&console.warn("js-synthesizer: Active voice count is not zero, but all voices are off:",i),h.HEAPU32[n]=0,0;return t}(this._synth)>0}setInterpolation(e,t){this.ensureInitialized(),void 0===t&&(t=-1),h._fluid_synth_set_interp_method(this._synth,t,e)}getGain(){return this._gain}setGain(e){this.ensureInitialized(),h._fluid_synth_set_gain(this._synth,e),this._gain=h._fluid_synth_get_gain(this._synth)}setChannelType(e,t){this.ensureInitialized(),h._fluid_synth_set_channel_type(this._synth,e,t?1:0)}waitForVoicesStopped(){return this.flushFramesAsync()}loadSFont(e){this.ensureInitialized();const t=(s=".sf2",`/${"sfont"}-r${65535*Math.random()}-${65535*Math.random()}${s}`);var s;const n=new Uint8Array(e);c.writeFile(t,n);const i=g(this._synth,t,1);return c.unlink(t),-1===i?Promise.reject(new Error(p(this._synth))):Promise.resolve(i)}unloadSFont(e){this.ensureInitialized(),this.stopPlayer(),this.flushFramesSync(),h._fluid_synth_sfunload(this._synth,e,1)}unloadSFontAsync(e){return this.ensureInitialized(),this.stopPlayer(),this.flushFramesAsync().then((()=>{h._fluid_synth_sfunload(this._synth,e,1)}))}getSFontObject(e){return E.getSoundfontById(this,e)}getSFontBankOffset(e){return this.ensureInitialized(),Promise.resolve(h._fluid_synth_get_bank_offset(this._synth,e))}setSFontBankOffset(e,t){this.ensureInitialized(),h._fluid_synth_set_bank_offset(this._synth,e,t)}render(e){const t="numberOfChannels"in e?e.length:e[0].length,s="numberOfChannels"in e?e.numberOfChannels:e.length,n=4*t,i=2*n;this._bufferSize<i&&(0!==this._buffer&&P(this._buffer),this._buffer=b(i),this._bufferSize=i);const r=this._buffer,_=this._buffer+n;this.renderRaw(r,_,t);const l=new Float32Array(h.HEAPU8.buffer,r,t),o=s>=2?new Float32Array(h.HEAPU8.buffer,_,t):null;if("numberOfChannels"in e)if(e.copyToChannel)e.copyToChannel(l,0,0),o&&e.copyToChannel(o,1,0);else{const t=e.getChannelData(0);if(l.forEach(((e,s)=>t[s]=e)),o){const t=e.getChannelData(1);o.forEach(((e,s)=>t[s]=e))}}else e[0].set(l),o&&e[1].set(o);this.isPlayerPlaying()}midiNoteOn(e,t,s){h._fluid_synth_noteon(this._synth,e,t,s)}midiNoteOff(e,t){h._fluid_synth_noteoff(this._synth,e,t)}midiKeyPressure(e,t,s){h._fluid_synth_key_pressure(this._synth,e,t,s)}midiControl(e,t,s){h._fluid_synth_cc(this._synth,e,t,s)}midiProgramChange(e,t){h._fluid_synth_program_change(this._synth,e,t)}midiChannelPressure(e,t){h._fluid_synth_channel_pressure(this._synth,e,t)}midiPitchBend(e,t){h._fluid_synth_pitch_bend(this._synth,e,t)}midiSysEx(e){const t=e.byteLength,s=b(t);h.HEAPU8.set(e,s),h._fluid_synth_sysex(this._synth,s,t,0,0,0,0),P(s)}midiPitchWheelSensitivity(e,t){h._fluid_synth_pitch_wheel_sens(this._synth,e,t)}midiBankSelect(e,t){h._fluid_synth_bank_select(this._synth,e,t)}midiSFontSelect(e,t){h._fluid_synth_sfont_select(this._synth,e,t)}midiProgramSelect(e,t,s,n){h._fluid_synth_program_select(this._synth,e,t,s,n)}midiUnsetProgram(e){h._fluid_synth_unset_program(this._synth,e)}midiProgramReset(){h._fluid_synth_program_reset(this._synth)}midiSystemReset(){h._fluid_synth_system_reset(this._synth)}midiAllNotesOff(e){h._fluid_synth_all_notes_off(this._synth,void 0===e?-1:e)}midiAllSoundsOff(e){h._fluid_synth_all_sounds_off(this._synth,void 0===e?-1:e)}midiSetChannelType(e,t){h._fluid_synth_set_channel_type(this._synth,e,t?1:0)}setReverb(e,t,s,n){h._fluid_synth_set_reverb(this._synth,e,t,s,n)}setReverbRoomsize(e){h._fluid_synth_set_reverb_roomsize(this._synth,e)}setReverbDamp(e){h._fluid_synth_set_reverb_damp(this._synth,e)}setReverbWidth(e){h._fluid_synth_set_reverb_width(this._synth,e)}setReverbLevel(e){h._fluid_synth_set_reverb_level(this._synth,e)}setReverbOn(e){h._fluid_synth_set_reverb_on(this._synth,e?1:0)}getReverbRoomsize(){return h._fluid_synth_get_reverb_roomsize(this._synth)}getReverbDamp(){return h._fluid_synth_get_reverb_damp(this._synth)}getReverbLevel(){return h._fluid_synth_get_reverb_level(this._synth)}getReverbWidth(){return h._fluid_synth_get_reverb_width(this._synth)}setChorus(e,t,s,n,i){h._fluid_synth_set_chorus(this._synth,e,t,s,n,i)}setChorusVoiceCount(e){h._fluid_synth_set_chorus_nr(this._synth,e)}setChorusLevel(e){h._fluid_synth_set_chorus_level(this._synth,e)}setChorusSpeed(e){h._fluid_synth_set_chorus_speed(this._synth,e)}setChorusDepth(e){h._fluid_synth_set_chorus_depth(this._synth,e)}setChorusType(e){h._fluid_synth_set_chorus_type(this._synth,e)}setChorusOn(e){h._fluid_synth_set_chorus_on(this._synth,e?1:0)}getChorusVoiceCount(){return h._fluid_synth_get_chorus_nr(this._synth)}getChorusLevel(){return h._fluid_synth_get_chorus_level(this._synth)}getChorusSpeed(){return h._fluid_synth_get_chorus_speed(this._synth)}getChorusDepth(){return h._fluid_synth_get_chorus_depth(this._synth)}getChorusType(){return h._fluid_synth_get_chorus_type(this._synth)}getGenerator(e,t){return h._fluid_synth_get_gen(this._synth,e,t)}setGenerator(e,t,s){h._fluid_synth_set_gen(this._synth,e,t,s)}getLegatoMode(e){return h._fluid_synth_get_legato_mode(this._synth,e,this._numPtr),h.HEAP32[this._numPtr>>2]}setLegatoMode(e,t){h._fluid_synth_set_legato_mode(this._synth,e,t)}getPortamentoMode(e){return h._fluid_synth_get_portamento_mode(this._synth,e,this._numPtr),h.HEAP32[this._numPtr>>2]}setPortamentoMode(e,t){h._fluid_synth_set_portamento_mode(this._synth,e,t)}getBreathMode(e){return h._fluid_synth_get_breath_mode(this._synth,e,this._numPtr),h.HEAP32[this._numPtr>>2]}setBreathMode(e,t){h._fluid_synth_set_breath_mode(this._synth,e,t)}resetPlayer(){return new Promise((e=>{this._initPlayer(),e()}))}closePlayer(){this._closePlayer()}_initPlayer(){this._closePlayer();const e=h._new_fluid_player(this._synth);if(this._player=e,0===e)throw new Error("Out of memory");if(null===this._fluidSynthCallback){const t=h.HEAPU32[e+588>>2];h.HEAPU32[e+592>>2]===this._synth&&(this._fluidSynthCallback=t)}}_closePlayer(){const e=this._player;0!==e&&(this.stopPlayer(),h._delete_fluid_player(e),this._player=0,this._playerCallbackPtr=null)}isPlayerPlaying(){if(this._playerPlaying){if(1===h._fluid_player_get_status(this._player))return!0;this.stopPlayer()}return!1}addSMFDataToPlayer(e){this.ensurePlayerInitialized();const t=e.byteLength,s=b(t);h.HEAPU8.set(new Uint8Array(e),s);const n=h._fluid_player_add_mem(this._player,s,t);return P(s),-1!==n?Promise.resolve():Promise.reject(new Error(p(this._synth)))}playPlayer(){if(this.ensurePlayerInitialized(),this._playerPlaying&&this.stopPlayer(),-1===h._fluid_player_play(this._player))return Promise.reject(new Error(p(this._synth)));this._playerPlaying=!0;let e=()=>{};const t=new Promise((t=>{e=t}));return this._playerDefer={promise:t,resolve:e},Promise.resolve()}stopPlayer(){const e=this._player;0!==e&&this._playerPlaying&&(h._fluid_player_stop(e),h._fluid_player_join(e),h._fluid_synth_all_sounds_off(this._synth,-1),this._playerDefer&&(this._playerDefer.resolve(),this._playerDefer=void 0),this._playerPlaying=!1)}retrievePlayerCurrentTick(){return this.ensurePlayerInitialized(),Promise.resolve(h._fluid_player_get_current_tick(this._player))}retrievePlayerTotalTicks(){return this.ensurePlayerInitialized(),Promise.resolve(h._fluid_player_get_total_ticks(this._player))}retrievePlayerBpm(){return this.ensurePlayerInitialized(),Promise.resolve(h._fluid_player_get_bpm(this._player))}retrievePlayerMIDITempo(){return this.ensurePlayerInitialized(),Promise.resolve(h._fluid_player_get_midi_tempo(this._player))}seekPlayer(e){this.ensurePlayerInitialized(),h._fluid_player_seek(this._player,e)}setPlayerLoop(e){this.ensurePlayerInitialized(),h._fluid_player_set_loop(this._player,e)}setPlayerTempo(e,t){this.ensurePlayerInitialized(),h._fluid_player_set_tempo(this._player,e,t)}hookPlayerMIDIEvents(t,s){this.ensurePlayerInitialized();const n=this._playerCallbackPtr;if(null===n&&null===t)return;const i=null!==t?a(function(t,s,n){return(i,r)=>{const _=h._fluid_midi_event_get_type(r);return s(t,_,new e(r,h),n)?0:h._fluid_synth_handle_midi_event(i,r)}}(this,t,s),"iii"):null!==this._fluidSynthCallback?null:a(w,"iii");null!==n&&null!==i?(h._fluid_player_set_playback_callback(this._player,i,this._synth),d(n)):null===i?(h._fluid_player_set_playback_callback(this._player,this._fluidSynthCallback,this._synth),d(n)):h._fluid_player_set_playback_callback(this._player,i,this._synth),this._playerCallbackPtr=i}ensureInitialized(){if(0===this._synth)throw new Error("Synthesizer is not initialized")}ensurePlayerInitialized(){this.ensureInitialized(),0===this._player&&this._initPlayer()}renderRaw(e,t,s){h._fluid_synth_write_float(this._synth,s,e,0,1,t,0,1)}flushFramesSync(){const e=262144,t=b(524288),s=t,n=t+e;for(;this.isPlaying();)this.renderRaw(s,n,65536);P(t)}flushFramesAsync(){if(!this.isPlaying())return Promise.resolve();const e=262144,t=b(524288),s=t,n=t+e,i="undefined"!=typeof setTimeout?()=>new Promise((e=>setTimeout(e,0))):()=>Promise.resolve();function r(){return i().then(l)}const _=this;function l(){return _.isPlaying()?(_.renderRaw(s,n,65536),r()):(P(t),Promise.resolve())}return r()}waitForPlayerStopped(){return this._playerDefer?this._playerDefer.promise:Promise.resolve()}static createSequencer(){A();const e=new C;return e._initialize().then((()=>e))}static registerSequencerClient(e,s,n,i){if(!(e instanceof C))throw new TypeError("Invalid sequencer instance");const r=a(((s,i,r,_)=>{const l=new t(i,h),o=h._fluid_event_get_type(i);n(s,o,l,e,_)}),"viiii"),_=v(e.getRaw(),s,r,i);return-1!==_&&(e._clientFuncMap[_]=r),_}static sendEventToClientNow(e,t,s){if(!(e instanceof C))throw new TypeError("Invalid sequencer instance");e.sendEventToClientNow(t,s)}static sendEventNow(e,t,s){if(!(e instanceof C))throw new TypeError("Invalid sequencer instance");e.sendEventNow(t,s)}static setIntervalForSequencer(e,t){if(!(e instanceof C))throw new TypeError("Invalid sequencer instance");return e.setIntervalForSequencer(t)}}function T(e){return function(e){const t={},s=[];let n=e;for(;n&&n!==Object.prototype;)s.unshift(n),n=Object.getPrototypeOf(n);return s.forEach((s=>{Object.getOwnPropertyNames(s).forEach((s=>{try{const n=e[s];"function"!=typeof n&&"symbol"!=typeof n&&(t[s]=n)}catch(e){}}))})),{baseName:e.name,message:e.message,detail:t}}(e&&e instanceof Error?e:new Error(`${e}`))}function G(e,t,s,n){const i={port:e};return t?e.addEventListener("message",(e=>{const r=e.data;r&&t.then((()=>W(i.port,r,s,n)))})):e.addEventListener("message",(e=>{const t=e.data;t&&W(i.port,t,s,n)})),e.start(),i}function W(e,t,s,n){if(n&&n(t))return;const i=s();if(i[t.method])try{O(e,t.id,t.method,i[t.method].apply(i,t.args))}catch(s){N(e,t.id,t.method,s)}else N(e,t.id,t.method,new Error("Not implemented"))}function D(e,t,s,n){O(e.port,t,s,n)}function O(e,t,s,n){n instanceof Promise?n.then((n=>{t>=0&&e.postMessage({id:t,method:s,val:n})}),(n=>{e.postMessage({id:t,method:s,error:T(n)})})):e.postMessage({id:t,method:s,val:n})}function L(e,t,s,n){N(e.port,t,s,n)}function N(e,t,s,n){e.postMessage({id:t,method:s,error:T(n)})}let j=null;const U=[],H=5,B={Panic:0,Error:1,Warning:2,Info:3,Debug:4};function x(e=B.Panic){if(j!==e){if(function(){if("undefined"!=typeof AudioWorkletGlobalScope)S=AudioWorkletGlobalScope.wasmModule;else{if("undefined"==typeof Module)throw new Error("wasm module is not available. libfluidsynth-*.js must be loaded.");S=Module}}(),null==e)null!=q&&(S._fluid_set_log_function(0,q,0),S._fluid_set_log_function(1,q,0),S._fluid_set_log_function(2,q,0),S._fluid_set_log_function(3,q,0)),S._fluid_set_log_function(4,0,0);else{let t;for(let s=e;s<H;++s){const e=S._fluid_set_log_function(s,0,0);s!==B.Debug&&(t=e)}null!=t&&null==q&&(q=t)}j=e;for(const t of U)t(e)}}var V=function(e,t,s,n){return new(s||(s=Promise))((function(i,r){function _(e){try{o(n.next(e))}catch(e){r(e)}}function l(e){try{o(n.throw(e))}catch(e){r(e)}}function o(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(_,l)}o((n=n.apply(e,t||[])).next())}))};const $=M.waitForWasmInitialized();AudioWorkletGlobalScope.JSSynth={rewriteEventData:function(e,s){if(!(e&&e instanceof t))return!1;const i=e.getRaw();return 0!==i&&n(i,s)},Synthesizer:M,disableLogging:x,restoreLogging:function(){x(null)}},AudioWorkletGlobalScope.Fluid=AudioWorkletGlobalScope.JSSynth,function(){class e extends AudioWorkletProcessor{constructor(e){super(e);const t=e.processorOptions,s=t&&t.settings;t&&t.disabledLoggingLevel&&x(t.disabledLoggingLevel);const n=this.doInit(s);this._messaging=G(this.port,n,(()=>this.synth),(e=>{switch(e.method){case"init":return this.synth.init(sampleRate,s),!0;case"createSequencer":return this.doCreateSequencer(e.args[0]).then((()=>{D(this._messaging,e.id,e.method,void 0)})),!0;case"hookPlayerMIDIEventsByName":this.doHookPlayerMIDIEvents(e.args[0],e.args[1])?D(this._messaging,e.id,e.method,void 0):L(this._messaging,e.id,e.method,new Error("Name not found"));return!0;case"callFunction":try{this.doCallFunction(e.args[0],e.args[1]),D(this._messaging,e.id,e.method,void 0)}catch(t){L(this._messaging,e.id,e.method,t)}return!0;case"getSFontObject":try{const t=this.doGetSFontObject(e.args[0],e.args[1]);null!==t?D(this._messaging,e.id,e.method,t):L(this._messaging,e.id,e.method,new Error("Invalid sfontId"))}catch(t){L(this._messaging,e.id,e.method,t)}return!0;case"playPlayer":return this.doPlayPlayer(e),!0;case"loggingChanged":return x(e.args[0]),!0}return!1}))}doInit(e){return V(this,void 0,void 0,(function*(){yield $,this.synth=new M,this.synth.init(sampleRate,e)}))}doCreateSequencer(e){return M.createSequencer().then((t=>{const s=G(e,null,(()=>t),(e=>{if("getRaw"===e.method)return D(s,e.id,e.method,t.getRaw()),!0;if("registerSequencerClientByName"===e.method){const n=this.doRegisterSequencerClient(t,e.args[0],e.args[1],e.args[2]);return null!==n?D(s,e.id,e.method,n):L(s,e.id,e.method,new Error("Name not found")),!0}return!1}))}))}doGetSFontObject(e,t){const s=this.synth.getSFontObject(t);if(null===s)return null;const n=G(e,null,(()=>s),(e=>"getPresetIterable"===e.method&&(D(n,e.id,e.method,[...s.getPresetIterable()]),!0)));return s.getName()}doPlayPlayer(e){const t=this.synth;t.playPlayer().then((()=>{D(this._messaging,-1,"updateStatus",{playing:t.isPlaying(),playerPlaying:t.isPlayerPlaying()}),D(this._messaging,e.id,e.method,void 0)}),(t=>{L(this._messaging,e.id,e.method,t)}))}doHookPlayerMIDIEvents(e,t){if(!e)return this.synth.hookPlayerMIDIEvents(null),!0;const s=AudioWorkletGlobalScope[e];return!(!s||"function"!=typeof s)&&(this.synth.hookPlayerMIDIEvents(s,t),!0)}doCallFunction(e,t){const s=AudioWorkletGlobalScope[e];if(!s||"function"!=typeof s)throw new Error("Name not found");s.call(null,this.synth,t)}doRegisterSequencerClient(e,t,s,n){const i=AudioWorkletGlobalScope[s];return i&&"function"==typeof i?M.registerSequencerClient(e,t,i,n):null}process(e,t){if(!this.synth)return!0;const s=this.synth;return s.render(t[0]),D(this._messaging,-1,"updateStatus",{playing:s.isPlaying(),playerPlaying:s.isPlayerPlaying()}),!0}}registerProcessor("fluid-js",e)}()})();
//# sourceMappingURL=js-synthesizer.worklet.min.js.map