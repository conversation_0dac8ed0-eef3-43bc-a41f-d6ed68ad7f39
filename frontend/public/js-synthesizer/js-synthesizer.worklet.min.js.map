{"version": 3, "file": "js-synthesizer.worklet.min.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAQe,MAAMA,EAGpB,WAAAC,CAAoBC,EAA6BC,GAA7B,KAAAD,KAAAA,EAA6B,KAAAC,QAAAA,CACjD,CAEO,OAAAC,GACN,OAAOC,KAAKF,QAAQG,2BAA2BD,KAAKH,KACrD,CACO,OAAAK,CAAQC,GACdH,KAAKF,QAAQM,2BAA2BJ,KAAKH,KAAMM,EACpD,CACO,UAAAE,GACN,OAAOL,KAAKF,QAAQQ,8BAA8BN,KAAKH,KACxD,CACO,UAAAU,CAAWJ,GACjBH,KAAKF,QAAQU,8BAA8BR,KAAKH,KAAMM,EACvD,CACO,MAAAM,GACN,OAAOT,KAAKF,QAAQY,0BAA0BV,KAAKH,KACpD,CACO,MAAAc,CAAOR,GACbH,KAAKF,QAAQc,0BAA0BZ,KAAKH,KAAMM,EACnD,CACO,WAAAU,GACN,OAAOb,KAAKF,QAAQgB,+BAA+Bd,KAAKH,KACzD,CACO,WAAAkB,CAAYZ,GAClBH,KAAKF,QAAQkB,+BAA+BhB,KAAKH,KAAMM,EACxD,CACO,UAAAc,GACN,OAAOjB,KAAKF,QAAQoB,8BAA8BlB,KAAKH,KACxD,CACO,UAAAsB,CAAWhB,GACjBH,KAAKF,QAAQsB,8BAA8BpB,KAAKH,KAAMM,EACvD,CACO,QAAAkB,GACN,OAAOrB,KAAKF,QAAQwB,4BAA4BtB,KAAKH,KACtD,CACO,QAAA0B,CAASpB,GACfH,KAAKF,QAAQ0B,4BAA4BxB,KAAKH,KAAMM,EACrD,CACO,UAAAsB,GACN,OAAOzB,KAAKF,QAAQ4B,8BAA8B1B,KAAKH,KACxD,CACO,UAAA8B,CAAWxB,GACjBH,KAAKF,QAAQ8B,8BAA8B5B,KAAKH,KAAMM,EACvD,CACO,QAAA0B,GACN,OAAO7B,KAAKF,QAAQgC,4BAA4B9B,KAAKH,KACtD,CACO,QAAAkC,CAAS5B,GACfH,KAAKF,QAAQkC,4BAA4BhC,KAAKH,KAAMM,EACrD,CAEO,QAAA8B,CAASC,GACf,MAAMC,EAAOD,EAAKE,WACZC,EAAmBrC,KAAKF,QAAQwC,QAAQH,GAC9B,IAAII,WAAWvC,KAAKF,QAAQ0C,OAAOC,OAAQJ,EAAKF,GACxDO,IAAIR,GACZlC,KAAKF,QAAQ6C,4BAA4B3C,KAAKH,KAAMwC,EAAKF,EAAM,EAChE,CACO,OAAAS,CAAQV,GACd,MAAMC,EAAOD,EAAKE,WACZC,EAAmBrC,KAAKF,QAAQwC,QAAQH,GAC9B,IAAII,WAAWvC,KAAKF,QAAQ0C,OAAOC,OAAQJ,EAAKF,GACxDO,IAAIR,GACZlC,KAAKF,QAAQ+C,2BAA2B7C,KAAKH,KAAMwC,EAAKF,EAAM,EAC/D,CACO,SAAAW,CAAUZ,GAChB,MAAMC,EAAOD,EAAKE,WACZC,EAAmBrC,KAAKF,QAAQwC,QAAQH,GAC9B,IAAII,WAAWvC,KAAKF,QAAQ0C,OAAOC,OAAQJ,EAAKF,GACxDO,IAAIR,GACZlC,KAAKF,QAAQiD,6BAA6B/C,KAAKH,KAAMwC,EAAKF,EAAM,EACjE,EC7Ec,MAAMa,EAEpB,WAAApD,CAAoBC,EAA2BC,GAA3B,KAAAD,KAAAA,EAA2B,KAAAC,QAAAA,CAC/C,CAGO,MAAAmD,GACN,OAAOjD,KAAKH,IACb,CAGO,OAAAqD,GACNlD,KAAKH,KCHyC,CDI/C,CAEO,OAAAE,GACN,OCP8C,IDO1CC,KAAKH,MAAkC,EACpCG,KAAKF,QAAQqD,sBAAsBnD,KAAKH,KAChD,CACO,SAAAuD,GACN,OCX8C,IDW1CpD,KAAKH,MAAkC,EACpCG,KAAKF,QAAQuD,wBAAwBrD,KAAKH,KAClD,CACO,OAAAyD,GACN,OCf8C,IDe1CtD,KAAKH,MAAkC,EACpCG,KAAKF,QAAQyD,sBAAsBvD,KAAKH,KAChD,CACO,UAAAQ,GACN,OCnB8C,IDmB1CL,KAAKH,MAAkC,EACpCG,KAAKF,QAAQ0D,yBAAyBxD,KAAKH,KACnD,CACO,MAAAY,GACN,OCvB8C,IDuB1CT,KAAKH,MAAkC,EACpCG,KAAKF,QAAQ2D,qBAAqBzD,KAAKH,KAC/C,CACO,WAAAgB,GACN,OC3B8C,ID2B1Cb,KAAKH,MAAkC,EACpCG,KAAKF,QAAQ4D,0BAA0B1D,KAAKH,KACpD,CACO,UAAAoB,GACN,OC/B8C,ID+B1CjB,KAAKH,MAAkC,EACpCG,KAAKF,QAAQ6D,yBAAyB3D,KAAKH,KACnD,CACO,QAAAwB,GACN,OCnC8C,IDmC1CrB,KAAKH,MAAkC,EACpCG,KAAKF,QAAQ8D,uBAAuB5D,KAAKH,KACjD,CACO,UAAA4B,GACN,OCvC8C,IDuC1CzB,KAAKH,MAAkC,EACpCG,KAAKF,QAAQ+D,yBAAyB7D,KAAKH,KACnD,CACO,OAAAiE,GACN,OC3C8C,ID2C1C9D,KAAKH,MAAkC,EACpCG,KAAKF,QAAQiE,sBAAsB/D,KAAKH,KAChD,CACO,WAAAmE,GACN,OC/C8C,ID+C1ChE,KAAKH,MAAkC,EACpCG,KAAKF,QAAQmE,0BAA0BjE,KAAKH,KACpD,CACO,OAAAqE,GACN,OCnD8C,IDmD1ClE,KAAKH,MAAkC,EACpCG,KAAKF,QAAQqE,sBAAsBnE,KAAKH,KAChD,CACO,QAAAgC,GACN,OCvD8C,IDuD1C7B,KAAKH,MAAkC,EACpCG,KAAKF,QAAQsE,uBAAuBpE,KAAKH,KACjD,CACO,UAAAwE,GACN,OC3D8C,ID2D1CrE,KAAKH,MAAkC,EACpCG,KAAKF,QAAQwE,0BAA0BtE,KAAKH,KACpD,EEpED,MAAMC,EAAkD,oBAA5ByE,wBAC3BA,wBAAwBC,WAAaC,OAwB/B,SAASC,EAAqBC,EAAiBC,GACrD,OAAQA,EAAMC,MACb,KAAK,EACL,IAAK,OACJ/E,EAAQgF,kBAAkBH,EAAIC,EAAMG,QAASH,EAAMI,IAAKJ,EAAMK,IAAKL,EAAMM,UACzE,MACD,KAAK,EACL,IAAK,SACL,IAAK,UACJpF,EAAQqF,oBAAoBR,EAAIC,EAAMG,QAASH,EAAMI,IAAKJ,EAAMK,KAChE,MACD,KAAK,EACL,IAAK,UACL,IAAK,WACJnF,EAAQsF,qBAAqBT,EAAIC,EAAMG,QAASH,EAAMI,KACtD,MACD,KAAK,EACL,IAAK,eACL,IAAK,iBACJlF,EAAQuF,4BAA4BV,EAAIC,EAAMG,SAC9C,MACD,KAAK,EACL,IAAK,cACL,IAAK,gBACJjF,EAAQwF,2BAA2BX,EAAIC,EAAMG,SAC7C,MACD,KAAK,EACL,IAAK,aACL,IAAK,cACJjF,EAAQyF,yBAAyBZ,EAAIC,EAAMG,QAASH,EAAMY,MAC1D,MACD,KAAK,EACL,IAAK,gBACL,IAAK,iBACJ1F,EAAQ2F,4BAA4Bd,EAAIC,EAAMG,QAASH,EAAMc,QAC7D,MACD,KAAK,EACL,IAAK,gBACL,IAAK,iBACJ5F,EAAQ6F,4BAA4BhB,EAAIC,EAAMG,QAASH,EAAMgB,QAAShB,EAAMY,KAAMZ,EAAMc,QACxF,MACD,KAAK,GACL,IAAK,gBACL,IAAK,iBACJ5F,EAAQ+F,4BAA4BlB,EAAIC,EAAMG,QAASH,EAAMkB,QAASlB,EAAMzE,OAC5E,MACD,KAAK,EACL,IAAK,YACL,IAAK,aACJL,EAAQiG,wBAAwBpB,EAAIC,EAAMG,QAASH,EAAMzE,OACzD,MACD,KAAK,EACL,IAAK,iBACL,IAAK,wBACL,IAAK,mBACL,IAAK,0BACJL,EAAQkG,6BAA6BrB,EAAIC,EAAMG,QAASH,EAAMzE,OAC9D,MACD,KAAK,GACL,IAAK,aACJL,EAAQmG,wBAAwBtB,EAAIC,EAAMG,QAASH,EAAMzE,OACzD,MACD,KAAK,GACL,IAAK,UACJL,EAAQoG,qBAAqBvB,EAAIC,EAAMG,QAASH,EAAMzE,OACtD,MACD,KAAK,GACL,IAAK,MACJL,EAAQqG,iBAAiBxB,EAAIC,EAAMG,QAASH,EAAMzE,OAClD,MACD,KAAK,GACL,IAAK,SACJL,EAAQsG,oBAAoBzB,EAAIC,EAAMG,QAASH,EAAMzE,OACrD,MACD,KAAK,GACL,IAAK,SACL,IAAK,aACL,IAAK,cACJL,EAAQuG,yBAAyB1B,EAAIC,EAAMG,QAASH,EAAMzE,OAC1D,MACD,KAAK,GACL,IAAK,SACL,IAAK,aACL,IAAK,cACJL,EAAQwG,yBAAyB3B,EAAIC,EAAMG,QAASH,EAAMzE,OAC1D,MACD,KAAK,GACL,IAAK,cACL,IAAK,eACL,IAAK,aACJL,EAAQyG,0BAA0B5B,EAAIC,EAAMG,QAASH,EAAMI,IAAKJ,EAAMzE,OACtE,MACD,KAAK,GACL,IAAK,kBACL,IAAK,mBACL,IAAK,qBACJL,EAAQ0G,8BAA8B7B,EAAIC,EAAMG,QAASH,EAAMzE,OAC/D,MACD,KAAK,GACL,IAAK,cACL,IAAK,eACJL,EAAQ2G,0BAA0B9B,GAClC,MACD,KAAK,GACL,IAAK,QACJ7E,EAAQ4G,mBAAmB/B,EAAIC,EAAM1C,MACrC,MACD,QAEC,OAAO,EAET,OAAO,CACR,CC9HA,IAAI,EACAyE,EAEAC,ECVA,EAEAC,EACAC,ECoBA,EACAC,EACA,EACAC,EAGAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEAC,EACAC,EAEAC,EAyCAC,EC5FA,EACAC,EHwCJ,SAASC,EAAUhD,GAClB,MAAMD,EAAK,EAAQkD,mBACnB,OAAKnD,EAAqBC,EAAIC,GAIvBD,GAHN,EAAQmD,oBAAoBnD,GACrB,KAGT,CAGe,MAAMoD,EAQpB,WAAAnI,GAlCI,IAImC,oBAA5B2E,yBACV,EAAUA,wBAAwBC,WAClCmC,EAAkBpC,wBAAwByD,qBAE1C,EAAUvD,OACVkC,EAAkBsB,gBAGnBrB,EACC,EAAQsB,MAAM,kCAAmC,SAAU,CAAC,SAAU,YAwBtElI,KAAKmI,KF/CyC,EEgD9CnI,KAAKoI,QAAU,EACfpI,KAAKqI,eAAiB,CAAC,CACxB,CAGO,WAAAC,GAIN,OAHAtI,KAAKuI,QACLvI,KAAKmI,KAAO,EAAQK,sBAAsB,GAC1CxI,KAAKoI,QAAU,EACRK,QAAQC,SAChB,CAGO,MAAAzF,GACN,OAAOjD,KAAKmI,IACb,CAEO,KAAAI,GFjEwC,IEkE1CvI,KAAKmI,OACRQ,OAAOC,KAAK5I,KAAKqI,gBAAgBQ,SAASC,IACzC9I,KAAK+I,iBAAiBC,OAAOF,GAAa,IAE3C9I,KAAK+I,kBAAkB,GACvB,EAAQE,wBAAwBjJ,KAAKmI,MACrCnI,KAAKmI,KFxEwC,EE0E/C,CAEO,mBAAAe,CAAoBC,GAK1B,IAAIC,EACJ,IALqB,IAAjBpJ,KAAKoI,SACR,EAAQiB,mCAAmCrJ,KAAKmI,KAAMnI,KAAKoI,QAC3DpI,KAAKoI,QAAU,GAGK,iBAAVe,EACVC,EAAMD,MACA,MAAIA,aAAiBG,GAG3B,OAAOb,QAAQc,OAAO,IAAIC,UAAU,8CAFpCJ,EAAMD,EAAMM,mB,CAMb,OADAzJ,KAAKoI,OAAS,EAAQsB,qCAAqC1J,KAAKmI,KAAMiB,GAC/DX,QAAQC,QAAQ1I,KAAKoI,OAC7B,CAEO,gBAAAW,CAAiBY,GACvB,IAAkB,IAAdA,IAEe,KADlBA,EAAW3J,KAAKoI,QAEf,OAKF,MAAMzD,EAAK,EAAQkD,mBAQnB,GAPA,EAAQ+B,wBAAwBjF,GAAK,GACrC,EAAQkF,sBAAsBlF,EAAIgF,GAClC,EAAQG,2BAA2BnF,GACnC,EAAQoF,0BAA0B/J,KAAKmI,KAAMxD,GAC7C,EAAQmD,oBAAoBnD,GAE5B,EAAQ0E,mCAAmCrJ,KAAKmI,KAAMwB,GAClD3J,KAAKoI,SAAWuB,EACnB3J,KAAKoI,QAAU,MACT,CACN,MAAM4B,EAAMhK,KAAKqI,eACb2B,EAAIL,KACPhD,EAAgBqD,EAAIL,WACbK,EAAIL,G,CAGd,CAEO,uBAAAM,GACN,MAAMC,EAAI,EAAQC,+BAA+BnK,KAAKmI,MAChDiC,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIH,IAAKG,EAAG,CAC3B,MAAMC,EAAK,EAAQC,+BAA+BvK,KAAKmI,KAAMkC,GACvDG,EAAO5D,EAAgC5G,KAAKmI,KAAMmC,GACxDF,EAAEK,KAAK,CAAEd,SAAUW,EAAIE,KAAMA,G,CAE9B,OAAO/B,QAAQC,QAAQ0B,EACxB,CAEO,cAAAM,GACN,OAAOjC,QAAQC,QAAgB,EAAQyB,+BAA+BnK,KAAKmI,MAC5E,CAEO,aAAAwC,CAAcC,GACpB,MAAMN,EAAK,EAAQC,+BAA+BvK,KAAKmI,KAAMyC,GACvDJ,EAAO5D,EAAgC5G,KAAKmI,KAAMmC,GACxD,OAAO7B,QAAQC,QAAoB,CAAEiB,SAAUW,EAAIE,KAAMA,GAC1D,CAEO,YAAAK,CAAaC,GACnB,EAAQC,gCAAgC/K,KAAKmI,KAAM2C,EACpD,CAEO,YAAAE,GACN,OAAOvC,QAAQC,QAAQ,EAAQuC,gCAAgCjL,KAAKmI,MACrE,CAEO,OAAA+C,GACN,OAAOzC,QAAQC,QAAQ,EAAQyC,0BAA0BnL,KAAKmI,MAC/D,CAEO,WAAAiD,CAAYxG,EAAuByG,EAAcC,GACvD,MAAM3G,EAAKiD,EAAUhD,GACrB,GAAW,OAAPD,EAAa,CAEhB,MAAM4G,EAAQ,EAAQpB,+BAA+BnK,KAAKmI,MAC1D,IAAK,IAAIkC,EAAI,EAAGA,EAAIkB,IAASlB,EAAG,CAC/B,MAAMC,EAAa,EAAQC,+BAA+BvK,KAAKmI,KAAMkC,GACrE,EAAQR,sBAAsBlF,EAAI2F,GAClC,EAAQkB,yBAAyBxL,KAAKmI,KAAMxD,EAAI0G,EAAMC,EAAa,EAAI,E,CAExE,EAAQxD,oBAAoBnD,E,CAE9B,CAEO,mBAAA8G,CAAoB9B,EAAkB/E,EAAuByG,EAAcC,GACjF,MAAM3G,EAAKiD,EAAUhD,GACV,OAAPD,IACH,EAAQkF,sBAAsBlF,GAAkB,IAAdgF,EAAkB3J,KAAKoI,OAASuB,GAClE,EAAQ6B,yBAAyBxL,KAAKmI,KAAMxD,EAAI0G,EAAMC,EAAa,EAAI,GACvE,EAAQxD,oBAAoBnD,GAE9B,CAGO,oBAAA+G,CAAqB/B,EAAkB/E,GAC7C,MAAMD,EAAKiD,EAAUhD,GACV,OAAPD,IACH,EAAQkF,sBAAsBlF,GAAkB,IAAdgF,EAAkB3J,KAAKoI,OAASuB,GAClE,EAAQI,0BAA0B/J,KAAKmI,KAAMxD,GAC7C,EAAQmD,oBAAoBnD,GAE9B,CAGO,YAAAgH,CAAahC,EAAkBiC,GACrC,KAAMA,aAAqB5I,GAC1B,OAED,MAAM2B,EAAKiH,EAAU3I,SFjMyB,IEkM1C0B,IACH,EAAQkF,sBAAsBlF,GAAkB,IAAdgF,EAAkB3J,KAAKoI,OAASuB,GAClE,EAAQI,0BAA0B/J,KAAKmI,KAAMxD,GAE/C,CAEO,eAAAkH,GACN,EAAQC,+BAA+B9L,KAAKmI,MAAO,GAAI,GAAI,EAC5D,CAEO,yBAAA4D,CAA0BpC,GAChC,EAAQmC,+BAA+B9L,KAAKmI,MAAO,GAAiB,IAAdwB,EAAkB3J,KAAKoI,OAASuB,GAAW,EAClG,CAEO,gBAAAqC,CAAiBC,GFhNuB,IEiN1CjM,KAAKmI,MACR,EAAQ+D,yBAAyBlM,KAAKmI,KAAM8D,EAE9C,CAGO,uBAAAE,CAAwBC,GAC9B,OAAOC,aAAY,IAAMrM,KAAKgM,iBAAiBI,IAAOA,EACvD,ECtMc,MAAME,EAIpB,YAAmBC,GAClBvM,KAAKH,KAAO0M,CACb,CAEO,uBAAOC,CAAiBrD,EAAoBmB,GAxB/C,IAKH,EADsC,oBAA5B/F,wBACAA,wBAAwBC,WAExBC,OAGXoC,EACC,EAAQqB,MAAM,uBAAwB,SAAU,CAAC,WAClDpB,EACC,EAAQoB,MAAM,wBAAyB,SAAU,CAAC,YAclD,MAAMuE,EAAQ,EAAQC,6BAA6BvD,EAAMM,oBAAqBa,GAC9E,OH/B8C,IG+B1CmC,EACI,KAED,IAAIH,EAAUG,EACtB,CAEO,OAAAE,GACN,OAAO9F,EAAqB7G,KAAKH,KAClC,CAEO,SAAA+M,CAAUpH,EAAcqH,GAC9B,MAAMC,EAA2B,EAAQC,wBAAwB/M,KAAKH,KAAM2F,EAAMqH,GAClF,GH3C8C,IG2C1CC,EACH,OAAO,KAKR,MAAO,CACNE,UAAWhN,KACXwK,KALY1D,EAAsBgG,GAMlCG,QALe,EAAQC,0BAA0BJ,GAMjDK,IALW,EAAQC,sBAAsBN,GAO3C,CAEO,iBAAAO,GACN,MAAMC,EAAQ,KACb,EAAQC,6BAA6BvN,KAAKH,KAAK,EAE1C2N,EAAO,KACZ,MAAMV,EAAY,EAAQW,4BAA4BzN,KAAKH,MAC3D,GAAkB,IAAdiN,EACH,MAAO,CACNY,MAAM,EACNvN,WAAOwN,GAMR,MAAO,CACND,MAAM,EACNvN,MAAO,CACN6M,UAAWhN,KACXwK,KAPW1D,EAAsBgG,GAQjCG,QAPc,EAAQC,0BAA0BJ,GAQhDK,IAPU,EAAQC,sBAAsBN,I,EAkB5C,MAAO,CACN,CAACc,OAAOC,UAPQ,KAChBP,IACO,CACNE,SAMH,ECtDD,SAAS,IACR,IAAIpG,EAAJ,CAKA,GAAuC,oBAA5B7C,wBACV,EAAUA,wBAAwBC,WAClCuC,EAAexC,wBAAwBuJ,gBACvC,EAAkBvJ,wBAAwByD,uBACpC,IAAsB,oBAAXvD,OAKjB,MAAM,IAAIsJ,MAAM,oEAJhB,EAAUtJ,OACVsC,EAAeiH,YACf,EAAkB/F,c,CAInBjB,EAAM,EAAQiH,GAGdhH,EACC,EAAQiB,MAAM,wBAAyB,SAAU,CAAC,SAAU,SAAU,WACvEhB,EACC,EAAQgB,MAAM,wBAAyB,SAAU,CAAC,SAAU,SAAU,WACvEf,EACC,EAAQe,MAAM,wBAAyB,SAAU,CAAC,SAAU,SAAU,WACvEd,EACC,EAAQc,MAAM,oBAAqB,SAAU,CAAC,WAC/Cb,EACC,EAAQa,MAAM,qBAAsB,SAAU,CAAC,SAAU,SAAU,WACpEZ,EACC,EAAQY,MAAM,kCAAmC,SAAU,CAAC,SAAU,SAAU,SAAU,WAE3FX,EAAS,EAAQjF,QAAQ4L,KAAK,GAC9B1G,EAAO,EAAQ2G,MAAMD,KAAK,GAE1BzG,EAA2B,EAAQ2G,+BAA+BF,KAAK,E,CACxE,CAyCA,SAASG,EAAwBC,EAAsB9D,EAAcrK,QAC/C,IAAVA,GACV8G,EAAsBqH,EAAU9D,EAAMrK,EAAQ,EAAI,EAEpD,CACA,SAASoO,EAAuBD,EAAsB9D,EAAcrK,QAC9C,IAAVA,GACV8G,EAAsBqH,EAAU9D,EAAMrK,EAExC,CACA,SAASqO,EAAuBF,EAAsB9D,EAAcrK,QAC9C,IAAVA,GACV+G,EAAsBoH,EAAU9D,EAAMrK,EAExC,CA8He,MAAMmJ,EA+BpB,WAAA1J,GACC,IAEAI,KAAKyO,UJlSyC,EImS9CzO,KAAK0O,OJnSyC,EIoS9C1O,KAAK2O,QJpSyC,EIqS9C3O,KAAK4O,gBAAiB,EACtB5O,KAAK6O,mBAAqB,KAC1B7O,KAAK8O,oBAAsB,KAE3B9O,KAAK+O,QJzSyC,EI0S9C/O,KAAKgP,YAAc,EACnBhP,KAAKiP,QJ3SyC,EI6S9CjP,KAAKkP,MAAQ,EACd,CAGO,6BAAOC,GACb,OApOF,WACC,GAAIzH,EACH,OAAOA,EAGR,IAAI0H,EACAC,EACJ,GAAuC,oBAA5B9K,wBACV6K,EAAM7K,wBAAwBC,WAC9B6K,EAAiB9K,wBAAwB+K,iBACnC,IAAsB,oBAAX7K,OAIjB,OAAOgE,QAAQc,OAAO,IAAIwE,MAAM,qEAHhCqB,EAAM3K,OACN4K,EAAyC,oBAAjBC,aAA+BA,kBAAe3B,C,CAIvE,OAAIyB,EAAIG,WACP7H,EAA4Be,QAAQC,UAC7BhB,IAGPA,EAA4B,IAAIe,aADH,IAAnB4G,EAC+B3G,IACxC,MAAM8G,EAA+B,EAAQC,qBAC7C,EAAQA,qBAAuB,KAC9B/G,IACI8G,GACHA,G,CAED,EAGuC9G,IACxC2G,EAAgB3G,EAAQ,GAGnBhB,EACR,CAgMSgI,EACR,CAEO,aAAAC,GACN,OJtT8C,IIsTvC3P,KAAK0O,MACb,CAGO,iBAAAjF,GACN,OAAOzJ,KAAK0O,MACb,CAEO,eAAAkB,CACNC,EACAC,GAEA,MAAMC,EAAOF,EAAQG,sBAAsBF,EAAW,EAAG,GAIzD,OAHAC,EAAKE,iBAAiB,gBAAiBtL,IACtC3E,KAAKkQ,OAAOvL,EAAGwL,aAAa,IAEtBJ,CACR,CAEO,IAAAK,CAAKC,EAAoB/B,GAC/BtO,KAAKuI,QAEL,MAAM7F,EAAO1C,KAAKyO,UAAY,EAAQ6B,sBACtCpJ,EAAsBxE,EAAK,oBAAqB2N,GAC5C/B,SACiC,IAAzBA,EAASiC,cACnBvQ,KAAKkP,MAAQZ,EAASiC,aAEvBlC,EACC3L,EACA,sBACA4L,EAASkC,cAEVhC,EACC9L,EACA,qBACA4L,EAASmC,aAEVjC,EACC9L,EACA,qBACA4L,EAASoC,aAEVnC,EAAuB7L,EAAK,kBAAmB4L,EAASqC,UACxDnC,EACC9L,EACA,qBACA4L,EAASsC,aAEVrC,EACC7L,EACA,sBACA4L,EAASuC,kBAvOb,SAAgCvC,EAAsB9D,EAAcrK,QAC9C,IAAVA,GACVgH,EAAsBmH,EAAU9D,EAAMrK,EAExC,CAqOG2Q,CACCpO,EACA,yBACA4L,EAASyC,gBAEVxC,EACC7L,EACA,wBACA4L,EAAS0C,eAEVxC,EACC9L,EACA,qBACA4L,EAAS2C,aAEVzC,EACC9L,EACA,2BACA4L,EAAS4C,6BAEwC,IAAvC5C,EAAS6C,2BACnBhK,EACCzE,EACA,oCACA4L,EAAS6C,0BAA0BC,KAAK,MAG1C5C,EACC9L,EACA,4BACA4L,EAAS+C,oBAEV7C,EACC9L,EACA,0BACA4L,EAASgD,kBAEV9C,EACC9L,EACA,2BACA4L,EAASiD,mBAEV/C,EACC9L,EACA,wBACA4L,EAASkD,gBAEVjD,EAAuB7L,EAAK,kBAAmB4L,EAASmD,WACxDpD,EACC3L,EACA,sBACA4L,EAASoD,cAEVlD,EACC9L,EACA,oBACA4L,EAASqD,YAEVnD,EACC9L,EACA,qBACA4L,EAASsD,aAEVpD,EACC9L,EACA,yBACA4L,EAASuD,gBAEVrD,EACC9L,EACA,qBACA4L,EAASwD,cAGX5K,EAAsBxE,EAAK,aAAc1C,KAAKkP,OAE9ClP,KAAK0O,OAAS,EAAQqD,iBAAiB/R,KAAKyO,WAE5CzO,KAAKiP,QAAU1H,EAAO,EACvB,CAEO,KAAAgB,GJ7bwC,II8b1CvI,KAAK0O,SAGT1O,KAAKgS,eACL,EAAQC,oBAAoBjS,KAAK0O,QACjC1O,KAAK0O,OJncyC,EIoc9C,EAAQwD,uBAAuBlS,KAAKyO,WACpCzO,KAAKyO,UJrcyC,EIsc9CjH,EAAKxH,KAAKiP,SACVjP,KAAKiP,QJvcyC,EIwc/C,CAEO,SAAAkD,GACN,OJ3c8C,II4c7CnS,KAAK0O,QAnUR,SAA6BvF,GAC5B,MAAMiJ,EAAc,EAAQC,oCAAoClJ,GAChE,IAAKiJ,EACJ,OAAO,EAcR,IAAIE,EAAoB,IACpBC,EAA4BpJ,EAAQmJ,EAAoB,GAAM,EAC9DE,EAAyB,EAAQC,QAAQF,GAC7C,GAAIC,IAA2BJ,IAE9BE,GAAqB,EACrBC,EAA4BpJ,EAAQmJ,EAAoB,GAAM,EAC9DE,EAAyB,EAAQC,QAAQF,GACrCC,IAA2BJ,GAM9B,OAJUM,QACRC,KACD,2EAEMP,EAIT,MAAMQ,EAAY,EAAQH,QAAStJ,EAAQmJ,GAAsB,GAEjE,IAAKM,GAAaA,GAAa,EAAQH,QAAQrQ,WAM9C,OAJUsQ,QACRC,KACD,2EAEMP,EAIR,MAAMS,EAAa,EAAQC,2BAA2B3J,GACtD,IAAI4J,GAAY,EAChB,IAAK,IAAI1I,EAAI,EAAGA,EAAIwI,IAAcxI,EAAG,CAEpC,MAAM2I,EAAQ,EAAQP,SAASG,GAAa,GAAKvI,GACjD,GAAK2I,GAMU,IAFA,EAAQxQ,OAAOwQ,EAAQ,GAEpB,CACjBD,GAAY,EACZ,K,EAGF,IAAKA,EASJ,OAR+B,IAA3BP,GACOE,QACRC,KACD,0EACAH,GAGF,EAAQC,QAAQF,GAA4B,EACrC,EAGR,OAAOH,CACR,CAwPGa,CAAoBjT,KAAK0O,QAAU,CAErC,CAEO,gBAAAwE,CAAiB/S,EAA4B4E,GACnD/E,KAAKmT,yBACkB,IAAZpO,IACVA,GAAW,GAEZ,EAAQqO,+BAA+BpT,KAAK0O,OAAQ3J,EAAS5E,EAC9D,CAEO,OAAAkT,GACN,OAAOrT,KAAKkP,KACb,CAEO,OAAAoE,CAAQC,GACdvT,KAAKmT,oBACL,EAAQK,sBAAsBxT,KAAK0O,OAAQ6E,GAC3CvT,KAAKkP,MAAQ,EAAQuE,sBAAsBzT,KAAK0O,OACjD,CAEO,cAAAgF,CAAe3O,EAAiB4O,GACtC3T,KAAKmT,oBAEL,EAAQS,8BACP5T,KAAK0O,OACL3J,EACA4O,EAAS,EAAI,EAEf,CAEO,oBAAAE,GACN,OAAO7T,KAAK8T,kBACb,CAEO,SAAAC,CAAUC,GAChBhU,KAAKmT,oBAEL,MAAM3I,GA7RkCyJ,EA6RC,OA5RnC,IA4R0B,YA5RG,MAAhBC,KAAKC,YAAoC,MAAhBD,KAAKC,WAAmBF,KADtE,IAA0CA,EA8RxC,MAAMG,EAAK,IAAI7R,WAAWyR,GAE1BhN,EAAIqN,UAAU7J,EAAM4J,GACpB,MAAM3H,EAAQpF,EAAmBrH,KAAK0O,OAAQlE,EAAM,GAEpD,OADAxD,EAAIsN,OAAO9J,IACO,IAAXiC,EACJhE,QAAQc,OAAO,IAAIwE,MAAM3G,EAAmBpH,KAAK0O,UACjDjG,QAAQC,QAAQ+D,EACpB,CAEO,WAAA8H,CAAYjK,GAClBtK,KAAKmT,oBACLnT,KAAKwU,aACLxU,KAAKyU,kBAEL,EAAQC,sBAAsB1U,KAAK0O,OAAQpE,EAAI,EAChD,CAEO,gBAAAqK,CAAiBrK,GAIvB,OAFAtK,KAAKmT,oBACLnT,KAAKwU,aACExU,KAAK8T,mBAAmBc,MAAK,KACnC,EAAQF,sBAAsB1U,KAAK0O,OAAQpE,EAAI,EAAE,GAEnD,CAOO,cAAAuK,CAAejP,GACrB,OAAO0G,EAAUE,iBAAiBxM,KAAM4F,EACzC,CAEO,kBAAAkP,CAAmBxK,GAEzB,OADAtK,KAAKmT,oBACE1K,QAAQC,QACd,EAAQqM,6BAA6B/U,KAAK0O,OAAQpE,GAEpD,CACO,kBAAA0K,CAAmB1K,EAAY2K,GACrCjV,KAAKmT,oBACL,EAAQ+B,6BAA6BlV,KAAK0O,OAAQpE,EAAI2K,EACvD,CAEO,MAAA/E,CAAOiF,GACb,MAAMC,EACL,qBAAsBD,EACnBA,EAAUE,OACVF,EAAU,GAAGE,OACXC,EACL,qBAAsBH,EACnBA,EAAUI,iBACVJ,EAAUE,OACRG,EAAiB,EAAIJ,EACrBK,EAA6B,EAAjBD,EACdxV,KAAKgP,YAAcyG,IJ/iBuB,IIgjBzCzV,KAAK+O,SACRvH,EAAKxH,KAAK+O,SAEX/O,KAAK+O,QAAUxH,EAAOkO,GACtBzV,KAAKgP,YAAcyG,GAGpB,MAAMC,EAAU1V,KAAK+O,QACf4G,EAAa3V,KAAK+O,QACvByG,EACDxV,KAAK4V,UAAUF,EAASC,EAAUP,GAElC,MAAMS,EAAQ,IAAIC,aACjB,EAAQtT,OAAOC,OACfiT,EACAN,GAEKW,EACLT,GAAY,EACT,IAAIQ,aAAa,EAAQtT,OAAOC,OAAQkT,EAAUP,GAClD,KACJ,GAAI,qBAAsBD,EACzB,GAAIA,EAAUa,cACbb,EAAUa,cAAcH,EAAO,EAAG,GAC9BE,GACHZ,EAAUa,cAAcD,EAAQ,EAAG,OAE9B,CAEN,MAAME,EAAWd,EAAUe,eAAe,GAE1C,GADAL,EAAMhN,SAAQ,CAACO,EAAKiB,IAAO4L,EAAS5L,GAAKjB,IACrC2M,EAAQ,CACX,MAAMI,EAAYhB,EAAUe,eAAe,GAC3CH,EAAOlN,SAAQ,CAACO,EAAKiB,IAAO8L,EAAU9L,GAAKjB,G,OAI7C+L,EAAU,GAAGzS,IAAImT,GACbE,GACHZ,EAAU,GAAGzS,IAAIqT,GAKnB/V,KAAKoW,iBACN,CAEO,UAAAC,CAAWC,EAActR,EAAaC,GAC5C,EAAQsR,oBAAoBvW,KAAK0O,OAAQ4H,EAAMtR,EAAKC,EACrD,CACO,WAAAuR,CAAYF,EAActR,GAChC,EAAQyR,qBAAqBzW,KAAK0O,OAAQ4H,EAAMtR,EACjD,CACO,eAAA0R,CAAgBJ,EAActR,EAAaoE,GACjD,EAAQuN,0BAA0B3W,KAAK0O,OAAQ4H,EAAMtR,EAAKoE,EAC3D,CACO,WAAAwN,CAAYN,EAAcO,EAAczN,GAC9C,EAAQ0N,gBAAgB9W,KAAK0O,OAAQ4H,EAAMO,EAAMzN,EAClD,CACO,iBAAA2N,CAAkBT,EAAcU,GACtC,EAAQC,4BAA4BjX,KAAK0O,OAAQ4H,EAAMU,EACxD,CACO,mBAAAE,CAAoBZ,EAAclN,GACxC,EAAQ+N,8BAA8BnX,KAAK0O,OAAQ4H,EAAMlN,EAC1D,CACO,aAAAgO,CAAcd,EAAclN,GAClC,EAAQiO,wBAAwBrX,KAAK0O,OAAQ4H,EAAMlN,EACpD,CACO,SAAAkO,CAAUpV,GAChB,MAAMqV,EAAMrV,EAAKE,WACXoV,EAAMjQ,EAAOgQ,GACnB,EAAQ/U,OAAOE,IAAIR,EAAMsV,GACzB,EAAQC,mBACPzX,KAAK0O,OACL8I,EACAD,EJ3nB6C,MI+nB7C,GAED/P,EAAKgQ,EACN,CAEO,yBAAAE,CAA0BpB,EAAclN,GAC9C,EAAQuO,8BAA8B3X,KAAK0O,OAAQ4H,EAAMlN,EAC1D,CACO,cAAA2H,CAAeuF,EAAc9Q,GACnC,EAAQoS,yBAAyB5X,KAAK0O,OAAQ4H,EAAM9Q,EACrD,CACO,eAAAqS,CAAgBvB,EAAc1Q,GACpC,EAAQkS,0BAA0B9X,KAAK0O,OAAQ4H,EAAM1Q,EACtD,CACO,iBAAAmS,CACNzB,EACA1Q,EACAJ,EACAqH,GAEA,EAAQmL,4BACPhY,KAAK0O,OACL4H,EACA1Q,EACAJ,EACAqH,EAEF,CACO,gBAAAoL,CAAiB3B,GACvB,EAAQ4B,2BAA2BlY,KAAK0O,OAAQ4H,EACjD,CACO,gBAAA6B,GACN,EAAQC,2BAA2BpY,KAAK0O,OACzC,CACO,eAAA2J,GACN,EAAQC,0BAA0BtY,KAAK0O,OACxC,CACO,eAAA6J,CAAgBjC,GACtB,EAAQkC,2BACPxY,KAAK0O,YACW,IAAT4H,GAAwB,EAAIA,EAErC,CACO,gBAAAmC,CAAiBnC,GACvB,EAAQoC,4BACP1Y,KAAK0O,YACW,IAAT4H,GAAwB,EAAIA,EAErC,CACO,kBAAAqC,CAAmBrC,EAAc3C,GAGvC,EAAQC,8BACP5T,KAAK0O,OACL4H,EACA3C,EAAS,EAAI,EAEf,CAKO,SAAAiF,CACNC,EACAC,EACAC,EACAC,GAEA,EAAQC,wBACPjZ,KAAK0O,OACLmK,EACAC,EACAC,EACAC,EAEF,CAIO,iBAAAE,CAAkBL,GACxB,EAAQM,iCAAiCnZ,KAAK0O,OAAQmK,EACvD,CAIO,aAAAO,CAAcN,GACpB,EAAQO,6BAA6BrZ,KAAK0O,OAAQoK,EACnD,CAIO,cAAAQ,CAAeP,GACrB,EAAQQ,8BAA8BvZ,KAAK0O,OAAQqK,EACpD,CAIO,cAAAS,CAAeR,GACrB,EAAQS,8BAA8BzZ,KAAK0O,OAAQsK,EACpD,CAIO,WAAAU,CAAYC,GAClB,EAAQC,2BAA2B5Z,KAAK0O,OAAQiL,EAAK,EAAI,EAC1D,CAIO,iBAAAE,GACN,OAAO,EAAQC,iCAAiC9Z,KAAK0O,OACtD,CAIO,aAAAqL,GACN,OAAO,EAAQC,6BAA6Bha,KAAK0O,OAClD,CAIO,cAAAuL,GACN,OAAO,EAAQC,8BAA8Bla,KAAK0O,OACnD,CAIO,cAAAyL,GACN,OAAO,EAAQC,8BAA8Bpa,KAAK0O,OACnD,CAKO,SAAA2L,CACNxH,EACAmG,EACAsB,EACAC,EACA1V,GAEA,EAAQ2V,wBACPxa,KAAK0O,OACLmE,EACAmG,EACAsB,EACAC,EACA1V,EAEF,CAIO,mBAAA4V,CAAoB5H,GAC1B,EAAQ6H,2BAA2B1a,KAAK0O,OAAQmE,EACjD,CAIO,cAAA8H,CAAe3B,GACrB,EAAQ4B,8BAA8B5a,KAAK0O,OAAQsK,EACpD,CAIO,cAAA6B,CAAeP,GACrB,EAAQQ,8BAA8B9a,KAAK0O,OAAQ4L,EACpD,CAIO,cAAAS,CAAeR,GACrB,EAAQS,8BAA8Bhb,KAAK0O,OAAQ6L,EACpD,CAIO,aAAAU,CAAcpW,GACpB,EAAQqW,6BAA6Blb,KAAK0O,OAAQ7J,EACnD,CAIO,WAAAsW,CAAYxB,GAClB,EAAQyB,2BAA2Bpb,KAAK0O,OAAQiL,EAAK,EAAI,EAC1D,CAIO,mBAAA0B,GACN,OAAO,EAAQC,2BAA2Btb,KAAK0O,OAChD,CAIO,cAAA6M,GACN,OAAO,EAAQC,8BAA8Bxb,KAAK0O,OACnD,CAIO,cAAA+M,GACN,OAAO,EAAQC,8BAA8B1b,KAAK0O,OACnD,CAIO,cAAAiN,GACN,OAAO,EAAQC,8BAA8B5b,KAAK0O,OACnD,CAIO,aAAAmN,GACN,OAAO,EAAQC,6BAA6B9b,KAAK0O,OAClD,CAQO,YAAAqN,CAAahX,EAAiBiX,GACpC,OAAO,EAAQC,qBAAqBjc,KAAK0O,OAAQ3J,EAASiX,EAC3D,CAOO,YAAAE,CAAanX,EAAiBiX,EAAuB7b,GAC3D,EAAQgc,qBAAqBnc,KAAK0O,OAAQ3J,EAASiX,EAAO7b,EAC3D,CAMO,aAAAic,CAAcrX,GAMpB,OALA,EAAQsX,6BACPrc,KAAK0O,OACL3J,EACA/E,KAAKiP,SAEC,EAAQqN,OAAQtc,KAAKiP,SAAsB,EACnD,CAMO,aAAAsN,CAAcxX,EAAiByX,GACrC,EAAQC,6BAA6Bzc,KAAK0O,OAAQ3J,EAASyX,EAC5D,CAMO,iBAAAE,CAAkB3X,GAMxB,OALA,EAAQ4X,iCACP3c,KAAK0O,OACL3J,EACA/E,KAAKiP,SAEC,EAAQqN,OAAQtc,KAAKiP,SAAsB,EACnD,CAMO,iBAAA2N,CAAkB7X,EAAiByX,GACzC,EAAQK,iCAAiC7c,KAAK0O,OAAQ3J,EAASyX,EAChE,CAMO,aAAAM,CAAc/X,GAMpB,OALA,EAAQgY,6BACP/c,KAAK0O,OACL3J,EACA/E,KAAKiP,SAEC,EAAQqN,OAAQtc,KAAKiP,SAAsB,EACnD,CAMO,aAAA+N,CAAcjY,EAAiBkY,GACrC,EAAQC,6BAA6Bld,KAAK0O,OAAQ3J,EAASkY,EAC5D,CAIO,WAAAE,GACN,OAAO,IAAI1U,SAAeC,IACzB1I,KAAKod,cACL1U,GAAS,GAEX,CAEO,WAAA2U,GACNrd,KAAKgS,cACN,CAGQ,WAAAoL,GACPpd,KAAKgS,eAEL,MAAMsL,EAAS,EAAQC,kBAAkBvd,KAAK0O,QAE9C,GADA1O,KAAK2O,QAAU2O,EJ57B+B,II67B1CA,EAcH,MAAM,IAAIvP,MAAM,iBAbhB,GAAiC,OAA7B/N,KAAK8O,oBAA8B,CAItC,MAAM0O,EACL,EAAQ/K,QAAU6K,EAAoB,KAAQ,GAE9C,EAAQ7K,QAAU6K,EAAoB,KAAQ,KAC9Btd,KAAK0O,SACrB1O,KAAK8O,oBAAsB0O,E,CAM/B,CAGQ,YAAAxL,GACP,MAAMyL,EAAIzd,KAAK2O,QJj9B+B,IIk9B1C8O,IAGJzd,KAAKwU,aACL,EAAQkJ,qBAAqBD,GAC7Bzd,KAAK2O,QJv9ByC,EIw9B9C3O,KAAK6O,mBAAqB,KAC3B,CAEO,eAAAuH,GACN,GAAIpW,KAAK4O,eAAgB,CAExB,GAAe,IADA,EAAQ+O,yBAAyB3d,KAAK2O,SAEpD,OAAO,EAER3O,KAAKwU,Y,CAEN,OAAO,CACR,CAEO,kBAAAoJ,CAAmB5J,GACzBhU,KAAK6d,0BACL,MAAMtG,EAAMvD,EAAI5R,WACVoV,EAAMjQ,EAAOgQ,GACnB,EAAQ/U,OAAOE,IAAI,IAAIH,WAAWyR,GAAMwD,GACxC,MAAMpN,EAAY,EAAQ0T,sBAAsB9d,KAAK2O,QAAS6I,EAAKD,GAEnE,OADA/P,EAAKgQ,IACS,IAAPpN,EACJ3B,QAAQC,UACRD,QAAQc,OAAO,IAAIwE,MAAM3G,EAAmBpH,KAAK0O,SACrD,CAEO,UAAAqP,GAMN,GALA/d,KAAK6d,0BACD7d,KAAK4O,gBACR5O,KAAKwU,cAG4C,IAA9C,EAAQwJ,mBAAmBhe,KAAK2O,SACnC,OAAOlG,QAAQc,OAAO,IAAIwE,MAAM3G,EAAmBpH,KAAK0O,UAEzD1O,KAAK4O,gBAAiB,EACtB,IAAIqP,EAAW,OACf,MAAMR,EAAI,IAAIhV,SAAeC,IAC5BuV,EAAWvV,CAAO,IAMnB,OAJA1I,KAAKke,aAAe,CACnBC,QAASV,EACT/U,QAASuV,GAEHxV,QAAQC,SAChB,CAEO,UAAA8L,GACN,MAAMiJ,EAAIzd,KAAK2O,QJxgC+B,IIygC1C8O,GAA0Bzd,KAAK4O,iBAGnC,EAAQwP,mBAAmBX,GAC3B,EAAQY,mBAAmBZ,GAC3B,EAAQ/E,4BAA4B1Y,KAAK0O,QAAS,GAC9C1O,KAAKke,eACRle,KAAKke,aAAaxV,UAClB1I,KAAKke,kBAAe,GAErBle,KAAK4O,gBAAiB,EACvB,CAEO,yBAAA0P,GAEN,OADAte,KAAK6d,0BACEpV,QAAQC,QACd,EAAQ6V,+BAA+Bve,KAAK2O,SAE9C,CACO,wBAAA6P,GAEN,OADAxe,KAAK6d,0BACEpV,QAAQC,QACd,EAAQ+V,8BAA8Bze,KAAK2O,SAE7C,CACO,iBAAA+P,GAEN,OADA1e,KAAK6d,0BACEpV,QAAQC,QAAQ,EAAQiW,sBAAsB3e,KAAK2O,SAC3D,CACO,uBAAAiQ,GAEN,OADA5e,KAAK6d,0BACEpV,QAAQC,QACd,EAAQmW,6BAA6B7e,KAAK2O,SAE5C,CACO,UAAAmQ,CAAWC,GACjB/e,KAAK6d,0BACL,EAAQmB,mBAAmBhf,KAAK2O,QAASoQ,EAC1C,CACO,aAAAE,CAAcC,GACpBlf,KAAK6d,0BACL,EAAQsB,uBAAuBnf,KAAK2O,QAASuQ,EAC9C,CACO,cAAAE,CAAeC,EAA+BC,GACpDtf,KAAK6d,0BACL,EAAQ0B,wBAAwBvf,KAAK2O,QAAS0Q,EAAWC,EAC1D,CAQO,oBAAAE,CACNC,EACAzD,GAEAhc,KAAK6d,0BAEL,MAAM6B,EAAS1f,KAAK6O,mBACpB,GAAe,OAAX6Q,GAAgC,OAAbD,EACtB,OAED,MAAME,EAEQ,OAAbF,EACG1Y,EAv1BN,SAA+BoC,EAAoByW,EAA2B5D,GAC7E,MAAO,CAAC9Z,EAAmB0C,KAC1B,MAAMib,EAAI,EAAQ5f,2BAA2B2E,GAC7C,OAAIgb,EAAGzW,EAAO0W,EAAG,IAAIlgB,EAAUiF,EAAO,GAAUoX,GACxC,EAED,EAAQ5N,+BAA+BlM,EAAM0C,EAAM,CAE5D,CAg1BMkb,CAAsB9f,KAAMyf,EAAUzD,GACtC,OAI2B,OAA7Bhc,KAAK8O,oBACH,KACA/H,EAAaU,EAA0B,OAE5B,OAAXiY,GAA8B,OAAXC,GAEtB,EAAQI,oCACP/f,KAAK2O,QACLgR,EACA3f,KAAK0O,QAEN,EAAgBgR,IAED,OAAXC,GAEH,EAAQI,oCACP/f,KAAK2O,QACL3O,KAAK8O,oBACL9O,KAAK0O,QAEN,EAAgBgR,IAEhB,EAAQK,oCACP/f,KAAK2O,QACLgR,EACA3f,KAAK0O,QAIR1O,KAAK6O,mBAAqB8Q,CAC3B,CAGQ,iBAAAxM,GACP,GJpnC8C,IIonC1CnT,KAAK0O,OACR,MAAM,IAAIX,MAAM,iCAElB,CAGQ,uBAAA8P,GACP7d,KAAKmT,oBJ3nCyC,II4nC1CnT,KAAK2O,SACR3O,KAAKod,aAEP,CAGQ,SAAAxH,CACPF,EACAC,EACAP,GAEA,EAAQ4K,yBACPhgB,KAAK0O,OACL0G,EACAM,EACA,EACA,EACAC,EACA,EACA,EAEF,CAGQ,eAAAlB,GACP,MACMtS,EAAO,OACPqV,EAAMjQ,EAAOpF,QACbuT,EAAU8B,EACV7B,EAAa6B,EAAiBrV,EACpC,KAAOnC,KAAKmS,aACXnS,KAAK4V,UAAUF,EAASC,EANN,OAQnBnO,EAAKgQ,EACN,CAGQ,gBAAA1D,GACP,IAAK9T,KAAKmS,YACT,OAAO1J,QAAQC,UAEhB,MACMvG,EAAO,OACPqV,EAAMjQ,EAAOpF,QACbuT,EAAU8B,EACV7B,EAAa6B,EAAiBrV,EAC9B8d,EACiB,oBAAfC,WACJ,IACO,IAAIzX,SAAeC,GACzBwX,WAAWxX,EAAS,KAGrB,IACOD,QAAQC,UAEnB,SAASyX,IACR,OAAOF,IAAYrL,KAAKwL,EACzB,CACA,MAAMC,EAAOrgB,KACb,SAASogB,IACR,OAAKC,EAAKlO,aAIVkO,EAAKzK,UAAUF,EAASC,EAxBN,OAyBXwK,MAJN3Y,EAAKgQ,GACE/O,QAAQC,UAIjB,CACA,OAAOyX,GACR,CAEO,oBAAAG,GACN,OAAOtgB,KAAKke,aACTle,KAAKke,aAAaC,QAClB1V,QAAQC,SACZ,CAKO,sBAAO6X,GACb,IACA,MAAMC,EAAM,IAAIzY,EAChB,OAAOyY,EAAIlY,cAAcsM,MAAK,IAAM4L,GACrC,CAWO,8BAAOC,CACbD,EACAhW,EACAiV,EACAzD,GAEA,KAAMwE,aAAezY,GACpB,MAAM,IAAIyB,UAAU,8BAErB,MAAMnH,EAAM0E,GACX,CAAC2Z,EAAc/b,EAAiBwD,EAAcjG,KAC7C,MAAMye,EAAI,IAAI3d,EAAmB2B,EAAI,GAC/BE,EACL,EAAQ1B,sBAAsBwB,GAC/B8a,EAASiB,EAAM7b,EAAM8b,EAAGH,EAAKte,EAAK,GAEnC,SAEKkI,EAAI9C,EACTkZ,EAAIvd,SACJuH,EACAnI,EACA2Z,GAKD,OAHW,IAAP5R,IACHoW,EAAInY,eAAe+B,GAAK/H,GAElB+H,CACR,CAQO,2BAAOsB,CACb8U,EACA7W,EACA/E,GAEA,KAAM4b,aAAezY,GACpB,MAAM,IAAIyB,UAAU,8BAErBgX,EAAI9U,qBAAqB/B,EAAU/E,EACpC,CAOO,mBAAO+G,CACb6U,EACA7W,EACAiC,GAEA,KAAM4U,aAAezY,GACpB,MAAM,IAAIyB,UAAU,8BAErBgX,EAAI7U,aAAahC,EAAUiC,EAC5B,CAQO,8BAAOO,CAAwBqU,EAAiBpU,GACtD,KAAMoU,aAAezY,GACpB,MAAM,IAAIyB,UAAU,8BAErB,OAAOgX,EAAIrU,wBAAwBC,EACpC,EEjuCD,SAASwU,EAA4BC,GACpC,OA1BD,SAAkCA,GACjC,MAAMC,EAAc,CAAC,EACfC,EAAiB,GACvB,IAAIC,EAAWH,EACf,KAAOG,GAAOA,IAAQrY,OAAOsY,WAC5BF,EAAQG,QAAQF,GAChBA,EAAMrY,OAAOwY,eAAeH,GAY7B,OAVAD,EAAQlY,SAASuY,IAChBzY,OAAO0Y,oBAAoBD,GAAGvY,SAAS7D,IACtC,IACC,MAAM9C,EAAQ2e,EAAY7b,GACN,mBAAT9C,GAAuC,iBAATA,IACxC4e,EAAO9b,GAAO9C,E,CAEd,MAAOof,GAAM,IACd,IAEI,CACNC,SAAUV,EAAIrW,KACdgX,QAASX,EAAIW,QACbC,OAAQX,EAEV,CAGQY,CAA0Bb,GAAOA,aAAe9S,MAAS8S,EAAM,IAAI9S,MAAM,GAAG8S,KACpF,CAqEO,SAASc,EACfC,EACAC,EACAC,EACAC,GAEA,MAAMC,EAAkC,CACvCJ,KAAMA,GAoBP,OAlBIC,EACHD,EAAK3R,iBAAiB,WAAY0Q,IACjC,MAAMze,EAAOye,EAAEze,KACVA,GAGL2f,EAAmBjN,MAAK,IAAMqN,EAAmBD,EAASJ,KAAM1f,EAAM4f,EAAoBC,IAAa,IAGxGH,EAAK3R,iBAAiB,WAAY0Q,IACjC,MAAMze,EAAOye,EAAEze,KACVA,GAGL+f,EAAmBD,EAASJ,KAAM1f,EAAM4f,EAAoBC,EAAY,IAG1EH,EAAKM,QACEF,CACR,CAEA,SAASC,EACRL,EACA1f,EACA4f,EACAK,GAEA,GAAIA,GAAQA,EAAKjgB,GAChB,OAED,MAAMkgB,EAASN,IACf,GAAKM,EAAOlgB,EAAKmgB,QAGhB,IACCC,EAAeV,EAAM1f,EAAKoI,GAAIpI,EAAKmgB,OAAQD,EAAOlgB,EAAKmgB,QAAQE,MAAMH,EAAQlgB,EAAKsgB,M,CACjF,MAAO7B,GACR8B,EAAoBb,EAAM1f,EAAKoI,GAAIpI,EAAKmgB,OAAQ1B,E,MALjD8B,EAAoBb,EAAM1f,EAAKoI,GAAIpI,EAAKmgB,OAAQ,IAAItU,MAAM,mBAQ5D,CAGO,SAAS2U,EAAWV,EAAiC1X,EAAY+X,EAAgBliB,GACvFmiB,EAAeN,EAASJ,KAAMtX,EAAI+X,EAAQliB,EAC3C,CAEA,SAASmiB,EAAeV,EAAmBtX,EAAY+X,EAAgBliB,GAClEA,aAAiBsI,QACpBtI,EAAMyU,MAAM+N,IACPrY,GAAM,GACTsX,EAAKgB,YAAY,CAChBtY,KACA+X,SACAjZ,IAAKuZ,G,IAGJE,IACHjB,EAAKgB,YAAY,CAChBtY,KACA+X,SACAQ,MAAOjC,EAA4BiC,IACT,IAG5BjB,EAAKgB,YAAY,CAChBtY,KACA+X,SACAjZ,IAAKjJ,GAGR,CAGO,SAAS2iB,EAAgBd,EAAiC1X,EAAY+X,EAAgBQ,GAC5FJ,EAAoBT,EAASJ,KAAMtX,EAAI+X,EAAQQ,EAChD,CAEA,SAASJ,EAAoBb,EAAmBtX,EAAY+X,EAAgBQ,GAC3EjB,EAAKgB,YAAY,CAChBtY,KACA+X,SACAQ,MAAOjC,EAA4BiC,IAErC,CDrPA,IAAIE,EAAyC,KAC7C,MAAMC,EAAqD,GAErDC,EAAkB,EAElBC,EAAW,CAChBC,MAAO,EACPpV,MAAO,EACPqV,QAAS,EACTC,KAAM,EACNC,MAAO,GAuBD,SAASC,EAAevK,EAAyBkK,EAASC,OAChE,GAAIJ,IAA0B/J,EAA9B,CAIA,GAtBD,WACC,GAAuC,oBAA5BzU,wBACV,EAAUA,wBAAwBC,eAC5B,IAAsB,oBAAXC,OAGjB,MAAM,IAAIsJ,MACT,oEAHD,EAAUtJ,M,CAMZ,CAWC,GACa,MAATuU,EAC2B,MAA1BrR,IACH,EAAQ6b,wBAAwB,EAAG7b,EAAwB,GAC3D,EAAQ6b,wBAAwB,EAAG7b,EAAwB,GAC3D,EAAQ6b,wBAAwB,EAAG7b,EAAwB,GAC3D,EAAQ6b,wBAAwB,EAAG7b,EAAwB,IAE5D,EAAQ6b,wBAAwB,EAAG,EAAG,OAChC,CACN,IAAInhB,EACJ,IAAK,IAAIohB,EAAIzK,EAAOyK,EAAIR,IAAmBQ,EAAG,CAC7C,MAAMhG,EAAI,EAAQ+F,wBAAwBC,EAAG,EAAG,GAC5CA,IAAMP,EAASI,QAClBjhB,EAAMob,E,CAGG,MAAPpb,GAAyC,MAA1BsF,IAClBA,EAAyBtF,E,CAG3B0gB,EAAwB/J,EACxB,IAAK,MAAMxJ,KAAMwT,EAChBxT,EAAGwJ,E,CAEL,C,0SE3CA,MAAM0K,ECfEpa,EAAY6F,yBCCpB5K,wBAAwBof,QAAU,CACjCC,iBRiJM,SAA0B1hB,EAA2B0C,GAC3D,KAAK1C,GAAUA,aAAgBc,GAC9B,OAAO,EAER,MAAM2B,EAAKzC,EAAKe,SAChB,OD/I+C,IC+I3C0B,GAGGD,EAAqBC,EAAIC,EACjC,EQzJC0E,YAAaA,EACbia,eAAgBA,EAChBM,eJ0DM,WACNN,EAAe,KAChB,GIzDAhf,wBAAwBuf,MAAQvf,wBAAwBof,QFUzC,WAId,MAAMI,UAAkBC,sBAKvB,WAAApkB,CAAYqkB,GACXC,MAAMD,GAEN,MAAME,EAAiDF,EAAQE,iBACzD7V,EACL6V,GAAoBA,EAAiB7V,SAClC6V,GAAoBA,EAAiBC,sBACxCb,EAAeY,EAAiBC,sBAGjC,MAAMvC,EAAqB7hB,KAAKqkB,OAAO/V,GACvCtO,KAAKskB,WAAa3C,EAAqB3hB,KAAK4hB,KAAMC,GAAoB,IAAM7hB,KAAKmJ,QAASjH,IACzF,OAAQA,EAAKmgB,QACZ,IAAK,OAEJ,OADAriB,KAAKmJ,MAAOiH,KAAKC,WAAY/B,IACtB,EACR,IAAK,kBAIJ,OAHAtO,KAAKukB,kBAAkBriB,EAAKsgB,KAAK,IAAI5N,MAAK,KACzC8N,EAAW1iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,YAAQ,EAAS,KAEtD,EACR,IAAK,6BAEOriB,KAAKwkB,uBAAuBtiB,EAAKsgB,KAAK,GAAItgB,EAAKsgB,KAAK,IAE7DE,EAAW1iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,YAAQ,GAEnDS,EAAgB9iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,OAAQ,IAAItU,MAAM,mBAGpE,OAAO,EACR,IAAK,eACJ,IACC/N,KAAKykB,eAAeviB,EAAKsgB,KAAK,GAAItgB,EAAKsgB,KAAK,IAC5CE,EAAW1iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,YAAQ,E,CAClD,MAAO1B,GACRmC,EAAgB9iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,OAAQ1B,E,CAEzD,OAAO,EACR,IAAK,iBACJ,IACC,MAAMnW,EAAOxK,KAAK0kB,iBAAiBxiB,EAAKsgB,KAAK,GAAItgB,EAAKsgB,KAAK,IAC9C,OAAThY,EACHkY,EAAW1iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,OAAQ7X,GAEnDsY,EAAgB9iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,OAAQ,IAAItU,MAAM,mB,CAElE,MAAO4S,GACRmC,EAAgB9iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,OAAQ1B,E,CAEzD,OAAO,EACR,IAAK,aAEJ,OADA3gB,KAAK2kB,aAAaziB,IACX,EACR,IAAK,iBAEJ,OADAqhB,EAAerhB,EAAKsgB,KAAK,KAClB,EAET,OAAO,CAAK,GAEd,CAEc,MAAA6B,CAAO/V,G,+CACdoV,EACN1jB,KAAKmJ,MAAQ,IAAIG,EACjBtJ,KAAKmJ,MAAMiH,KAAKC,WAAY/B,EAC7B,G,CAEQ,iBAAAiW,CAAkB3C,GACzB,OAAOtY,EAAYiX,kBAAkB3L,MAAM4L,IAC1C,MAAMoE,EAAYjD,EAAqBC,EAAM,MAAM,IAAMpB,IAAMte,IAE9D,GAAoB,WAAhBA,EAAKmgB,OAER,OADAK,EAAWkC,EAAW1iB,EAAKoI,GAAIpI,EAAKmgB,OAAS7B,EAAkBvd,WACxD,EACD,GAAoB,kCAAhBf,EAAKmgB,OAA4C,CAC3D,MAAMjY,EAAIpK,KAAK6kB,0BAA0BrE,EAAkBte,EAAKsgB,KAAK,GAAItgB,EAAKsgB,KAAK,GAAItgB,EAAKsgB,KAAK,IAMjG,OALU,OAANpY,EACHsY,EAAWkC,EAAW1iB,EAAKoI,GAAIpI,EAAKmgB,OAAQjY,GAE5C0Y,EAAgB8B,EAAW1iB,EAAKoI,GAAIpI,EAAKmgB,OAAQ,IAAItU,MAAM,oBAErD,C,CAER,OAAO,CAAK,GACX,GAEJ,CAEQ,gBAAA2W,CAAiB9C,EAAmBhc,GAC3C,MAAM6G,EAAQzM,KAAKmJ,MAAO0L,eAAejP,GACzC,GAAc,OAAV6G,EACH,OAAO,KAER,MAAMmY,EAAYjD,EAAqBC,EAAM,MAAM,IAAMnV,IAAQvK,GAC5C,sBAAhBA,EAAKmgB,SACRK,EAAWkC,EAAW1iB,EAAKoI,GAAIpI,EAAKmgB,OAAQ,IAAI5V,EAAMY,uBAC/C,KAIT,OAAOZ,EAAME,SACd,CAEQ,YAAAgY,CAAaziB,GACpB,MAAM4iB,EAAM9kB,KAAKmJ,MACjB2b,EAAI/G,aAAanJ,MAAK,KACrB8N,EAAW1iB,KAAKskB,YAAa,EAAG,eAAwB,CACvDS,QAASD,EAAI3S,YACb6S,cAAeF,EAAI1O,oBAEpBsM,EAAW1iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,YAAQ,EAAS,IACzD1B,IACHmC,EAAgB9iB,KAAKskB,WAAapiB,EAAKoI,GAAIpI,EAAKmgB,OAAQ1B,EAAE,GAE5D,CAEQ,sBAAA6D,CAAuBha,EAAiCwR,GAC/D,IAAKxR,EAEJ,OADAxK,KAAKmJ,MAAOqW,qBAAqB,OAC1B,EAER,MAAMhQ,EAAWjL,wBAAwBiG,GACzC,SAAIgF,GAAoB,mBAAPA,KAChBxP,KAAKmJ,MAAOqW,qBAAqBhQ,EAAIwM,IAC9B,EAGT,CAEQ,cAAAyI,CAAeja,EAAcwR,GACpC,MAAMxM,EAAWjL,wBAAwBiG,GACzC,IAAIgF,GAAoB,mBAAPA,EAIjB,MAAM,IAAIzB,MAAM,kBAHfyB,EAAGyV,KAAK,KAAMjlB,KAAKmJ,MAAO6S,EAI5B,CAEQ,yBAAA6I,CAA0BrE,EAAgB0E,EAAoBC,EAAsBnJ,GAC3F,MAAMxM,EAAWjL,wBAAwB4gB,GACzC,OAAI3V,GAAoB,mBAAPA,EACTlG,EAAYmX,wBAAwBD,EAAK0E,EAAY1V,EAAIwM,GAE1D,IACR,CAEO,OAAAoJ,CAAQC,EAA2BC,GACzC,IAAKtlB,KAAKmJ,MACT,OAAO,EAER,MAAM2b,EAAM9kB,KAAKmJ,MAMjB,OALA2b,EAAI5U,OAAOoV,EAAQ,IACnB5C,EAAW1iB,KAAKskB,YAAa,EAAG,eAAwB,CACvDS,QAASD,EAAI3S,YACb6S,cAAeF,EAAI1O,qBAEb,CACR,EAGDmP,kBAAkB,WAAyBxB,EAC5C,CEnLAyB,E", "sources": ["webpack://js-synthesizer/./src/main/MIDIEvent.ts", "webpack://js-synthesizer/./src/main/SequencerEventData.ts", "webpack://js-synthesizer/./src/main/PointerType.ts", "webpack://js-synthesizer/./src/main/ISequencerEventData.ts", "webpack://js-synthesizer/./src/main/Sequencer.ts", "webpack://js-synthesizer/./src/main/Soundfont.ts", "webpack://js-synthesizer/./src/main/Synthesizer.ts", "webpack://js-synthesizer/./src/main/logging.ts", "webpack://js-synthesizer/./src/main/MethodMessaging.ts", "webpack://js-synthesizer/./src/main/registerAudioWorkletProcessor.ts", "webpack://js-synthesizer/./src/main/waitForReady.ts", "webpack://js-synthesizer/./src/main/workletEntry.ts"], "sourcesContent": ["\nimport IMIDIEvent from './IMIDIEvent';\nimport PointerType, { UniquePointerType } from './PointerType';\n\n/** @internal */\nexport type MIDIEventType = UniquePointerType<'midi_event'>;\n\n/** @internal */\nexport default class MIDIEvent implements IMIDIEvent {\n\n\t/** @internal */\n\tconstructor(private _ptr: MIDIEventType, private _module: any) {\n\t}\n\n\tpublic getType(): number {\n\t\treturn this._module._fluid_midi_event_get_type(this._ptr);\n\t}\n\tpublic setType(value: number): void {\n\t\tthis._module._fluid_midi_event_set_type(this._ptr, value);\n\t}\n\tpublic getChannel(): number {\n\t\treturn this._module._fluid_midi_event_get_channel(this._ptr);\n\t}\n\tpublic setChannel(value: number): void {\n\t\tthis._module._fluid_midi_event_set_channel(this._ptr, value);\n\t}\n\tpublic getKey(): number {\n\t\treturn this._module._fluid_midi_event_get_key(this._ptr);\n\t}\n\tpublic setKey(value: number): void {\n\t\tthis._module._fluid_midi_event_set_key(this._ptr, value);\n\t}\n\tpublic getVelocity(): number {\n\t\treturn this._module._fluid_midi_event_get_velocity(this._ptr);\n\t}\n\tpublic setVelocity(value: number): void {\n\t\tthis._module._fluid_midi_event_set_velocity(this._ptr, value);\n\t}\n\tpublic getControl(): number {\n\t\treturn this._module._fluid_midi_event_get_control(this._ptr);\n\t}\n\tpublic setControl(value: number): void {\n\t\tthis._module._fluid_midi_event_set_control(this._ptr, value);\n\t}\n\tpublic getValue(): number {\n\t\treturn this._module._fluid_midi_event_get_value(this._ptr);\n\t}\n\tpublic setValue(value: number): void {\n\t\tthis._module._fluid_midi_event_set_value(this._ptr, value);\n\t}\n\tpublic getProgram(): number {\n\t\treturn this._module._fluid_midi_event_get_program(this._ptr);\n\t}\n\tpublic setProgram(value: number): void {\n\t\tthis._module._fluid_midi_event_set_program(this._ptr, value);\n\t}\n\tpublic getPitch(): number {\n\t\treturn this._module._fluid_midi_event_get_pitch(this._ptr);\n\t}\n\tpublic setPitch(value: number): void {\n\t\tthis._module._fluid_midi_event_set_pitch(this._ptr, value);\n\t}\n\n\tpublic setSysEx(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_sysex(this._ptr, ptr, size, 1);\n\t}\n\tpublic setText(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_text(this._ptr, ptr, size, 1);\n\t}\n\tpublic setLyrics(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_lyrics(this._ptr, ptr, size, 1);\n\t}\n}\n", "\nimport { EventType } from './SequencerEvent';\nimport ISequencerEventData from './ISequencerEventData';\nimport PointerType, { INVALID_POINTER } from './PointerType';\n\n/** @internal */\nexport default class SequencerEventData implements ISequencerEventData {\n\t/** @internal */\n\tconstructor(private _ptr: PointerType, private _module: any) {\n\t}\n\n\t/** @internal */\n\tpublic getRaw() {\n\t\treturn this._ptr;\n\t}\n\n\t/** @internal */\n\tpublic dispose() {\n\t\tthis._ptr = INVALID_POINTER;\n\t}\n\n\tpublic getType(): EventType {\n\t\tif (this._ptr === INVALID_POINTER) return -1 as any as EventType;\n\t\treturn this._module._fluid_event_get_type(this._ptr);\n\t}\n\tpublic getSource(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_source(this._ptr);\n\t}\n\tpublic getDest(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_dest(this._ptr);\n\t}\n\tpublic getChannel(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_channel(this._ptr);\n\t}\n\tpublic getKey(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_key(this._ptr);\n\t}\n\tpublic getVelocity(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_velocity(this._ptr);\n\t}\n\tpublic getControl(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_control(this._ptr);\n\t}\n\tpublic getValue(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_value(this._ptr);\n\t}\n\tpublic getProgram(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_program(this._ptr);\n\t}\n\tpublic getData(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_data(this._ptr);\n\t}\n\tpublic getDuration(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_duration(this._ptr);\n\t}\n\tpublic getBank(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_bank(this._ptr);\n\t}\n\tpublic getPitch(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_pitch(this._ptr);\n\t}\n\tpublic getSFontId(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_sfont_id(this._ptr);\n\t}\n}\n", "\ntype NullPointerType = number & { _null_pointer_marker: never; };\n\n/** @internal */\ntype PointerType = NullPointerType | (number & { _pointer_marker: never; });\n\nexport default PointerType;\n\ntype UniquePointerType<TMarker extends string> = NullPointerType | (number & {\n\t_pointer_marker: never;\n} & {\n\t[P in TMarker]: never;\n});\nexport { UniquePointerType };\n\nexport const INVALID_POINTER: NullPointerType = 0 as any as NullPointerType;\n", "\nimport SequencerEvent, { EventType } from './SequencerEvent';\n\n/** @internal */\nimport PointerType, { INVALID_POINTER } from './PointerType';\n/** @internal */\nimport SequencerEventData from './SequencerEventData';\n\nconst _module: any = typeof AudioWorkletGlobalScope !== 'undefined' ?\n\tAudioWorkletGlobalScope.wasmModule : Module;\n\n/** Event data for sequencer callback. Only available in the callback function due to the instance lifetime. */\nexport default interface ISequencerEventData {\n\t/** Returns the event type */\n\tgetType(): EventType;\n\t/** Returns the source client id of event */\n\tgetSource(): number;\n\t/** Returns the destination client id of event */\n\tgetDest(): number;\n\tgetChannel(): number;\n\tgetKey(): number;\n\tgetVelocity(): number;\n\tgetControl(): number;\n\tgetValue(): number;\n\tgetProgram(): number;\n\tgetData(): number;\n\tgetDuration(): number;\n\tgetBank(): number;\n\tgetPitch(): number;\n\tgetSFontId(): number;\n}\n\n/** @internal */\nexport function rewriteEventDataImpl(ev: PointerType, event: SequencerEvent): boolean {\n\tswitch (event.type) {\n\t\tcase EventType.Note:\n\t\tcase 'note':\n\t\t\t_module._fluid_event_note(ev, event.channel, event.key, event.vel, event.duration);\n\t\t\tbreak;\n\t\tcase EventType.NoteOn:\n\t\tcase 'noteon':\n\t\tcase 'note-on':\n\t\t\t_module._fluid_event_noteon(ev, event.channel, event.key, event.vel);\n\t\t\tbreak;\n\t\tcase EventType.NoteOff:\n\t\tcase 'noteoff':\n\t\tcase 'note-off':\n\t\t\t_module._fluid_event_noteoff(ev, event.channel, event.key);\n\t\t\tbreak;\n\t\tcase EventType.AllSoundsOff:\n\t\tcase 'allsoundsoff':\n\t\tcase 'all-sounds-off':\n\t\t\t_module._fluid_event_all_sounds_off(ev, event.channel);\n\t\t\tbreak;\n\t\tcase EventType.AllNotesOff:\n\t\tcase 'allnotesoff':\n\t\tcase 'all-notes-off':\n\t\t\t_module._fluid_event_all_notes_off(ev, event.channel);\n\t\t\tbreak;\n\t\tcase EventType.BankSelect:\n\t\tcase 'bankselect':\n\t\tcase 'bank-select':\n\t\t\t_module._fluid_event_bank_select(ev, event.channel, event.bank);\n\t\t\tbreak;\n\t\tcase EventType.ProgramChange:\n\t\tcase 'programchange':\n\t\tcase 'program-change':\n\t\t\t_module._fluid_event_program_change(ev, event.channel, event.preset);\n\t\t\tbreak;\n\t\tcase EventType.ProgramSelect:\n\t\tcase 'programselect':\n\t\tcase 'program-select':\n\t\t\t_module._fluid_event_program_select(ev, event.channel, event.sfontId, event.bank, event.preset);\n\t\t\tbreak;\n\t\tcase EventType.ControlChange:\n\t\tcase 'controlchange':\n\t\tcase 'control-change':\n\t\t\t_module._fluid_event_control_change(ev, event.channel, event.control, event.value);\n\t\t\tbreak;\n\t\tcase EventType.PitchBend:\n\t\tcase 'pitchbend':\n\t\tcase 'pitch-bend':\n\t\t\t_module._fluid_event_pitch_bend(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.PitchWheelSensitivity:\n\t\tcase 'pitchwheelsens':\n\t\tcase 'pitchwheelsensitivity':\n\t\tcase 'pitch-wheel-sens':\n\t\tcase 'pitch-wheel-sensitivity':\n\t\t\t_module._fluid_event_pitch_wheelsens(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Modulation:\n\t\tcase 'modulation':\n\t\t\t_module._fluid_event_modulation(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Sustain:\n\t\tcase 'sustain':\n\t\t\t_module._fluid_event_sustain(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Pan:\n\t\tcase 'pan':\n\t\t\t_module._fluid_event_pan(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Volume:\n\t\tcase 'volume':\n\t\t\t_module._fluid_event_volume(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ReverbSend:\n\t\tcase 'reverb':\n\t\tcase 'reverbsend':\n\t\tcase 'reverb-send':\n\t\t\t_module._fluid_event_reverb_send(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ChorusSend:\n\t\tcase 'chorus':\n\t\tcase 'chorussend':\n\t\tcase 'chorus-send':\n\t\t\t_module._fluid_event_chorus_send(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.KeyPressure:\n\t\tcase 'keypressure':\n\t\tcase 'key-pressure':\n\t\tcase 'aftertouch':\n\t\t\t_module._fluid_event_key_pressure(ev, event.channel, event.key, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ChannelPressure:\n\t\tcase 'channelpressure':\n\t\tcase 'channel-pressure':\n\t\tcase 'channel-aftertouch':\n\t\t\t_module._fluid_event_channel_pressure(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.SystemReset:\n\t\tcase 'systemreset':\n\t\tcase 'system-reset':\n\t\t\t_module._fluid_event_system_reset(ev);\n\t\t\tbreak;\n\t\tcase EventType.Timer:\n\t\tcase 'timer':\n\t\t\t_module._fluid_event_timer(ev, event.data);\n\t\t\tbreak;\n\t\tdefault:\n\t\t\t// 'typeof event' must be 'never' here\n\t\t\treturn false;\n\t}\n\treturn true;\n}\n\n/**\n * Rewrites event data with specified SequencerEvent object.\n * @param data destination instance\n * @param event source data\n * @return true if succeeded\n */\nexport function rewriteEventData(data: ISequencerEventData, event: SequencerEvent): boolean {\n\tif (!data || !(data instanceof SequencerEventData)) {\n\t\treturn false;\n\t}\n\tconst ev = data.getRaw();\n\tif (ev === INVALID_POINTER) {\n\t\treturn false;\n\t}\n\treturn rewriteEventDataImpl(ev, event);\n}\n", "\nimport ISequencer, { ClientInfo } from './ISequencer';\nimport ISequencerEventData, { rewriteEventDataImpl } from './ISequencerEventData';\nimport ISynthesizer from './ISynthesizer';\nimport PointerType, { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport SequencerEvent from './SequencerEvent';\nimport SequencerEventData from './SequencerEventData';\n\nimport Synthesizer from './Synthesizer';\n\ntype SequencerPointer = UniquePointerType<'sequencer_ptr'>;\ntype SequencerId = number;\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n\tfunction removeFunction(funcPtr: number): void;\n}\n\nlet _module: any;\nlet _removeFunction: (funcPtr: number) => void;\n\nlet fluid_sequencer_get_client_name: (seq: number, id: number) => string;\n\nfunction bindFunctions() {\n\tif (_module) {\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t\t_removeFunction = AudioWorkletGlobalScope.wasmRemoveFunction;\n\t} else {\n\t\t_module = Module;\n\t\t_removeFunction = removeFunction;\n\t}\n\n\tfluid_sequencer_get_client_name =\n\t\t_module.cwrap('fluid_sequencer_get_client_name', 'string', ['number', 'number']);\n}\n\nfunction makeEvent(event: SequencerEvent): PointerType | null {\n\tconst ev = _module._new_fluid_event();\n\tif (!rewriteEventDataImpl(ev, event)) {\n\t\t_module._delete_fluid_event(ev);\n\t\treturn null;\n\t}\n\treturn ev;\n}\n\n/** @internal */\nexport default class Sequencer implements ISequencer {\n\n\tprivate _seq: SequencerPointer;\n\tprivate _seqId: SequencerId;\n\n\t/** @internal */\n\tpublic _clientFuncMap: { [id: number]: number };\n\n\tconstructor() {\n\t\tbindFunctions();\n\n\t\tthis._seq = INVALID_POINTER;\n\t\tthis._seqId = -1;\n\t\tthis._clientFuncMap = {};\n\t}\n\n\t/** @internal */\n\tpublic _initialize(): Promise<void> {\n\t\tthis.close();\n\t\tthis._seq = _module._new_fluid_sequencer2(0);\n\t\tthis._seqId = -1;\n\t\treturn Promise.resolve();\n\t}\n\n\t/** @internal */\n\tpublic getRaw() {\n\t\treturn this._seq;\n\t}\n\n\tpublic close() {\n\t\tif (this._seq !== INVALID_POINTER) {\n\t\t\tObject.keys(this._clientFuncMap).forEach((clientIdStr) => {\n\t\t\t\tthis.unregisterClient(Number(clientIdStr));\n\t\t\t});\n\t\t\tthis.unregisterClient(-1);\n\t\t\t_module._delete_fluid_sequencer(this._seq);\n\t\t\tthis._seq = INVALID_POINTER;\n\t\t}\n\t}\n\n\tpublic registerSynthesizer(synth: ISynthesizer | number): Promise<number> {\n\t\tif (this._seqId !== -1) {\n\t\t\t_module._fluid_sequencer_unregister_client(this._seq, this._seqId);\n\t\t\tthis._seqId = -1;\n\t\t}\n\t\tlet val: number;\n\t\tif (typeof synth === 'number') {\n\t\t\tval = synth;\n\t\t} else if (synth instanceof Synthesizer) {\n\t\t\tval = synth.getRawSynthesizer();\n\t\t} else {\n\t\t\treturn Promise.reject(new TypeError('\\'synth\\' is not a compatible type instance'));\n\t\t}\n\n\t\tthis._seqId = _module._fluid_sequencer_register_fluidsynth(this._seq, val);\n\t\treturn Promise.resolve(this._seqId);\n\t}\n\n\tpublic unregisterClient(clientId: number): void {\n\t\tif (clientId === -1) {\n\t\t\tclientId = this._seqId;\n\t\t\tif (clientId === -1) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\t// send 'unregistering' event\n\t\tconst ev = _module._new_fluid_event();\n\t\t_module._fluid_event_set_source(ev, -1);\n\t\t_module._fluid_event_set_dest(ev, clientId);\n\t\t_module._fluid_event_unregistering(ev);\n\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t_module._delete_fluid_event(ev);\n\n\t\t_module._fluid_sequencer_unregister_client(this._seq, clientId);\n\t\tif (this._seqId === clientId) {\n\t\t\tthis._seqId = -1;\n\t\t} else {\n\t\t\tconst map = this._clientFuncMap;\n\t\t\tif (map[clientId]) {\n\t\t\t\t_removeFunction(map[clientId]);\n\t\t\t\tdelete map[clientId];\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic getAllRegisteredClients(): Promise<ClientInfo[]> {\n\t\tconst c = _module._fluid_sequencer_count_clients(this._seq);\n\t\tconst r: ClientInfo[] = [];\n\t\tfor (let i = 0; i < c; ++i) {\n\t\t\tconst id = _module._fluid_sequencer_get_client_id(this._seq, i);\n\t\t\tconst name = fluid_sequencer_get_client_name(this._seq, id);\n\t\t\tr.push({ clientId: id, name: name });\n\t\t}\n\t\treturn Promise.resolve(r);\n\t}\n\n\tpublic getClientCount(): Promise<number> {\n\t\treturn Promise.resolve<number>(_module._fluid_sequencer_count_clients(this._seq));\n\t}\n\n\tpublic getClientInfo(index: number): Promise<ClientInfo> {\n\t\tconst id = _module._fluid_sequencer_get_client_id(this._seq, index);\n\t\tconst name = fluid_sequencer_get_client_name(this._seq, id);\n\t\treturn Promise.resolve<ClientInfo>({ clientId: id, name: name });\n\t}\n\n\tpublic setTimeScale(scale: number): void {\n\t\t_module._fluid_sequencer_set_time_scale(this._seq, scale);\n\t}\n\n\tpublic getTimeScale(): Promise<number> {\n\t\treturn Promise.resolve(_module._fluid_sequencer_get_time_scale(this._seq));\n\t}\n\n\tpublic getTick(): Promise<number> {\n\t\treturn Promise.resolve(_module._fluid_sequencer_get_tick(this._seq));\n\t}\n\n\tpublic sendEventAt(event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t// send to all clients\n\t\t\tconst count = _module._fluid_sequencer_count_clients(this._seq);\n\t\t\tfor (let i = 0; i < count; ++i) {\n\t\t\t\tconst id: number = _module._fluid_sequencer_get_client_id(this._seq, i);\n\t\t\t\t_module._fluid_event_set_dest(ev, id);\n\t\t\t\t_module._fluid_sequencer_send_at(this._seq, ev, tick, isAbsolute ? 1 : 0);\n\t\t\t}\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\tpublic sendEventToClientAt(clientId: number, event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_at(this._seq, ev, tick, isAbsolute ? 1 : 0);\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic sendEventToClientNow(clientId: number, event: SequencerEvent): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic sendEventNow(clientId: number, eventData: ISequencerEventData): void {\n\t\tif (!(eventData instanceof SequencerEventData)) {\n\t\t\treturn;\n\t\t}\n\t\tconst ev = eventData.getRaw();\n\t\tif (ev !== INVALID_POINTER) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t}\n\t}\n\n\tpublic removeAllEvents(): void {\n\t\t_module._fluid_sequencer_remove_events(this._seq, -1, -1, -1);\n\t}\n\n\tpublic removeAllEventsFromClient(clientId: number): void {\n\t\t_module._fluid_sequencer_remove_events(this._seq, -1, clientId === -1 ? this._seqId : clientId, -1);\n\t}\n\n\tpublic processSequencer(msecToProcess: number) {\n\t\tif (this._seq !== INVALID_POINTER) {\n\t\t\t_module._fluid_sequencer_process(this._seq, msecToProcess);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic setIntervalForSequencer(msec: number) {\n\t\treturn setInterval(() => this.processSequencer(msec), msec);\n\t}\n}\n", "import { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport Preset from './Preset';\nimport Synthesizer from './Synthesizer';\n\ntype SFontPointer = UniquePointerType<'sfont_ptr'>;\ntype PresetPointer = UniquePointerType<'preset_ptr'>;\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n}\n\nlet _module: any;\n\nlet fluid_sfont_get_name: (sfont: SFontPointer) => string;\nlet fluid_preset_get_name: (preset: PresetPointer) => string;\n\nfunction bindFunctions() {\n\tif (_module) {\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t} else {\n\t\t_module = Module;\n\t}\n\n\tfluid_sfont_get_name =\n\t\t_module.cwrap('fluid_sfont_get_name', 'string', ['number']);\n\tfluid_preset_get_name =\n\t\t_module.cwrap('fluid_preset_get_name', 'string', ['number']);\n}\n\nexport default class Soundfont {\n\tprivate readonly _ptr: SFontPointer;\n\n\t// @internal\n\tpublic constructor(sfontPtr: SFontPointer) {\n\t\tthis._ptr = sfontPtr;\n\t}\n\n\tpublic static getSoundfontById(synth: Synthesizer, id: number): Soundfont | null {\n\t\tbindFunctions();\n\n\t\tconst sfont = _module._fluid_synth_get_sfont_by_id(synth.getRawSynthesizer(), id);\n\t\tif (sfont === INVALID_POINTER) {\n\t\t\treturn null;\n\t\t}\n\t\treturn new Soundfont(sfont);\n\t}\n\n\tpublic getName(): string {\n\t\treturn fluid_sfont_get_name(this._ptr);\n\t}\n\n\tpublic getPreset(bank: number, presetNum: number): Preset | null {\n\t\tconst presetPtr: PresetPointer = _module._fluid_sfont_get_preset(this._ptr, bank, presetNum);\n\t\tif (presetPtr === INVALID_POINTER) {\n\t\t\treturn null;\n\t\t}\n\t\tconst name = fluid_preset_get_name(presetPtr);\n\t\tconst bankNum = _module._fluid_preset_get_banknum(presetPtr);\n\t\tconst num = _module._fluid_preset_get_num(presetPtr);\n\t\treturn {\n\t\t\tsoundfont: this,\n\t\t\tname,\n\t\t\tbankNum,\n\t\t\tnum\n\t\t};\n\t}\n\n\tpublic getPresetIterable(): Iterable<Preset> {\n\t\tconst reset = () => {\n\t\t\t_module._fluid_sfont_iteration_start(this._ptr);\n\t\t};\n\t\tconst next = (): IteratorResult<Preset, void> => {\n\t\t\tconst presetPtr = _module._fluid_sfont_iteration_next(this._ptr);\n\t\t\tif (presetPtr === 0) {\n\t\t\t\treturn {\n\t\t\t\t\tdone: true,\n\t\t\t\t\tvalue: undefined\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tconst name = fluid_preset_get_name(presetPtr);\n\t\t\t\tconst bankNum = _module._fluid_preset_get_banknum(presetPtr);\n\t\t\t\tconst num = _module._fluid_preset_get_num(presetPtr);\n\t\t\t\treturn {\n\t\t\t\t\tdone: false,\n\t\t\t\t\tvalue: {\n\t\t\t\t\t\tsoundfont: this,\n\t\t\t\t\t\tname,\n\t\t\t\t\t\tbankNum,\n\t\t\t\t\t\tnum\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t};\n\t\tconst iterator = (): Iterator<Preset> => {\n\t\t\treset();\n\t\t\treturn {\n\t\t\t\tnext,\n\t\t\t};\n\t\t};\n\t\treturn {\n\t\t\t[Symbol.iterator]: iterator,\n\t\t};\n\t}\n}\n", "\nimport {\n\tSynthesizerDefaultValues,\n\tInterpolationValues,\n\tChorusModulation,\n\tGeneratorTypes,\n\tLegatoMode,\n\tPortamentoMode,\n\tPlayerSetTempoType,\n} from './Constants';\nimport IMIDIEvent from './IMIDIEvent';\nimport ISequencer from './ISequencer';\nimport ISequencerEventData from './ISequencerEventData';\nimport ISynthesizer from './ISynthesizer';\nimport PointerType, { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport SynthesizerSettings from './SynthesizerSettings';\n\nimport MIDIEvent, { MIDIEventType } from './MIDIEvent';\nimport Sequencer from './Sequencer';\nimport SequencerEvent, { EventType as SequencerEventType } from './SequencerEvent';\nimport SequencerEventData from './SequencerEventData';\nimport Soundfont from './Soundfont';\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n\tfunction addFunction(func: Function, sig: string): number;\n\tfunction removeFunction(funcPtr: number): void;\n\tfunction addOnPostRun(cb: (Module: any) => void): void;\n}\n\ntype SettingsId = UniquePointerType<'settings_id'>;\ntype SynthId = UniquePointerType<'synth_id'>;\ntype PlayerId = UniquePointerType<'player_id'>;\n\nlet _module: any;\nlet _addFunction: (func: Function, sig: string) => number;\nlet _removeFunction: (funcPtr: number) => void;\nlet _fs: any;\n\n// wrapper to use String type\nlet fluid_settings_setint: (settings: SettingsId, name: string, val: number) => number;\nlet fluid_settings_setnum: (settings: SettingsId, name: string, val: number) => number;\nlet fluid_settings_setstr: (settings: SettingsId, name: string, str: string) => number;\nlet fluid_synth_error: undefined | ((synth: SynthId) => string);\nlet fluid_synth_sfload: (synth: SynthId, filename: string, reset_presets: number) => number;\nlet fluid_sequencer_register_client: (seq: PointerType, name: string, callback: number, data: number) => number;\n\nlet malloc: (size: number) => PointerType;\nlet free: (ptr: PointerType) => void;\n\nlet defaultMIDIEventCallback: (data: PointerType, event: MIDIEventType) => number;\n\nfunction bindFunctions() {\n\tif (fluid_synth_error) {\n\t\t// (already bound)\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t\t_addFunction = AudioWorkletGlobalScope.wasmAddFunction;\n\t\t_removeFunction = AudioWorkletGlobalScope.wasmRemoveFunction;\n\t} else if (typeof Module !== 'undefined') {\n\t\t_module = Module;\n\t\t_addFunction = addFunction;\n\t\t_removeFunction = removeFunction;\n\t} else {\n\t\tthrow new Error('wasm module is not available. libfluidsynth-*.js must be loaded.');\n\t}\n\t_fs = _module.FS;\n\n\t// wrapper to use String type\n\tfluid_settings_setint =\n\t\t_module.cwrap('fluid_settings_setint', 'number', ['number', 'string', 'number']);\n\tfluid_settings_setnum =\n\t\t_module.cwrap('fluid_settings_setnum', 'number', ['number', 'string', 'number']);\n\tfluid_settings_setstr =\n\t\t_module.cwrap('fluid_settings_setstr', 'number', ['number', 'string', 'string']);\n\tfluid_synth_error =\n\t\t_module.cwrap('fluid_synth_error', 'string', ['number']);\n\tfluid_synth_sfload =\n\t\t_module.cwrap('fluid_synth_sfload', 'number', ['number', 'string', 'number']);\n\tfluid_sequencer_register_client =\n\t\t_module.cwrap('fluid_sequencer_register_client', 'number', ['number', 'string', 'number', 'number']);\n\n\tmalloc = _module._malloc.bind(_module);\n\tfree = _module._free.bind(_module);\n\n\tdefaultMIDIEventCallback = _module._fluid_synth_handle_midi_event.bind(_module);\n}\n\nlet promiseWaitForInitialized: Promise<void> | undefined;\nfunction waitForInitialized() {\n\tif (promiseWaitForInitialized) {\n\t\treturn promiseWaitForInitialized;\n\t}\n\n\tlet mod: any;\n\tlet addOnPostRunFn: ((cb: (Module: any) => void) => void) | undefined;\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\tmod = AudioWorkletGlobalScope.wasmModule;\n\t\taddOnPostRunFn = AudioWorkletGlobalScope.addOnPostRun;\n\t} else if (typeof Module !== 'undefined') {\n\t\tmod = Module;\n\t\taddOnPostRunFn = typeof addOnPostRun !== 'undefined' ? addOnPostRun : undefined;\n\t} else {\n\t\treturn Promise.reject(new Error('wasm module is not available. libfluidsynth-*.js must be loaded.'));\n\t}\n\tif (mod.calledRun) {\n\t\tpromiseWaitForInitialized = Promise.resolve();\n\t\treturn promiseWaitForInitialized;\n\t}\n\tif (typeof addOnPostRunFn === 'undefined') {\n\t\tpromiseWaitForInitialized = new Promise((resolve) => {\n\t\t\tconst fn: (() => void) | undefined = _module.onRuntimeInitialized;\n\t\t\t_module.onRuntimeInitialized = () => {\n\t\t\t\tresolve();\n\t\t\t\tif (fn) {\n\t\t\t\t\tfn();\n\t\t\t\t}\n\t\t\t};\n\t\t});\n\t} else {\n\t\tpromiseWaitForInitialized = new Promise((resolve) => {\n\t\t\taddOnPostRunFn!(resolve);\n\t\t});\n\t}\n\treturn promiseWaitForInitialized;\n}\n\nfunction setBoolValueForSettings(settings: SettingsId, name: string, value: boolean | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setint(settings, name, value ? 1 : 0);\n\t}\n}\nfunction setIntValueForSettings(settings: SettingsId, name: string, value: number | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setint(settings, name, value);\n\t}\n}\nfunction setNumValueForSettings(settings: SettingsId, name: string, value: number | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setnum(settings, name, value);\n\t}\n}\nfunction setStrValueForSettings(settings: SettingsId, name: string, value: string | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setstr(settings, name, value);\n\t}\n}\n\nfunction getActiveVoiceCount(synth: SynthId): number {\n\tconst actualCount = _module._fluid_synth_get_active_voice_count(synth);\n\tif (!actualCount) {\n\t\treturn 0;\n\t}\n\n\t// FluidSynth may return incorrect value for active voice count,\n\t// so check internal data and correct it\n\n\t// check if the structure is not changed\n\t// for fluidsynth 2.0.x-2.1.x:\n\t//   140 === offset [synth->voice]\n\t//   144 === offset [synth->active_voice_count] for \n\t// for fluidsynth 2.2.x:\n\t//   144 === offset [synth->voice]\n\t//   148 === offset [synth->active_voice_count]\n\t// first check 2.1.x structure\n\tlet baseOffsetOfVoice = 140;\n\tlet offsetOfActiveVoiceCount = (synth + baseOffsetOfVoice + 4) >> 2;\n\tlet structActiveVoiceCount = _module.HEAPU32[offsetOfActiveVoiceCount];\n\tif (structActiveVoiceCount !== actualCount) {\n\t\t// add 4 for 2.2.x\n\t\tbaseOffsetOfVoice += 4;\n\t\toffsetOfActiveVoiceCount = (synth + baseOffsetOfVoice + 4) >> 2;\n\t\tstructActiveVoiceCount = _module.HEAPU32[offsetOfActiveVoiceCount];\n\t\tif (structActiveVoiceCount !== actualCount) {\n\t\t\t// unknown structure\n\t\t\tconst c = console;\n\t\t\tc.warn(\n\t\t\t\t'js-synthesizer: cannot check synthesizer internal data (may be changed)'\n\t\t\t);\n\t\t\treturn actualCount;\n\t\t}\n\t}\n\n\tconst voiceList = _module.HEAPU32[(synth + baseOffsetOfVoice) >> 2];\n\t// (voice should not be NULL)\n\tif (!voiceList || voiceList >= _module.HEAPU32.byteLength) {\n\t\t// unknown structure\n\t\tconst c = console;\n\t\tc.warn(\n\t\t\t'js-synthesizer: cannot check synthesizer internal data (may be changed)'\n\t\t);\n\t\treturn actualCount;\n\t}\n\n\t// count of internal voice data is restricted to polyphony value\n\tconst voiceCount = _module._fluid_synth_get_polyphony(synth);\n\tlet isRunning = false;\n\tfor (let i = 0; i < voiceCount; ++i) {\n\t\t// auto voice = voiceList[i]\n\t\tconst voice = _module.HEAPU32[(voiceList >> 2) + i];\n\t\tif (!voice) {\n\t\t\tcontinue;\n\t\t}\n\t\t// offset [voice->status]\n\t\tconst status = _module.HEAPU8[voice + 4];\n\t\t// 4: FLUID_VOICE_OFF\n\t\tif (status !== 4) {\n\t\t\tisRunning = true;\n\t\t\tbreak;\n\t\t}\n\t}\n\tif (!isRunning) {\n\t\tif (structActiveVoiceCount !== 0) {\n\t\t\tconst c = console;\n\t\t\tc.warn(\n\t\t\t\t'js-synthesizer: Active voice count is not zero, but all voices are off:',\n\t\t\t\tstructActiveVoiceCount,\n\t\t\t);\n\t\t}\n\t\t_module.HEAPU32[offsetOfActiveVoiceCount] = 0;\n\t\treturn 0;\n\t}\n\n\treturn actualCount;\n}\n\nfunction makeRandomFileName(type: string, ext: string) {\n\treturn `/${type}-r${Math.random() * 65535}-${Math.random() * 65535}${ext}`;\n}\n\n/** Hook callback function type */\nexport interface HookMIDIEventCallback {\n\t/**\n\t * Hook callback function type.\n\t * @param synth the base synthesizer instance\n\t * @param eventType MIDI event type (e.g. 0x90 is note-on event)\n\t * @param eventData detailed event data\n\t * @param param parameter data passed to the registration method\n\t * @return true if the event data is processed, or false if the default processing is necessary\n\t */\n\t(synth: Synthesizer, eventType: number, eventData: IMIDIEvent, param: any): boolean;\n}\n\n/** Client callback function type for sequencer object */\nexport interface SequencerClientCallback {\n\t/**\n\t * Client callback function type for sequencer object.\n\t * @param time the sequencer tick value\n\t * @param eventType sequencer event type\n\t * @param event actual event data (can only be used in this callback function)\n\t * @param sequencer the base sequencer object\n\t * @param param parameter data passed to the registration method\n\t */\n\t(time: number, eventType: SequencerEventType, event: ISequencerEventData, sequencer: ISequencer, param: number): void;\n}\n\nfunction makeMIDIEventCallback(synth: Synthesizer, cb: HookMIDIEventCallback, param: any) {\n\treturn (data: PointerType, event: MIDIEventType): number => {\n\t\tconst t = _module._fluid_midi_event_get_type(event);\n\t\tif (cb(synth, t, new MIDIEvent(event, _module), param)) {\n\t\t\treturn 0;\n\t\t}\n\t\treturn _module._fluid_synth_handle_midi_event(data, event);\n\t};\n}\n\n/** Default implementation of ISynthesizer */\nexport default class Synthesizer implements ISynthesizer {\n\t/** @internal */\n\tprivate _settings: SettingsId;\n\t/** @internal */\n\tprivate _synth: SynthId;\n\t/** @internal */\n\tprivate _player: PlayerId;\n\t/** @internal */\n\tprivate _playerPlaying: boolean;\n\t/** @internal */\n\tprivate _playerDefer:\n\t\t| undefined\n\t\t| {\n\t\t\t\tpromise: Promise<void>;\n\t\t\t\tresolve: () => void;\n\t\t  };\n\t/** @internal */\n\tprivate _playerCallbackPtr: number | null;\n\t/** @internal */\n\tprivate _fluidSynthCallback: PointerType | null;\n\n\t/** @internal */\n\tprivate _buffer: PointerType;\n\t/** @internal */\n\tprivate _bufferSize: number;\n\t/** @internal */\n\tprivate _numPtr: PointerType;\n\n\t/** @internal */\n\tprivate _gain: number;\n\n\tconstructor() {\n\t\tbindFunctions();\n\n\t\tthis._settings = INVALID_POINTER;\n\t\tthis._synth = INVALID_POINTER;\n\t\tthis._player = INVALID_POINTER;\n\t\tthis._playerPlaying = false;\n\t\tthis._playerCallbackPtr = null;\n\t\tthis._fluidSynthCallback = null;\n\n\t\tthis._buffer = INVALID_POINTER;\n\t\tthis._bufferSize = 0;\n\t\tthis._numPtr = INVALID_POINTER;\n\n\t\tthis._gain = SynthesizerDefaultValues.Gain;\n\t}\n\n\t/** Return the promise object that resolves when WebAssembly has been initialized */\n\tpublic static waitForWasmInitialized(): Promise<void> {\n\t\treturn waitForInitialized();\n\t}\n\n\tpublic isInitialized() {\n\t\treturn this._synth !== INVALID_POINTER;\n\t}\n\n\t/** Return the raw synthesizer instance value (pointer for libfluidsynth). */\n\tpublic getRawSynthesizer(): number {\n\t\treturn this._synth;\n\t}\n\n\tpublic createAudioNode(\n\t\tcontext: AudioContext,\n\t\tframeSize?: number\n\t): AudioNode {\n\t\tconst node = context.createScriptProcessor(frameSize, 0, 2);\n\t\tnode.addEventListener(\"audioprocess\", (ev) => {\n\t\t\tthis.render(ev.outputBuffer);\n\t\t});\n\t\treturn node;\n\t}\n\n\tpublic init(sampleRate: number, settings?: SynthesizerSettings) {\n\t\tthis.close();\n\n\t\tconst set = (this._settings = _module._new_fluid_settings());\n\t\tfluid_settings_setnum(set, \"synth.sample-rate\", sampleRate);\n\t\tif (settings) {\n\t\t\tif (typeof settings.initialGain !== \"undefined\") {\n\t\t\t\tthis._gain = settings.initialGain;\n\t\t\t}\n\t\t\tsetBoolValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.active\",\n\t\t\t\tsettings.chorusActive\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.depth\",\n\t\t\t\tsettings.chorusDepth\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.level\",\n\t\t\t\tsettings.chorusLevel\n\t\t\t);\n\t\t\tsetIntValueForSettings(set, \"synth.chorus.nr\", settings.chorusNr);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.speed\",\n\t\t\t\tsettings.chorusSpeed\n\t\t\t);\n\t\t\tsetIntValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.midi-channels\",\n\t\t\t\tsettings.midiChannelCount\n\t\t\t);\n\t\t\tsetStrValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.midi-bank-select\",\n\t\t\t\tsettings.midiBankSelect\n\t\t\t);\n\t\t\tsetIntValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.min-note-length\",\n\t\t\t\tsettings.minNoteLength\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.age\",\n\t\t\t\tsettings.overflowAge\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.important\",\n\t\t\t\tsettings.overflowImportantValue\n\t\t\t);\n\t\t\tif (typeof settings.overflowImportantChannels !== \"undefined\") {\n\t\t\t\tfluid_settings_setstr(\n\t\t\t\t\tset,\n\t\t\t\t\t\"synth.overflow.important-channels\",\n\t\t\t\t\tsettings.overflowImportantChannels.join(\",\")\n\t\t\t\t);\n\t\t\t}\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.percussion\",\n\t\t\t\tsettings.overflowPercussion\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.released\",\n\t\t\t\tsettings.overflowReleased\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.sustained\",\n\t\t\t\tsettings.overflowSustained\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.volume\",\n\t\t\t\tsettings.overflowVolume\n\t\t\t);\n\t\t\tsetIntValueForSettings(set, \"synth.polyphony\", settings.polyphony);\n\t\t\tsetBoolValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.active\",\n\t\t\t\tsettings.reverbActive\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.damp\",\n\t\t\t\tsettings.reverbDamp\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.level\",\n\t\t\t\tsettings.reverbLevel\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.room-size\",\n\t\t\t\tsettings.reverbRoomSize\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.width\",\n\t\t\t\tsettings.reverbWidth\n\t\t\t);\n\t\t}\n\t\tfluid_settings_setnum(set, \"synth.gain\", this._gain);\n\n\t\tthis._synth = _module._new_fluid_synth(this._settings);\n\n\t\tthis._numPtr = malloc(8);\n\t}\n\n\tpublic close() {\n\t\tif (this._synth === INVALID_POINTER) {\n\t\t\treturn;\n\t\t}\n\t\tthis._closePlayer();\n\t\t_module._delete_fluid_synth(this._synth);\n\t\tthis._synth = INVALID_POINTER;\n\t\t_module._delete_fluid_settings(this._settings);\n\t\tthis._settings = INVALID_POINTER;\n\t\tfree(this._numPtr);\n\t\tthis._numPtr = INVALID_POINTER;\n\t}\n\n\tpublic isPlaying() {\n\t\treturn (\n\t\t\tthis._synth !== INVALID_POINTER &&\n\t\t\tgetActiveVoiceCount(this._synth) > 0\n\t\t);\n\t}\n\n\tpublic setInterpolation(value: InterpolationValues, channel?: number) {\n\t\tthis.ensureInitialized();\n\t\tif (typeof channel === \"undefined\") {\n\t\t\tchannel = -1;\n\t\t}\n\t\t_module._fluid_synth_set_interp_method(this._synth, channel, value);\n\t}\n\n\tpublic getGain() {\n\t\treturn this._gain;\n\t}\n\n\tpublic setGain(gain: number) {\n\t\tthis.ensureInitialized();\n\t\t_module._fluid_synth_set_gain(this._synth, gain);\n\t\tthis._gain = _module._fluid_synth_get_gain(this._synth);\n\t}\n\n\tpublic setChannelType(channel: number, isDrum: boolean) {\n\t\tthis.ensureInitialized();\n\t\t// CHANNEL_TYPE_MELODIC = 0, CHANNEL_TYPE_DRUM = 1\n\t\t_module._fluid_synth_set_channel_type(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tisDrum ? 1 : 0\n\t\t);\n\t}\n\n\tpublic waitForVoicesStopped() {\n\t\treturn this.flushFramesAsync();\n\t}\n\n\tpublic loadSFont(bin: ArrayBuffer) {\n\t\tthis.ensureInitialized();\n\n\t\tconst name = makeRandomFileName(\"sfont\", \".sf2\");\n\t\tconst ub = new Uint8Array(bin);\n\n\t\t_fs.writeFile(name, ub);\n\t\tconst sfont = fluid_synth_sfload(this._synth, name, 1);\n\t\t_fs.unlink(name);\n\t\treturn sfont === -1\n\t\t\t? Promise.reject(new Error(fluid_synth_error!(this._synth)))\n\t\t\t: Promise.resolve(sfont);\n\t}\n\n\tpublic unloadSFont(id: number) {\n\t\tthis.ensureInitialized();\n\t\tthis.stopPlayer();\n\t\tthis.flushFramesSync();\n\n\t\t_module._fluid_synth_sfunload(this._synth, id, 1);\n\t}\n\n\tpublic unloadSFontAsync(id: number) {\n\t\t// not throw with Promise.reject\n\t\tthis.ensureInitialized();\n\t\tthis.stopPlayer();\n\t\treturn this.flushFramesAsync().then(() => {\n\t\t\t_module._fluid_synth_sfunload(this._synth, id, 1);\n\t\t});\n\t}\n\n\t/**\n\t * Returns the `Soundfont` instance for specified SoundFont.\n\t * @param sfontId loaded SoundFont id ({@link loadSFont} returns this)\n\t * @return `Soundfont` instance or `null` if `sfontId` is not valid or loaded\n\t */\n\tpublic getSFontObject(sfontId: number): Soundfont | null {\n\t\treturn Soundfont.getSoundfontById(this, sfontId);\n\t}\n\n\tpublic getSFontBankOffset(id: number) {\n\t\tthis.ensureInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_synth_get_bank_offset(this._synth, id) as number\n\t\t);\n\t}\n\tpublic setSFontBankOffset(id: number, offset: number) {\n\t\tthis.ensureInitialized();\n\t\t_module._fluid_synth_set_bank_offset(this._synth, id, offset);\n\t}\n\n\tpublic render(outBuffer: AudioBuffer | Float32Array[]) {\n\t\tconst frameCount =\n\t\t\t\"numberOfChannels\" in outBuffer\n\t\t\t\t? outBuffer.length\n\t\t\t\t: outBuffer[0].length;\n\t\tconst channels =\n\t\t\t\"numberOfChannels\" in outBuffer\n\t\t\t\t? outBuffer.numberOfChannels\n\t\t\t\t: outBuffer.length;\n\t\tconst sizePerChannel = 4 * frameCount;\n\t\tconst totalSize = sizePerChannel * 2;\n\t\tif (this._bufferSize < totalSize) {\n\t\t\tif (this._buffer !== INVALID_POINTER) {\n\t\t\t\tfree(this._buffer);\n\t\t\t}\n\t\t\tthis._buffer = malloc(totalSize);\n\t\t\tthis._bufferSize = totalSize;\n\t\t}\n\n\t\tconst memLeft = this._buffer;\n\t\tconst memRight = ((this._buffer as number) +\n\t\t\tsizePerChannel) as PointerType;\n\t\tthis.renderRaw(memLeft, memRight, frameCount);\n\n\t\tconst aLeft = new Float32Array(\n\t\t\t_module.HEAPU8.buffer,\n\t\t\tmemLeft,\n\t\t\tframeCount\n\t\t);\n\t\tconst aRight =\n\t\t\tchannels >= 2\n\t\t\t\t? new Float32Array(_module.HEAPU8.buffer, memRight, frameCount)\n\t\t\t\t: null;\n\t\tif (\"numberOfChannels\" in outBuffer) {\n\t\t\tif (outBuffer.copyToChannel) {\n\t\t\t\toutBuffer.copyToChannel(aLeft, 0, 0);\n\t\t\t\tif (aRight) {\n\t\t\t\t\toutBuffer.copyToChannel(aRight, 1, 0);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// copyToChannel API not exist in Safari AudioBuffer\n\t\t\t\tconst leftData = outBuffer.getChannelData(0);\n\t\t\t\taLeft.forEach((val, i) => (leftData[i] = val));\n\t\t\t\tif (aRight) {\n\t\t\t\t\tconst rightData = outBuffer.getChannelData(1);\n\t\t\t\t\taRight.forEach((val, i) => (rightData[i] = val));\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\toutBuffer[0].set(aLeft);\n\t\t\tif (aRight) {\n\t\t\t\toutBuffer[1].set(aRight);\n\t\t\t}\n\t\t}\n\n\t\t// check and update player status\n\t\tthis.isPlayerPlaying();\n\t}\n\n\tpublic midiNoteOn(chan: number, key: number, vel: number) {\n\t\t_module._fluid_synth_noteon(this._synth, chan, key, vel);\n\t}\n\tpublic midiNoteOff(chan: number, key: number) {\n\t\t_module._fluid_synth_noteoff(this._synth, chan, key);\n\t}\n\tpublic midiKeyPressure(chan: number, key: number, val: number) {\n\t\t_module._fluid_synth_key_pressure(this._synth, chan, key, val);\n\t}\n\tpublic midiControl(chan: number, ctrl: number, val: number) {\n\t\t_module._fluid_synth_cc(this._synth, chan, ctrl, val);\n\t}\n\tpublic midiProgramChange(chan: number, prognum: number) {\n\t\t_module._fluid_synth_program_change(this._synth, chan, prognum);\n\t}\n\tpublic midiChannelPressure(chan: number, val: number) {\n\t\t_module._fluid_synth_channel_pressure(this._synth, chan, val);\n\t}\n\tpublic midiPitchBend(chan: number, val: number) {\n\t\t_module._fluid_synth_pitch_bend(this._synth, chan, val);\n\t}\n\tpublic midiSysEx(data: Uint8Array) {\n\t\tconst len = data.byteLength;\n\t\tconst mem = malloc(len);\n\t\t_module.HEAPU8.set(data, mem);\n\t\t_module._fluid_synth_sysex(\n\t\t\tthis._synth,\n\t\t\tmem,\n\t\t\tlen,\n\t\t\tINVALID_POINTER,\n\t\t\tINVALID_POINTER,\n\t\t\tINVALID_POINTER,\n\t\t\t0\n\t\t);\n\t\tfree(mem);\n\t}\n\n\tpublic midiPitchWheelSensitivity(chan: number, val: number) {\n\t\t_module._fluid_synth_pitch_wheel_sens(this._synth, chan, val);\n\t}\n\tpublic midiBankSelect(chan: number, bank: number) {\n\t\t_module._fluid_synth_bank_select(this._synth, chan, bank);\n\t}\n\tpublic midiSFontSelect(chan: number, sfontId: number) {\n\t\t_module._fluid_synth_sfont_select(this._synth, chan, sfontId);\n\t}\n\tpublic midiProgramSelect(\n\t\tchan: number,\n\t\tsfontId: number,\n\t\tbank: number,\n\t\tpresetNum: number\n\t) {\n\t\t_module._fluid_synth_program_select(\n\t\t\tthis._synth,\n\t\t\tchan,\n\t\t\tsfontId,\n\t\t\tbank,\n\t\t\tpresetNum\n\t\t);\n\t}\n\tpublic midiUnsetProgram(chan: number) {\n\t\t_module._fluid_synth_unset_program(this._synth, chan);\n\t}\n\tpublic midiProgramReset() {\n\t\t_module._fluid_synth_program_reset(this._synth);\n\t}\n\tpublic midiSystemReset() {\n\t\t_module._fluid_synth_system_reset(this._synth);\n\t}\n\tpublic midiAllNotesOff(chan?: number) {\n\t\t_module._fluid_synth_all_notes_off(\n\t\t\tthis._synth,\n\t\t\ttypeof chan === \"undefined\" ? -1 : chan\n\t\t);\n\t}\n\tpublic midiAllSoundsOff(chan?: number) {\n\t\t_module._fluid_synth_all_sounds_off(\n\t\t\tthis._synth,\n\t\t\ttypeof chan === \"undefined\" ? -1 : chan\n\t\t);\n\t}\n\tpublic midiSetChannelType(chan: number, isDrum: boolean) {\n\t\t// CHANNEL_TYPE_MELODIC = 0\n\t\t// CHANNEL_TYPE_DRUM = 1\n\t\t_module._fluid_synth_set_channel_type(\n\t\t\tthis._synth,\n\t\t\tchan,\n\t\t\tisDrum ? 1 : 0\n\t\t);\n\t}\n\n\t/**\n\t * Set reverb parameters to the synthesizer.\n\t */\n\tpublic setReverb(\n\t\troomsize: number,\n\t\tdamping: number,\n\t\twidth: number,\n\t\tlevel: number\n\t) {\n\t\t_module._fluid_synth_set_reverb(\n\t\t\tthis._synth,\n\t\t\troomsize,\n\t\t\tdamping,\n\t\t\twidth,\n\t\t\tlevel\n\t\t);\n\t}\n\t/**\n\t * Set reverb roomsize parameter to the synthesizer.\n\t */\n\tpublic setReverbRoomsize(roomsize: number) {\n\t\t_module._fluid_synth_set_reverb_roomsize(this._synth, roomsize);\n\t}\n\t/**\n\t * Set reverb damping parameter to the synthesizer.\n\t */\n\tpublic setReverbDamp(damping: number) {\n\t\t_module._fluid_synth_set_reverb_damp(this._synth, damping);\n\t}\n\t/**\n\t * Set reverb width parameter to the synthesizer.\n\t */\n\tpublic setReverbWidth(width: number) {\n\t\t_module._fluid_synth_set_reverb_width(this._synth, width);\n\t}\n\t/**\n\t * Set reverb level to the synthesizer.\n\t */\n\tpublic setReverbLevel(level: number) {\n\t\t_module._fluid_synth_set_reverb_level(this._synth, level);\n\t}\n\t/**\n\t * Enable or disable reverb effect of the synthesizer.\n\t */\n\tpublic setReverbOn(on: boolean) {\n\t\t_module._fluid_synth_set_reverb_on(this._synth, on ? 1 : 0);\n\t}\n\t/**\n\t * Get reverb roomsize parameter of the synthesizer.\n\t */\n\tpublic getReverbRoomsize(): number {\n\t\treturn _module._fluid_synth_get_reverb_roomsize(this._synth);\n\t}\n\t/**\n\t * Get reverb damping parameter of the synthesizer.\n\t */\n\tpublic getReverbDamp(): number {\n\t\treturn _module._fluid_synth_get_reverb_damp(this._synth);\n\t}\n\t/**\n\t * Get reverb level of the synthesizer.\n\t */\n\tpublic getReverbLevel(): number {\n\t\treturn _module._fluid_synth_get_reverb_level(this._synth);\n\t}\n\t/**\n\t * Get reverb width parameter of the synthesizer.\n\t */\n\tpublic getReverbWidth(): number {\n\t\treturn _module._fluid_synth_get_reverb_width(this._synth);\n\t}\n\n\t/**\n\t * Set chorus parameters to the synthesizer.\n\t */\n\tpublic setChorus(\n\t\tvoiceCount: number,\n\t\tlevel: number,\n\t\tspeed: number,\n\t\tdepthMillisec: number,\n\t\ttype: ChorusModulation\n\t) {\n\t\t_module._fluid_synth_set_chorus(\n\t\t\tthis._synth,\n\t\t\tvoiceCount,\n\t\t\tlevel,\n\t\t\tspeed,\n\t\t\tdepthMillisec,\n\t\t\ttype\n\t\t);\n\t}\n\t/**\n\t * Set chorus voice count parameter to the synthesizer.\n\t */\n\tpublic setChorusVoiceCount(voiceCount: number) {\n\t\t_module._fluid_synth_set_chorus_nr(this._synth, voiceCount);\n\t}\n\t/**\n\t * Set chorus level parameter to the synthesizer.\n\t */\n\tpublic setChorusLevel(level: number) {\n\t\t_module._fluid_synth_set_chorus_level(this._synth, level);\n\t}\n\t/**\n\t * Set chorus speed parameter to the synthesizer.\n\t */\n\tpublic setChorusSpeed(speed: number) {\n\t\t_module._fluid_synth_set_chorus_speed(this._synth, speed);\n\t}\n\t/**\n\t * Set chorus depth parameter to the synthesizer.\n\t */\n\tpublic setChorusDepth(depthMillisec: number) {\n\t\t_module._fluid_synth_set_chorus_depth(this._synth, depthMillisec);\n\t}\n\t/**\n\t * Set chorus modulation type to the synthesizer.\n\t */\n\tpublic setChorusType(type: ChorusModulation) {\n\t\t_module._fluid_synth_set_chorus_type(this._synth, type);\n\t}\n\t/**\n\t * Enable or disable chorus effect of the synthesizer.\n\t */\n\tpublic setChorusOn(on: boolean) {\n\t\t_module._fluid_synth_set_chorus_on(this._synth, on ? 1 : 0);\n\t}\n\t/**\n\t * Get chorus voice count of the synthesizer.\n\t */\n\tpublic getChorusVoiceCount(): number {\n\t\treturn _module._fluid_synth_get_chorus_nr(this._synth);\n\t}\n\t/**\n\t * Get chorus level of the synthesizer.\n\t */\n\tpublic getChorusLevel(): number {\n\t\treturn _module._fluid_synth_get_chorus_level(this._synth);\n\t}\n\t/**\n\t * Get chorus speed of the synthesizer.\n\t */\n\tpublic getChorusSpeed(): number {\n\t\treturn _module._fluid_synth_get_chorus_speed(this._synth);\n\t}\n\t/**\n\t * Get chorus depth (in milliseconds) of the synthesizer.\n\t */\n\tpublic getChorusDepth(): number {\n\t\treturn _module._fluid_synth_get_chorus_depth(this._synth);\n\t}\n\t/**\n\t * Get chorus modulation type of the synthesizer.\n\t */\n\tpublic getChorusType(): ChorusModulation {\n\t\treturn _module._fluid_synth_get_chorus_type(this._synth);\n\t}\n\n\t/**\n\t * Get generator value assigned to the MIDI channel.\n\t * @param channel MIDI channel number\n\t * @param param generator ID\n\t * @return a value related to the generator\n\t */\n\tpublic getGenerator(channel: number, param: GeneratorTypes): number {\n\t\treturn _module._fluid_synth_get_gen(this._synth, channel, param);\n\t}\n\t/**\n\t * Set generator value assigned to the MIDI channel.\n\t * @param channel MIDI channel number\n\t * @param param generator ID\n\t * @param value a value related to the generator\n\t */\n\tpublic setGenerator(channel: number, param: GeneratorTypes, value: number) {\n\t\t_module._fluid_synth_set_gen(this._synth, channel, param, value);\n\t}\n\t/**\n\t * Return the current legato mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return legato mode\n\t */\n\tpublic getLegatoMode(channel: number) {\n\t\t_module._fluid_synth_get_legato_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as LegatoMode;\n\t}\n\t/**\n\t * Set the current legato mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param mode legato mode\n\t */\n\tpublic setLegatoMode(channel: number, mode: LegatoMode) {\n\t\t_module._fluid_synth_set_legato_mode(this._synth, channel, mode);\n\t}\n\t/**\n\t * Return the current portamento mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return portamento mode\n\t */\n\tpublic getPortamentoMode(channel: number) {\n\t\t_module._fluid_synth_get_portamento_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as PortamentoMode;\n\t}\n\t/**\n\t * Set the current portamento mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param mode portamento mode\n\t */\n\tpublic setPortamentoMode(channel: number, mode: PortamentoMode) {\n\t\t_module._fluid_synth_set_portamento_mode(this._synth, channel, mode);\n\t}\n\t/**\n\t * Return the current breath mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return breath mode (BreathFlags)\n\t */\n\tpublic getBreathMode(channel: number) {\n\t\t_module._fluid_synth_get_breath_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as number;\n\t}\n\t/**\n\t * Set the current breath mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param flags breath mode flags (BreathFlags)\n\t */\n\tpublic setBreathMode(channel: number, flags: number) {\n\t\t_module._fluid_synth_set_breath_mode(this._synth, channel, flags);\n\t}\n\n\t////////////////////////////////////////////////////////////////////////////\n\n\tpublic resetPlayer() {\n\t\treturn new Promise<void>((resolve) => {\n\t\t\tthis._initPlayer();\n\t\t\tresolve();\n\t\t});\n\t}\n\n\tpublic closePlayer() {\n\t\tthis._closePlayer();\n\t}\n\n\t/** @internal */\n\tprivate _initPlayer() {\n\t\tthis._closePlayer();\n\n\t\tconst player = _module._new_fluid_player(this._synth);\n\t\tthis._player = player;\n\t\tif (player !== INVALID_POINTER) {\n\t\t\tif (this._fluidSynthCallback === null) {\n\t\t\t\t// hacky retrieve 'fluid_synth_handle_midi_event' callback pointer\n\t\t\t\t// * 'playback_callback' is filled with 'fluid_synth_handle_midi_event' by default.\n\t\t\t\t// * 'playback_userdata' is filled with the synthesizer pointer by default\n\t\t\t\tconst funcPtr: PointerType =\n\t\t\t\t\t_module.HEAPU32[((player as number) + 588) >> 2]; // _fluid_player_t::playback_callback\n\t\t\t\tconst synthPtr: SynthId =\n\t\t\t\t\t_module.HEAPU32[((player as number) + 592) >> 2]; // _fluid_player_t::playback_userdata\n\t\t\t\tif (synthPtr === this._synth) {\n\t\t\t\t\tthis._fluidSynthCallback = funcPtr;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tthrow new Error(\"Out of memory\");\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate _closePlayer() {\n\t\tconst p = this._player;\n\t\tif (p === INVALID_POINTER) {\n\t\t\treturn;\n\t\t}\n\t\tthis.stopPlayer();\n\t\t_module._delete_fluid_player(p);\n\t\tthis._player = INVALID_POINTER;\n\t\tthis._playerCallbackPtr = null;\n\t}\n\n\tpublic isPlayerPlaying() {\n\t\tif (this._playerPlaying) {\n\t\t\tconst status = _module._fluid_player_get_status(this._player);\n\t\t\tif (status === 1 /*FLUID_PLAYER_PLAYING*/) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tthis.stopPlayer();\n\t\t}\n\t\treturn false;\n\t}\n\n\tpublic addSMFDataToPlayer(bin: ArrayBuffer) {\n\t\tthis.ensurePlayerInitialized();\n\t\tconst len = bin.byteLength;\n\t\tconst mem = malloc(len);\n\t\t_module.HEAPU8.set(new Uint8Array(bin), mem);\n\t\tconst r: number = _module._fluid_player_add_mem(this._player, mem, len);\n\t\tfree(mem);\n\t\treturn r !== -1\n\t\t\t? Promise.resolve()\n\t\t\t: Promise.reject(new Error(fluid_synth_error!(this._synth)));\n\t}\n\n\tpublic playPlayer() {\n\t\tthis.ensurePlayerInitialized();\n\t\tif (this._playerPlaying) {\n\t\t\tthis.stopPlayer();\n\t\t}\n\n\t\tif (_module._fluid_player_play(this._player) === -1) {\n\t\t\treturn Promise.reject(new Error(fluid_synth_error!(this._synth)));\n\t\t}\n\t\tthis._playerPlaying = true;\n\t\tlet resolver = () => {};\n\t\tconst p = new Promise<void>((resolve) => {\n\t\t\tresolver = resolve;\n\t\t});\n\t\tthis._playerDefer = {\n\t\t\tpromise: p,\n\t\t\tresolve: resolver,\n\t\t};\n\t\treturn Promise.resolve();\n\t}\n\n\tpublic stopPlayer() {\n\t\tconst p = this._player;\n\t\tif (p === INVALID_POINTER || !this._playerPlaying) {\n\t\t\treturn;\n\t\t}\n\t\t_module._fluid_player_stop(p);\n\t\t_module._fluid_player_join(p);\n\t\t_module._fluid_synth_all_sounds_off(this._synth, -1);\n\t\tif (this._playerDefer) {\n\t\t\tthis._playerDefer.resolve();\n\t\t\tthis._playerDefer = void 0;\n\t\t}\n\t\tthis._playerPlaying = false;\n\t}\n\n\tpublic retrievePlayerCurrentTick(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_current_tick(this._player)\n\t\t);\n\t}\n\tpublic retrievePlayerTotalTicks(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_total_ticks(this._player)\n\t\t);\n\t}\n\tpublic retrievePlayerBpm(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(_module._fluid_player_get_bpm(this._player));\n\t}\n\tpublic retrievePlayerMIDITempo(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_midi_tempo(this._player)\n\t\t);\n\t}\n\tpublic seekPlayer(ticks: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_seek(this._player, ticks);\n\t}\n\tpublic setPlayerLoop(loopTimes: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_set_loop(this._player, loopTimes);\n\t}\n\tpublic setPlayerTempo(tempoType: PlayerSetTempoType, tempo: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_set_tempo(this._player, tempoType, tempo);\n\t}\n\n\t/**\n\t * Hooks MIDI events sent by the player.\n\t * initPlayer() must be called before calling this method.\n\t * @param callback hook callback function, or null to unhook\n\t * @param param any additional data passed to the callback\n\t */\n\tpublic hookPlayerMIDIEvents(\n\t\tcallback: HookMIDIEventCallback | null,\n\t\tparam?: any\n\t) {\n\t\tthis.ensurePlayerInitialized();\n\n\t\tconst oldPtr = this._playerCallbackPtr;\n\t\tif (oldPtr === null && callback === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst newPtr =\n\t\t\t// if callback is specified, add function\n\t\t\tcallback !== null\n\t\t\t\t? _addFunction(\n\t\t\t\t\t\tmakeMIDIEventCallback(this, callback, param),\n\t\t\t\t\t\t\"iii\"\n\t\t\t\t  )\n\t\t\t\t: // if _fluidSynthCallback is filled, set null to use it for reset callback\n\t\t\t\t// if not, add function defaultMIDIEventCallback for reset\n\t\t\t\tthis._fluidSynthCallback !== null\n\t\t\t\t? null\n\t\t\t\t: _addFunction(defaultMIDIEventCallback, \"iii\");\n\t\t// the third parameter of 'fluid_player_set_playback_callback' should be 'fluid_synth_t*'\n\t\tif (oldPtr !== null && newPtr !== null) {\n\t\t\t// (using defaultMIDIEventCallback also comes here)\n\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\tthis._player,\n\t\t\t\tnewPtr,\n\t\t\t\tthis._synth\n\t\t\t);\n\t\t\t_removeFunction(oldPtr);\n\t\t} else {\n\t\t\tif (newPtr === null) {\n\t\t\t\t// newPtr === null --> use _fluidSynthCallback\n\t\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\t\tthis._player,\n\t\t\t\t\tthis._fluidSynthCallback!,\n\t\t\t\t\tthis._synth\n\t\t\t\t);\n\t\t\t\t_removeFunction(oldPtr!);\n\t\t\t} else {\n\t\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\t\tthis._player,\n\t\t\t\t\tnewPtr,\n\t\t\t\t\tthis._synth\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t\tthis._playerCallbackPtr = newPtr;\n\t}\n\n\t/** @internal */\n\tprivate ensureInitialized() {\n\t\tif (this._synth === INVALID_POINTER) {\n\t\t\tthrow new Error(\"Synthesizer is not initialized\");\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate ensurePlayerInitialized() {\n\t\tthis.ensureInitialized();\n\t\tif (this._player === INVALID_POINTER) {\n\t\t\tthis._initPlayer();\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate renderRaw(\n\t\tmemLeft: PointerType,\n\t\tmemRight: PointerType,\n\t\tframeCount: number\n\t) {\n\t\t_module._fluid_synth_write_float(\n\t\t\tthis._synth,\n\t\t\tframeCount,\n\t\t\tmemLeft,\n\t\t\t0,\n\t\t\t1,\n\t\t\tmemRight,\n\t\t\t0,\n\t\t\t1\n\t\t);\n\t}\n\n\t/** @internal */\n\tprivate flushFramesSync() {\n\t\tconst frameCount = 65536;\n\t\tconst size = 4 * frameCount;\n\t\tconst mem = malloc(size * 2);\n\t\tconst memLeft = mem;\n\t\tconst memRight = ((mem as number) + size) as PointerType;\n\t\twhile (this.isPlaying()) {\n\t\t\tthis.renderRaw(memLeft, memRight, frameCount);\n\t\t}\n\t\tfree(mem);\n\t}\n\n\t/** @internal */\n\tprivate flushFramesAsync() {\n\t\tif (!this.isPlaying()) {\n\t\t\treturn Promise.resolve();\n\t\t}\n\t\tconst frameCount = 65536;\n\t\tconst size = 4 * frameCount;\n\t\tconst mem = malloc(size * 2);\n\t\tconst memLeft = mem;\n\t\tconst memRight = ((mem as number) + size) as PointerType;\n\t\tconst nextFrame =\n\t\t\ttypeof setTimeout !== \"undefined\"\n\t\t\t\t? () => {\n\t\t\t\t\t\treturn new Promise<void>((resolve) =>\n\t\t\t\t\t\t\tsetTimeout(resolve, 0)\n\t\t\t\t\t\t);\n\t\t\t\t  }\n\t\t\t\t: () => {\n\t\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t  };\n\t\tfunction head(): Promise<void> {\n\t\t\treturn nextFrame().then(tail);\n\t\t}\n\t\tconst self = this;\n\t\tfunction tail(): Promise<void> {\n\t\t\tif (!self.isPlaying()) {\n\t\t\t\tfree(mem);\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\t\t\tself.renderRaw(memLeft, memRight, frameCount);\n\t\t\treturn head();\n\t\t}\n\t\treturn head();\n\t}\n\n\tpublic waitForPlayerStopped() {\n\t\treturn this._playerDefer\n\t\t\t? this._playerDefer.promise\n\t\t\t: Promise.resolve();\n\t}\n\n\t/**\n\t * Create the sequencer object for this class.\n\t */\n\tpublic static createSequencer(): Promise<ISequencer> {\n\t\tbindFunctions();\n\t\tconst seq = new Sequencer();\n\t\treturn seq._initialize().then(() => seq);\n\t}\n\n\t/**\n\t * Registers the user-defined client to the sequencer.\n\t * The client can receive events in the time from sequencer process.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param name the client name\n\t * @param callback the client callback function that processes event data\n\t * @param param additional parameter passed to the callback\n\t * @return registered sequencer client id (can be passed to seq.unregisterClient())\n\t */\n\tpublic static registerSequencerClient(\n\t\tseq: ISequencer,\n\t\tname: string,\n\t\tcallback: SequencerClientCallback,\n\t\tparam: number\n\t): number {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tconst ptr = _addFunction(\n\t\t\t(time: number, ev: PointerType, _seq: number, data: number) => {\n\t\t\t\tconst e = new SequencerEventData(ev, _module);\n\t\t\t\tconst type: SequencerEventType =\n\t\t\t\t\t_module._fluid_event_get_type(ev);\n\t\t\t\tcallback(time, type, e, seq, data);\n\t\t\t},\n\t\t\t\"viiii\"\n\t\t);\n\t\tconst r = fluid_sequencer_register_client(\n\t\t\tseq.getRaw(),\n\t\t\tname,\n\t\t\tptr,\n\t\t\tparam\n\t\t);\n\t\tif (r !== -1) {\n\t\t\tseq._clientFuncMap[r] = ptr;\n\t\t}\n\t\treturn r;\n\t}\n\n\t/**\n\t * Send sequencer event immediately to the specific client.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param clientId registered client id (-1 for registered synthesizer)\n\t * @param event event data\n\t */\n\tpublic static sendEventToClientNow(\n\t\tseq: ISequencer,\n\t\tclientId: number,\n\t\tevent: SequencerEvent\n\t): void {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tseq.sendEventToClientNow(clientId, event);\n\t}\n\t/**\n\t * (Re-)send event data immediately.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param clientId registered client id (-1 for registered synthesizer)\n\t * @param eventData event data which can be retrieved in SequencerClientCallback\n\t */\n\tpublic static sendEventNow(\n\t\tseq: ISequencer,\n\t\tclientId: number,\n\t\teventData: ISequencerEventData\n\t): void {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tseq.sendEventNow(clientId, eventData);\n\t}\n\t/**\n\t * Set interval timer process to call processSequencer for this sequencer.\n\t * This method uses 'setInterval' global method to register timer.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param msec time in milliseconds passed to both setInterval and processSequencer\n\t * @return return value of 'setInterval' (usually passing to 'clearInterval' will reset event)\n\t */\n\tpublic static setIntervalForSequencer(seq: ISequencer, msec: number) {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\treturn seq.setIntervalForSequencer(msec);\n\t}\n}\n", "let _module: any;\nlet _ptrDefaultLogFunction: number | undefined;\nlet _disabledLoggingLevel: LogLevel | null = null;\nconst _handlers: Array<(level: LogLevel | null) => void> = [];\n\nconst LOG_LEVEL_COUNT = 5;\n/** Log level for libfluidsynth */\nconst LogLevel = {\n\tPanic: 0,\n\tError: 1,\n\tWarning: 2,\n\tInfo: 3,\n\tDebug: 4,\n} as const;\n/** Log level for libfluidsynth */\ntype LogLevel = (typeof LogLevel)[keyof typeof LogLevel];\nexport { LogLevel };\n\nfunction bindFunctions() {\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t} else if (typeof Module !== 'undefined') {\n\t\t_module = Module;\n\t} else {\n\t\tthrow new Error(\n\t\t\t'wasm module is not available. libfluidsynth-*.js must be loaded.'\n\t\t);\n\t}\n}\n\n/**\n * Disable log output from libfluidsynth.\n * @param level disable log level (when `LogLevel.Warning` is specified, `Warning` `Info` `Debug` is disabled)\n * - If `null` is specified, log output feature is restored to the default.\n */\nexport function disableLogging(level: LogLevel | null = LogLevel.Panic): void {\n\tif (_disabledLoggingLevel === level) {\n\t\treturn;\n\t}\n\tbindFunctions();\n\tif (level == null) {\n\t\tif (_ptrDefaultLogFunction != null) {\n\t\t\t_module._fluid_set_log_function(0, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(1, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(2, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(3, _ptrDefaultLogFunction, 0);\n\t\t}\n\t\t_module._fluid_set_log_function(4, 0, 0);\n\t} else {\n\t\tlet ptr: number | undefined;\n\t\tfor (let l = level; l < LOG_LEVEL_COUNT; ++l) {\n\t\t\tconst p = _module._fluid_set_log_function(l, 0, 0);\n\t\t\tif (l !== LogLevel.Debug) {\n\t\t\t\tptr = p;\n\t\t\t}\n\t\t}\n\t\tif (ptr != null && _ptrDefaultLogFunction == null) {\n\t\t\t_ptrDefaultLogFunction = ptr;\n\t\t}\n\t}\n\t_disabledLoggingLevel = level;\n\tfor (const fn of _handlers) {\n\t\tfn(level);\n\t}\n}\n\n/**\n * Restores the log output from libfluidsynth. Same for calling `disableLogging(null)`.\n */\nexport function restoreLogging(): void {\n\tdisableLogging(null);\n}\n\n// @internal\nexport function getDisabledLoggingLevel(): LogLevel | null {\n\treturn _disabledLoggingLevel;\n}\n\n// @internal\nexport function addLoggingStatusChangedHandler(fn: (level: LogLevel | null) => void): void {\n\t_handlers.push(fn);\n}\n\n// @internal\nexport function removeLoggingStatusChangedHandler(fn: (level: LogLevel | null) => void): void {\n\tfor (let i = 0; i < _handlers.length; ++i) {\n\t\tif (_handlers[i] === fn) {\n\t\t\t_handlers.splice(i, 1);\n\t\t\treturn;\n\t\t}\n\t}\n}\n", "\nimport MessageError from './MessageError';\n\nexport interface MethodCallEventData {\n\tid: number;\n\tmethod: string;\n\targs: any[];\n}\n\nexport interface MethodReturnEventData {\n\tid: number;\n\tmethod: string;\n\tval: any;\n\terror?: MessageErrorData;\n}\n\nexport interface MessageErrorData {\n\tbaseName: string;\n\tmessage: string;\n\tdetail: any;\n}\n\n/** @internal */\nexport interface Defer<T> {\n\tresolve(value: T): void;\n\treject(reason: any): void;\n}\n\n/** @internal */\nexport interface DeferMap {\n\t[id: number]: Defer<any>;\n}\n\n/** @internal */\nexport type HookReturnMessageCallback = (data: MethodReturnEventData) => boolean;\n\n/** @internal */\nexport interface CallMessageInstance {\n\tport: MessagePort;\n\tdefers: DeferMap;\n\tdeferId: number;\n}\n\n/** @internal */\nexport function initializeCallPort(\n\tport: MessagePort,\n\thookMessage?: HookReturnMessageCallback | undefined\n): CallMessageInstance {\n\tconst instance: CallMessageInstance = {\n\t\tport: port,\n\t\tdefers: {},\n\t\tdeferId: 0\n\t};\n\tport.addEventListener('message', (e) => processReturnMessage(instance.defers, hookMessage, e));\n\tport.start();\n\treturn instance;\n}\n\nfunction convertErrorTransferable(err: Error): MessageErrorData {\n\tconst result: any = {};\n\tconst objList: any[] = [];\n\tlet obj: any = err;\n\twhile (obj && obj !== Object.prototype) {\n\t\tobjList.unshift(obj);\n\t\tobj = Object.getPrototypeOf(obj);\n\t}\n\tobjList.forEach((o) => {\n\t\tObject.getOwnPropertyNames(o).forEach((key) => {\n\t\t\ttry {\n\t\t\t\tconst data = (err as any)[key];\n\t\t\t\tif (typeof data !== 'function' && typeof data !== 'symbol') {\n\t\t\t\t\tresult[key] = data;\n\t\t\t\t}\n\t\t\t} catch (_e) { }\n\t\t});\n\t});\n\treturn {\n\t\tbaseName: err.name,\n\t\tmessage: err.message,\n\t\tdetail: result\n\t};\n}\n\nfunction convertAnyErrorTransferable(err: any): MessageErrorData {\n\treturn convertErrorTransferable((err && err instanceof Error) ? err : new Error(`${err}`));\n}\n\nfunction makeMessageError(error: MessageErrorData): MessageError {\n\treturn new MessageError(error.baseName, error.message, error.detail);\n}\n\nfunction processReturnMessage(defers: DeferMap, hook: HookReturnMessageCallback | undefined, e: MessageEvent) {\n\tconst data: MethodReturnEventData = e.data;\n\tif (!data) {\n\t\treturn;\n\t}\n\tif (hook && hook(data)) {\n\t\treturn;\n\t}\n\tconst defer = defers[data.id];\n\tif (defer) {\n\t\tdelete defers[data.id];\n\t\tif (data.error) {\n\t\t\tdefer.reject(makeMessageError(data.error));\n\t\t} else {\n\t\t\tdefer.resolve(data.val);\n\t\t}\n\t} else {\n\t\tif (data.error) {\n\t\t\tthrow makeMessageError(data.error);\n\t\t}\n\t}\n}\n\n/** @internal */\nexport function postCall(instance: CallMessageInstance, method: string, args: any[]): void;\n\n/** @internal */\nexport function postCall({ port }: CallMessageInstance, method: string, args: any[]) {\n\tport.postMessage({\n\t\tid: -1, method, args\n\t} as MethodCallEventData);\n}\n\n/** @internal */\nexport function postCallWithPromise<T>(instance: CallMessageInstance, method: string, args: any[]): Promise<T> {\n\tconst id = instance.deferId++;\n\tif (instance.deferId === Infinity || instance.deferId < 0) {\n\t\tinstance.deferId = 0;\n\t}\n\tconst promise = new Promise<T>((resolve, reject) => {\n\t\tinstance.defers[id] = { resolve, reject };\n\t});\n\tconst transfers: Transferable[] = [];\n\tif (args[0] instanceof MessagePort) {\n\t\ttransfers.push(args[0]);\n\t}\n\tinstance.port.postMessage({\n\t\tid, method, args\n\t} as MethodCallEventData, transfers);\n\treturn promise;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport type HookCallMessageCallback = (data: MethodCallEventData) => boolean;\n\n/** @internal */\nexport interface ReturnMessageInstance {\n\tport: MessagePort;\n}\n\n/** @internal */\nexport function initializeReturnPort(\n\tport: MessagePort,\n\tpromiseInitialized: Promise<void> | null,\n\ttargetObjectHolder: () => any,\n\thookMessage?: HookCallMessageCallback | undefined\n): ReturnMessageInstance {\n\tconst instance: ReturnMessageInstance = {\n\t\tport: port\n\t};\n\tif (promiseInitialized) {\n\t\tport.addEventListener('message', (e) => {\n\t\t\tconst data = e.data;\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpromiseInitialized.then(() => processCallMessage(instance.port, data, targetObjectHolder, hookMessage));\n\t\t});\n\t} else {\n\t\tport.addEventListener('message', (e) => {\n\t\t\tconst data = e.data;\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tprocessCallMessage(instance.port, data, targetObjectHolder, hookMessage);\n\t\t});\n\t}\n\tport.start();\n\treturn instance;\n}\n\nfunction processCallMessage(\n\tport: MessagePort,\n\tdata: MethodCallEventData,\n\ttargetObjectHolder: () => any,\n\thook?: HookCallMessageCallback | undefined\n) {\n\tif (hook && hook(data)) {\n\t\treturn;\n\t}\n\tconst target = targetObjectHolder();\n\tif (!target[data.method]) {\n\t\tpostReturnErrorImpl(port, data.id, data.method, new Error('Not implemented'));\n\t} else {\n\t\ttry {\n\t\t\tpostReturnImpl(port, data.id, data.method, target[data.method].apply(target, data.args));\n\t\t} catch (e) {\n\t\t\tpostReturnErrorImpl(port, data.id, data.method, e);\n\t\t}\n\t}\n}\n\n/** @internal */\nexport function postReturn(instance: ReturnMessageInstance, id: number, method: string, value: any) {\n\tpostReturnImpl(instance.port, id, method, value);\n}\n\nfunction postReturnImpl(port: MessagePort, id: number, method: string, value: any) {\n\tif (value instanceof Promise) {\n\t\tvalue.then((v) => {\n\t\t\tif (id >= 0) {\n\t\t\t\tport.postMessage({\n\t\t\t\t\tid,\n\t\t\t\t\tmethod,\n\t\t\t\t\tval: v\n\t\t\t\t} as MethodReturnEventData);\n\t\t\t}\n\t\t}, (error) => {\n\t\t\tport.postMessage({\n\t\t\t\tid,\n\t\t\t\tmethod,\n\t\t\t\terror: convertAnyErrorTransferable(error)\n\t\t\t} as MethodReturnEventData);\n\t\t});\n\t} else {\n\t\tport.postMessage({\n\t\t\tid,\n\t\t\tmethod,\n\t\t\tval: value\n\t\t} as MethodReturnEventData);\n\t}\n}\n\n/** @internal */\nexport function postReturnError(instance: ReturnMessageInstance, id: number, method: string, error: any) {\n\tpostReturnErrorImpl(instance.port, id, method, error);\n}\n\nfunction postReturnErrorImpl(port: MessagePort, id: number, method: string, error: any) {\n\tport.postMessage({\n\t\tid,\n\t\tmethod,\n\t\terror: convertAnyErrorTransferable(error)\n\t} as MethodReturnEventData);\n}\n", "\nimport Sequencer from './Sequencer';\nimport Synthesizer from './Synthesizer';\nimport SynthesizerSettings from './SynthesizerSettings';\nimport waitForReady from './waitForReady';\n\nimport {\n\tConstants,\n\tProcessorOptions,\n\tSynthesizerStatus\n} from './AudioWorkletNodeSynthesizer';\n\nimport {\n\tinitializeReturnPort,\n\tMethodCallEventData,\n\tpostReturn,\n\tpostReturnError,\n    ReturnMessageInstance\n} from './MethodMessaging';\nimport { disableLogging } from './logging';\n\nconst promiseWasmInitialized = waitForReady();\n\n/** Registers processor using Synthesizer for AudioWorklet. */\nexport default function registerAudioWorkletProcessor() {\n\t/**\n\t * The processor using Synthesizer\n\t */\n\tclass Processor extends AudioWorkletProcessor {\n\n\t\tprivate synth: Synthesizer | undefined;\n\t\tprivate _messaging: ReturnMessageInstance;\n\n\t\tconstructor(options: AudioWorkletNodeOptions) {\n\t\t\tsuper(options);\n\n\t\t\tconst processorOptions: ProcessorOptions | undefined = options.processorOptions;\n\t\t\tconst settings: SynthesizerSettings | undefined =\n\t\t\t\tprocessorOptions && processorOptions.settings;\n\t\t\tif (processorOptions && processorOptions.disabledLoggingLevel) {\n\t\t\t\tdisableLogging(processorOptions.disabledLoggingLevel);\n\t\t\t}\n\n\t\t\tconst promiseInitialized = this.doInit(settings);\n\t\t\tthis._messaging = initializeReturnPort(this.port, promiseInitialized, () => this.synth!, (data) => {\n\t\t\t\tswitch (data.method) {\n\t\t\t\t\tcase 'init':\n\t\t\t\t\t\tthis.synth!.init(sampleRate, settings);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\tcase 'createSequencer':\n\t\t\t\t\t\tthis.doCreateSequencer(data.args[0]).then(() => {\n\t\t\t\t\t\t\tpostReturn(this._messaging!, data.id, data.method, void (0));\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn true;\n\t\t\t\t\tcase 'hookPlayerMIDIEventsByName':\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tconst r = this.doHookPlayerMIDIEvents(data.args[0], data.args[1]);\n\t\t\t\t\t\t\tif (r) {\n\t\t\t\t\t\t\t\tpostReturn(this._messaging!, data.id, data.method, void (0));\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tpostReturnError(this._messaging!, data.id, data.method, new Error('Name not found'));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn true;\n\t\t\t\t\tcase 'callFunction':\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tthis.doCallFunction(data.args[0], data.args[1]);\n\t\t\t\t\t\t\tpostReturn(this._messaging!, data.id, data.method, void (0));\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tpostReturnError(this._messaging!, data.id, data.method, e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn true;\n\t\t\t\t\tcase 'getSFontObject':\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst name = this.doGetSFontObject(data.args[0], data.args[1]);\n\t\t\t\t\t\t\tif (name !== null) {\n\t\t\t\t\t\t\t\tpostReturn(this._messaging!, data.id, data.method, name);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tpostReturnError(this._messaging!, data.id, data.method, new Error('Invalid sfontId'));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tpostReturnError(this._messaging!, data.id, data.method, e);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn true;\n\t\t\t\t\tcase 'playPlayer':\n\t\t\t\t\t\tthis.doPlayPlayer(data);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\tcase 'loggingChanged':\n\t\t\t\t\t\tdisableLogging(data.args[0]);\n\t\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t});\n\t\t}\n\n\t\tprivate async doInit(settings?: SynthesizerSettings | undefined) {\n\t\t\tawait promiseWasmInitialized;\n\t\t\tthis.synth = new Synthesizer();\n\t\t\tthis.synth.init(sampleRate, settings);\n\t\t}\n\n\t\tprivate doCreateSequencer(port: MessagePort): Promise<void> {\n\t\t\treturn Synthesizer.createSequencer().then((seq) => {\n\t\t\t\tconst messaging = initializeReturnPort(port, null, () => seq, (data) => {\n\t\t\t\t\t// special handle for Sequencer\n\t\t\t\t\tif (data.method === 'getRaw') {\n\t\t\t\t\t\tpostReturn(messaging, data.id, data.method, (seq as Sequencer).getRaw());\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t} else if (data.method === 'registerSequencerClientByName') {\n\t\t\t\t\t\tconst r = this.doRegisterSequencerClient(seq as Sequencer, data.args[0], data.args[1], data.args[2]);\n\t\t\t\t\t\tif (r !== null) {\n\t\t\t\t\t\t\tpostReturn(messaging, data.id, data.method, r);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tpostReturnError(messaging, data.id, data.method, new Error('Name not found'));\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\treturn false;\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\n\t\tprivate doGetSFontObject(port: MessagePort, sfontId: number): string | null {\n\t\t\tconst sfont = this.synth!.getSFontObject(sfontId);\n\t\t\tif (sfont === null) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tconst messaging = initializeReturnPort(port, null, () => sfont, (data) => {\n\t\t\t\tif (data.method === 'getPresetIterable') {\n\t\t\t\t\tpostReturn(messaging, data.id, data.method, [...sfont.getPresetIterable()]);\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t});\n\t\t\treturn sfont.getName();\n\t\t}\n\n\t\tprivate doPlayPlayer(data: MethodCallEventData) {\n\t\t\tconst syn = this.synth!;\n\t\t\tsyn.playPlayer().then(() => {\n\t\t\t\tpostReturn(this._messaging, -1, Constants.UpdateStatus, {\n\t\t\t\t\tplaying: syn.isPlaying(),\n\t\t\t\t\tplayerPlaying: syn.isPlayerPlaying()\n\t\t\t\t} as SynthesizerStatus);\n\t\t\t\tpostReturn(this._messaging!, data.id, data.method, void (0));\n\t\t\t}, (e: unknown) => {\n\t\t\t\tpostReturnError(this._messaging!, data.id, data.method, e);\n\t\t\t})\n\t\t}\n\n\t\tprivate doHookPlayerMIDIEvents(name: string | null | undefined, param: any) {\n\t\t\tif (!name) {\n\t\t\t\tthis.synth!.hookPlayerMIDIEvents(null);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tconst fn: any = (AudioWorkletGlobalScope[name]);\n\t\t\tif (fn && typeof fn === 'function') {\n\t\t\t\tthis.synth!.hookPlayerMIDIEvents(fn, param);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tprivate doCallFunction(name: string, param: any) {\n\t\t\tconst fn: any = (AudioWorkletGlobalScope[name]);\n\t\t\tif (fn && typeof fn === 'function') {\n\t\t\t\tfn.call(null, this.synth, param);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthrow new Error('Name not found');\n\t\t}\n\n\t\tprivate doRegisterSequencerClient(seq: Sequencer, clientName: string, callbackName: string, param: number) {\n\t\t\tconst fn: any = (AudioWorkletGlobalScope[callbackName]);\n\t\t\tif (fn && typeof fn === 'function') {\n\t\t\t\treturn Synthesizer.registerSequencerClient(seq, clientName, fn, param);\n\t\t\t}\n\t\t\treturn null;\n\t\t}\n\n\t\tpublic process(_inputs: Float32Array[][], outputs: Float32Array[][]) {\n\t\t\tif (!this.synth) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tconst syn = this.synth!;\n\t\t\tsyn.render(outputs[0]);\n\t\t\tpostReturn(this._messaging, -1, Constants.UpdateStatus, {\n\t\t\t\tplaying: syn.isPlaying(),\n\t\t\t\tplayerPlaying: syn.isPlayerPlaying()\n\t\t\t} as SynthesizerStatus);\n\t\t\treturn true;\n\t\t}\n\t}\n\n\tregisterProcessor(Constants.ProcessorName, Processor);\n}\n", "import Synthesizer from './Synthesizer';\n\n/**\n * Returns the Promise object which resolves when the synthesizer engine is ready.\n */\nexport default function waitForReady(): Promise<void> {\n\treturn Synthesizer.waitForWasmInitialized();\n}\n", "\nimport registerAudioWorkletProcessor from './registerAudioWorkletProcessor';\n\nimport { rewriteEventData } from './ISequencerEventData';\nimport Synthesizer from './Synthesizer';\nimport { disableLogging, restoreLogging } from './logging';\n\nAudioWorkletGlobalScope.JSSynth = {\n\trewriteEventData: rewriteEventData,\n\tSynthesizer: Synthesizer,\n\tdisableLogging: disableLogging,\n\trestoreLogging: restoreLogging,\n};\n// deprecated\nAudioWorkletGlobalScope.Fluid = AudioWorkletGlobalScope.JSSynth;\n\nregisterAudioWorkletProcessor();\n"], "names": ["MIDIEvent", "constructor", "_ptr", "_module", "getType", "this", "_fluid_midi_event_get_type", "setType", "value", "_fluid_midi_event_set_type", "getChannel", "_fluid_midi_event_get_channel", "setChannel", "_fluid_midi_event_set_channel", "<PERSON><PERSON><PERSON>", "_fluid_midi_event_get_key", "<PERSON><PERSON><PERSON>", "_fluid_midi_event_set_key", "getVelocity", "_fluid_midi_event_get_velocity", "setVelocity", "_fluid_midi_event_set_velocity", "getControl", "_fluid_midi_event_get_control", "setControl", "_fluid_midi_event_set_control", "getValue", "_fluid_midi_event_get_value", "setValue", "_fluid_midi_event_set_value", "getProgram", "_fluid_midi_event_get_program", "setProgram", "_fluid_midi_event_set_program", "get<PERSON><PERSON>", "_fluid_midi_event_get_pitch", "<PERSON><PERSON><PERSON>", "_fluid_midi_event_set_pitch", "setSysEx", "data", "size", "byteLength", "ptr", "_malloc", "Uint8Array", "HEAPU8", "buffer", "set", "_fluid_midi_event_set_sysex", "setText", "_fluid_midi_event_set_text", "setLyrics", "_fluid_midi_event_set_lyrics", "SequencerEventData", "getRaw", "dispose", "_fluid_event_get_type", "getSource", "_fluid_event_get_source", "getDest", "_fluid_event_get_dest", "_fluid_event_get_channel", "_fluid_event_get_key", "_fluid_event_get_velocity", "_fluid_event_get_control", "_fluid_event_get_value", "_fluid_event_get_program", "getData", "_fluid_event_get_data", "getDuration", "_fluid_event_get_duration", "getBank", "_fluid_event_get_bank", "_fluid_event_get_pitch", "getSFontId", "_fluid_event_get_sfont_id", "AudioWorkletGlobalScope", "wasmModule", "<PERSON><PERSON><PERSON>", "rewriteEventDataImpl", "ev", "event", "type", "_fluid_event_note", "channel", "key", "vel", "duration", "_fluid_event_noteon", "_fluid_event_noteoff", "_fluid_event_all_sounds_off", "_fluid_event_all_notes_off", "_fluid_event_bank_select", "bank", "_fluid_event_program_change", "preset", "_fluid_event_program_select", "sfontId", "_fluid_event_control_change", "control", "_fluid_event_pitch_bend", "_fluid_event_pitch_wheelsens", "_fluid_event_modulation", "_fluid_event_sustain", "_fluid_event_pan", "_fluid_event_volume", "_fluid_event_reverb_send", "_fluid_event_chorus_send", "_fluid_event_key_pressure", "_fluid_event_channel_pressure", "_fluid_event_system_reset", "_fluid_event_timer", "_removeFunction", "fluid_sequencer_get_client_name", "fluid_sfont_get_name", "fluid_preset_get_name", "_addFunction", "_fs", "fluid_settings_setint", "fluid_settings_setnum", "fluid_settings_setstr", "fluid_synth_error", "fluid_synth_sfload", "fluid_sequencer_register_client", "malloc", "free", "defaultMIDIEventCallback", "promiseWaitForInitialized", "_ptrDefaultLogFunction", "makeEvent", "_new_fluid_event", "_delete_fluid_event", "Sequencer", "wasmRemoveFunction", "removeFunction", "cwrap", "_seq", "_seqId", "_clientFuncMap", "_initialize", "close", "_new_fluid_sequencer2", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "clientIdStr", "unregisterClient", "Number", "_delete_fluid_sequencer", "registerSynthesizer", "synth", "val", "_fluid_sequencer_unregister_client", "Synthesizer", "reject", "TypeError", "getRawSynthesizer", "_fluid_sequencer_register_fluidsynth", "clientId", "_fluid_event_set_source", "_fluid_event_set_dest", "_fluid_event_unregistering", "_fluid_sequencer_send_now", "map", "getAllRegisteredClients", "c", "_fluid_sequencer_count_clients", "r", "i", "id", "_fluid_sequencer_get_client_id", "name", "push", "getClientCount", "getClientInfo", "index", "setTimeScale", "scale", "_fluid_sequencer_set_time_scale", "getTimeScale", "_fluid_sequencer_get_time_scale", "getTick", "_fluid_sequencer_get_tick", "sendEventAt", "tick", "isAbsolute", "count", "_fluid_sequencer_send_at", "sendEventToClientAt", "sendEventToClientNow", "sendEventNow", "eventData", "removeAllEvents", "_fluid_sequencer_remove_events", "removeAllEventsFromClient", "processSequencer", "msecToProcess", "_fluid_sequencer_process", "setIntervalForSequencer", "msec", "setInterval", "Soundfont", "sfontPtr", "getSoundfontById", "sfont", "_fluid_synth_get_sfont_by_id", "getName", "getPreset", "presetNum", "presetPtr", "_fluid_sfont_get_preset", "soundfont", "bankNum", "_fluid_preset_get_banknum", "num", "_fluid_preset_get_num", "getPresetIterable", "reset", "_fluid_sfont_iteration_start", "next", "_fluid_sfont_iteration_next", "done", "undefined", "Symbol", "iterator", "wasmAddFunction", "Error", "addFunction", "FS", "bind", "_free", "_fluid_synth_handle_midi_event", "setBoolValueForSettings", "settings", "setIntValueForSettings", "setNumValueForSettings", "_settings", "_synth", "_player", "_playerPlaying", "_playerCallbackPtr", "_fluidSynthCallback", "_buffer", "_bufferSize", "_numPtr", "_gain", "waitForWasmInitialized", "mod", "addOnPostRunFn", "addOnPostRun", "calledRun", "fn", "onRuntimeInitialized", "waitForInitialized", "isInitialized", "createAudioNode", "context", "frameSize", "node", "createScriptProcessor", "addEventListener", "render", "outputBuffer", "init", "sampleRate", "_new_fluid_settings", "initialGain", "chorusActive", "chorusD<PERSON>h", "chorusLevel", "chorusNr", "chorusSpeed", "midiChannelCount", "setStrValueForSettings", "midiBankSelect", "minNote<PERSON>ength", "overflowAge", "overflowImportantValue", "overflowImportantChannels", "join", "overflowPercussion", "overflowReleased", "overflowSustained", "overflowVolume", "polyphony", "reverbActive", "reverbDamp", "reverbLevel", "reverbRoomSize", "reverbWidth", "_new_fluid_synth", "_closePlayer", "_delete_fluid_synth", "_delete_fluid_settings", "isPlaying", "actualCount", "_fluid_synth_get_active_voice_count", "baseOffsetOfVoice", "offsetOfActiveVoiceCount", "structActiveVoiceCount", "HEAPU32", "console", "warn", "voiceList", "voiceCount", "_fluid_synth_get_polyphony", "isRunning", "voice", "getActiveVoiceCount", "setInterpolation", "ensureInitialized", "_fluid_synth_set_interp_method", "get<PERSON>ain", "setGain", "gain", "_fluid_synth_set_gain", "_fluid_synth_get_gain", "setChannelType", "isDrum", "_fluid_synth_set_channel_type", "waitForVoicesStopped", "flushFramesAsync", "loadSFont", "bin", "ext", "Math", "random", "ub", "writeFile", "unlink", "unloadSFont", "stopPlayer", "flushFramesSync", "_fluid_synth_sfunload", "unloadSFontAsync", "then", "getSFontObject", "getSFontBankOffset", "_fluid_synth_get_bank_offset", "setSFontBankOffset", "offset", "_fluid_synth_set_bank_offset", "outBuffer", "frameCount", "length", "channels", "numberOfChannels", "sizePerChannel", "totalSize", "memLeft", "memRight", "renderRaw", "aLeft", "Float32Array", "aRight", "copyToChannel", "leftData", "getChannelData", "rightData", "isPlayerPlaying", "midiNoteOn", "chan", "_fluid_synth_noteon", "midiNoteOff", "_fluid_synth_noteoff", "midiKeyPressure", "_fluid_synth_key_pressure", "midiControl", "ctrl", "_fluid_synth_cc", "midiProgramChange", "prognum", "_fluid_synth_program_change", "midiChannelPressure", "_fluid_synth_channel_pressure", "midiPitchBend", "_fluid_synth_pitch_bend", "midiSysEx", "len", "mem", "_fluid_synth_sysex", "midiPitchWheelSensitivity", "_fluid_synth_pitch_wheel_sens", "_fluid_synth_bank_select", "midiSFontSelect", "_fluid_synth_sfont_select", "midiProgramSelect", "_fluid_synth_program_select", "midiUnsetProgram", "_fluid_synth_unset_program", "midiProgramReset", "_fluid_synth_program_reset", "midiSystemReset", "_fluid_synth_system_reset", "midiAllNotesOff", "_fluid_synth_all_notes_off", "midiAllSoundsOff", "_fluid_synth_all_sounds_off", "midiSetChannelType", "setReverb", "roomsize", "damping", "width", "level", "_fluid_synth_set_reverb", "setReverbRoomsize", "_fluid_synth_set_reverb_roomsize", "setReverbDamp", "_fluid_synth_set_reverb_damp", "setReverbWidth", "_fluid_synth_set_reverb_width", "setReverbLevel", "_fluid_synth_set_reverb_level", "setReverbOn", "on", "_fluid_synth_set_reverb_on", "getReverbRoomsize", "_fluid_synth_get_reverb_roomsize", "getReverbDamp", "_fluid_synth_get_reverb_damp", "getReverbLevel", "_fluid_synth_get_reverb_level", "getReverbWidth", "_fluid_synth_get_reverb_width", "<PERSON><PERSON><PERSON><PERSON>", "speed", "depthMillisec", "_fluid_synth_set_chorus", "setChorusVoiceCount", "_fluid_synth_set_chorus_nr", "setChorusLevel", "_fluid_synth_set_chorus_level", "setChorusSpeed", "_fluid_synth_set_chorus_speed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_fluid_synth_set_chorus_depth", "setChorusType", "_fluid_synth_set_chorus_type", "setChorusOn", "_fluid_synth_set_chorus_on", "getChorusVoiceCount", "_fluid_synth_get_chorus_nr", "getChorusLevel", "_fluid_synth_get_chorus_level", "getChorusSpeed", "_fluid_synth_get_chorus_speed", "getChorusDepth", "_fluid_synth_get_chorus_depth", "getChorusType", "_fluid_synth_get_chorus_type", "getGenerator", "param", "_fluid_synth_get_gen", "setGenerator", "_fluid_synth_set_gen", "getLegatoMode", "_fluid_synth_get_legato_mode", "HEAP32", "setLegatoMode", "mode", "_fluid_synth_set_legato_mode", "getPortamentoMode", "_fluid_synth_get_portamento_mode", "setPortamentoMode", "_fluid_synth_set_portamento_mode", "getBreathMode", "_fluid_synth_get_breath_mode", "setBreathMode", "flags", "_fluid_synth_set_breath_mode", "resetPlayer", "_initPlayer", "closePlayer", "player", "_new_fluid_player", "funcPtr", "p", "_delete_fluid_player", "_fluid_player_get_status", "addSMFDataToPlayer", "ensurePlayerInitialized", "_fluid_player_add_mem", "playPlayer", "_fluid_player_play", "resolver", "_player<PERSON><PERSON><PERSON>", "promise", "_fluid_player_stop", "_fluid_player_join", "retrievePlayerCurrentTick", "_fluid_player_get_current_tick", "retrievePlayerTotalTicks", "_fluid_player_get_total_ticks", "retrievePlayerBpm", "_fluid_player_get_bpm", "retrievePlayerMIDITempo", "_fluid_player_get_midi_tempo", "seekPlayer", "ticks", "_fluid_player_seek", "setPlayerLoop", "loopTimes", "_fluid_player_set_loop", "setPlayerTempo", "tempoType", "tempo", "_fluid_player_set_tempo", "hookPlayerMIDIEvents", "callback", "oldPtr", "newPtr", "cb", "t", "makeMIDIEventCallback", "_fluid_player_set_playback_callback", "_fluid_synth_write_float", "next<PERSON><PERSON><PERSON>", "setTimeout", "head", "tail", "self", "waitForPlayerStopped", "createSequencer", "seq", "registerSequencerClient", "time", "e", "convertAnyErrorTransferable", "err", "result", "objList", "obj", "prototype", "unshift", "getPrototypeOf", "o", "getOwnPropertyNames", "_e", "baseName", "message", "detail", "convertErrorTransferable", "initializeReturnPort", "port", "promiseInitialized", "targetObjectHolder", "hookMessage", "instance", "processCallMessage", "start", "hook", "target", "method", "postReturnImpl", "apply", "args", "postReturnErrorImpl", "postReturn", "v", "postMessage", "error", "postReturnError", "_disabledLoggingLevel", "_handlers", "LOG_LEVEL_COUNT", "LogLevel", "Panic", "Warning", "Info", "Debug", "disableLogging", "_fluid_set_log_function", "l", "promiseWasmInitialized", "JSSynth", "rewriteEventData", "restoreLogging", "Fluid", "Processor", "AudioWorkletProcessor", "options", "super", "processorOptions", "disabledLoggingLevel", "doInit", "_messaging", "doCreateSequencer", "doHookPlayerMIDIEvents", "doCallFunction", "doGetSFontObject", "doPlayPlayer", "messaging", "doRegisterSequencerClient", "syn", "playing", "playerPlaying", "call", "clientName", "callback<PERSON><PERSON>", "process", "_inputs", "outputs", "registerProcessor", "registerAudioWorkletProcessor"], "sourceRoot": ""}