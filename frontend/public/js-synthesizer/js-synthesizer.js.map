{"version": 3, "file": "js-synthesizer.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;UCVA;UACA;;;;;WCDA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+GA,kDAAkD;AAClD,MAAM,kBAAkB,GAAG;IAC1B,QAAQ,EAAE,CAAC;IACX,WAAW,EAAE,CAAC;IACd,YAAY,EAAE,CAAC;CACN,CAAC;AAGmB;;;AC9GvB,MAAM,eAAe,GAAoB,CAA2B,CAAC;;;ACZf;AAE7D,gBAAgB;AACD,MAAM,kBAAkB;IACtC,gBAAgB;IAChB,YAAoB,IAAiB,EAAU,OAAY;QAAvC,SAAI,GAAJ,IAAI,CAAa;QAAU,YAAO,GAAP,OAAO,CAAK;IAC3D,CAAC;IAED,gBAAgB;IACT,MAAM;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,gBAAgB;IACT,OAAO;QACb,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC7B,CAAC;IAEM,OAAO;QACb,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAqB,CAAC;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IACM,SAAS;QACf,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IACM,OAAO;QACb,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IACM,UAAU;QAChB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IACM,MAAM;QACZ,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IACM,WAAW;QACjB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IACM,UAAU;QAChB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IACM,QAAQ;QACd,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IACM,UAAU;QAChB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IACM,OAAO;QACb,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IACM,WAAW;QACjB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IACM,OAAO;QACb,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IACM,QAAQ;QACd,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IACM,UAAU;QAChB,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe;YAAE,OAAO,CAAC,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;CACD;;;AC1ED,gBAAgB;AAC6C;AAC7D,gBAAgB;AACsC;AAEtD,MAAM,OAAO,GAAQ,OAAO,uBAAuB,KAAK,WAAW,CAAC,CAAC;IACpE,uBAAuB,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AAuB7C,gBAAgB;AACT,SAAS,oBAAoB,CAAC,EAAe,EAAE,KAAqB;IAC1E,QAAQ,KAAK,CAAC,IAAI,EAAE;QACnB,kBAAoB;QACpB,KAAK,MAAM;YACV,OAAO,CAAC,iBAAiB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;YACnF,MAAM;QACP,oBAAsB;QACtB,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS;YACb,OAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;YACrE,MAAM;QACP,qBAAuB;QACvB,KAAK,SAAS,CAAC;QACf,KAAK,UAAU;YACd,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3D,MAAM;QACP,0BAA4B;QAC5B,KAAK,cAAc,CAAC;QACpB,KAAK,gBAAgB;YACpB,OAAO,CAAC,2BAA2B,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM;QACP,yBAA2B;QAC3B,KAAK,aAAa,CAAC;QACnB,KAAK,eAAe;YACnB,OAAO,CAAC,0BAA0B,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM;QACP,wBAA0B;QAC1B,KAAK,YAAY,CAAC;QAClB,KAAK,aAAa;YACjB,OAAO,CAAC,wBAAwB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM;QACP,2BAA6B;QAC7B,KAAK,eAAe,CAAC;QACrB,KAAK,gBAAgB;YACpB,OAAO,CAAC,2BAA2B,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM;QACP,2BAA6B;QAC7B,KAAK,eAAe,CAAC;QACrB,KAAK,gBAAgB;YACpB,OAAO,CAAC,2BAA2B,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAChG,MAAM;QACP,4BAA6B;QAC7B,KAAK,eAAe,CAAC;QACrB,KAAK,gBAAgB;YACpB,OAAO,CAAC,2BAA2B,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACnF,MAAM;QACP,uBAAyB;QACzB,KAAK,WAAW,CAAC;QACjB,KAAK,YAAY;YAChB,OAAO,CAAC,uBAAuB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM;QACP,mCAAqC;QACrC,KAAK,gBAAgB,CAAC;QACtB,KAAK,uBAAuB,CAAC;QAC7B,KAAK,kBAAkB,CAAC;QACxB,KAAK,yBAAyB;YAC7B,OAAO,CAAC,4BAA4B,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrE,MAAM;QACP,yBAA0B;QAC1B,KAAK,YAAY;YAChB,OAAO,CAAC,uBAAuB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChE,MAAM;QACP,sBAAuB;QACvB,KAAK,SAAS;YACb,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7D,MAAM;QACP,kBAAmB;QACnB,KAAK,KAAK;YACT,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM;QACP,qBAAsB;QACtB,KAAK,QAAQ;YACZ,OAAO,CAAC,mBAAmB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM;QACP,yBAA0B;QAC1B,KAAK,QAAQ,CAAC;QACd,KAAK,YAAY,CAAC;QAClB,KAAK,aAAa;YACjB,OAAO,CAAC,wBAAwB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM;QACP,yBAA0B;QAC1B,KAAK,QAAQ,CAAC;QACd,KAAK,YAAY,CAAC;QAClB,KAAK,aAAa;YACjB,OAAO,CAAC,wBAAwB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjE,MAAM;QACP,0BAA2B;QAC3B,KAAK,aAAa,CAAC;QACnB,KAAK,cAAc,CAAC;QACpB,KAAK,YAAY;YAChB,OAAO,CAAC,yBAAyB,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7E,MAAM;QACP,8BAA+B;QAC/B,KAAK,iBAAiB,CAAC;QACvB,KAAK,kBAAkB,CAAC;QACxB,KAAK,oBAAoB;YACxB,OAAO,CAAC,6BAA6B,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACtE,MAAM;QACP,0BAA2B;QAC3B,KAAK,aAAa,CAAC;QACnB,KAAK,cAAc;YAClB,OAAO,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM;QACP,oBAAqB;QACrB,KAAK,OAAO;YACX,OAAO,CAAC,kBAAkB,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM;QACP;YACC,sCAAsC;YACtC,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACb,CAAC;AAED;;;;;GAKG;AACI,SAAS,gBAAgB,CAAC,IAAyB,EAAE,KAAqB;IAChF,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,YAAY,kBAAkB,CAAC,EAAE;QACnD,OAAO,KAAK,CAAC;KACb;IACD,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IACzB,IAAI,EAAE,KAAK,eAAe,EAAE;QAC3B,OAAO,KAAK,CAAC;KACb;IACD,OAAO,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACxC,CAAC;;;ACjKD,mFAAmF;AACpE,MAAM,YAAa,SAAQ,KAAK;IAM9C,YAAY,QAAgB,EAAE,OAAe,EAAE,MAAY;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;YAC3B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;SAC1B;IACF,CAAC;CACD;;;;;;AETD,gBAAgB;AACD,MAAM,SAAS;IAE7B,gBAAgB;IAChB,YAAoB,IAAmB,EAAU,OAAY;QAAzC,SAAI,GAAJ,IAAI,CAAe;QAAU,YAAO,GAAP,OAAO,CAAK;IAC7D,CAAC;IAEM,OAAO;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IACM,OAAO,CAAC,KAAa;QAC3B,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IACM,UAAU;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IACM,UAAU,CAAC,KAAa;QAC9B,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IACM,MAAM;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IACM,MAAM,CAAC,KAAa;QAC1B,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IACM,WAAW;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D,CAAC;IACM,WAAW,CAAC,KAAa;QAC/B,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;IACM,UAAU;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IACM,UAAU,CAAC,KAAa;QAC9B,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IACM,QAAQ;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IACM,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IACM,UAAU;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IACM,UAAU,CAAC,KAAa;QAC9B,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IACM,QAAQ;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IACM,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAEM,QAAQ,CAAC,IAAgB;QAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,MAAM,GAAG,GAAgB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACnE,CAAC;IACM,OAAO,CAAC,IAAgB;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,MAAM,GAAG,GAAgB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IACM,SAAS,CAAC,IAAgB;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,MAAM,GAAG,GAAgB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpE,CAAC;CACD;;;AClFiF;AAEF;AAE1B;AAEd;AAWxC,IAAI,gBAAY,CAAC;AACjB,IAAI,eAA0C,CAAC;AAE/C,IAAI,+BAAoE,CAAC;AAEzE,SAAS,aAAa;IACrB,IAAI,gBAAO,EAAE;QACZ,OAAO;KACP;IAED,IAAI,OAAO,uBAAuB,KAAK,WAAW,EAAE;QACnD,gBAAO,GAAG,uBAAuB,CAAC,UAAU,CAAC;QAC7C,eAAe,GAAG,uBAAuB,CAAC,kBAAkB,CAAC;KAC7D;SAAM;QACN,gBAAO,GAAG,MAAM,CAAC;QACjB,eAAe,GAAG,cAAc,CAAC;KACjC;IAED,+BAA+B;QAC9B,gBAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,SAAS,CAAC,KAAqB;IACvC,MAAM,EAAE,GAAG,gBAAO,CAAC,gBAAgB,EAAE,CAAC;IACtC,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;QACrC,gBAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC;KACZ;IACD,OAAO,EAAE,CAAC;AACX,CAAC;AAED,gBAAgB;AACD,MAAM,SAAS;IAQ7B;QACC,aAAa,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACT,WAAW;QACjB,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,gBAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACT,MAAM;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAEM,KAAK;QACX,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;gBACxD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,gBAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;SAC5B;IACF,CAAC;IAEM,mBAAmB,CAAC,KAA4B;QACtD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;YACvB,gBAAO,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACnE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACjB;QACD,IAAI,GAAW,CAAC;QAChB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC9B,GAAG,GAAG,KAAK,CAAC;SACZ;aAAM,IAAI,KAAK,YAAY,WAAW,EAAE;YACxC,GAAG,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;SAChC;aAAM;YACN,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAC,CAAC;SACpF;QAED,IAAI,CAAC,MAAM,GAAG,gBAAO,CAAC,oCAAoC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC3E,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAEM,gBAAgB,CAAC,QAAgB;QACvC,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;YACpB,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YACvB,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;gBACpB,OAAO;aACP;SACD;QAED,6BAA6B;QAC7B,MAAM,EAAE,GAAG,gBAAO,CAAC,gBAAgB,EAAE,CAAC;QACtC,gBAAO,CAAC,uBAAuB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACxC,gBAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC5C,gBAAO,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;QACvC,gBAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACjD,gBAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAEhC,gBAAO,CAAC,kCAAkC,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACjB;aAAM;YACN,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;YAChC,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAClB,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC/B,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC;aACrB;SACD;IACF,CAAC;IAEM,uBAAuB;QAC7B,MAAM,CAAC,GAAG,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,CAAC,GAAiB,EAAE,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;YAC3B,MAAM,EAAE,GAAG,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAChE,MAAM,IAAI,GAAG,+BAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC5D,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;SACrC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEM,cAAc;QACpB,OAAO,OAAO,CAAC,OAAO,CAAS,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACnF,CAAC;IAEM,aAAa,CAAC,KAAa;QACjC,MAAM,EAAE,GAAG,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACpE,MAAM,IAAI,GAAG,+BAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC5D,OAAO,OAAO,CAAC,OAAO,CAAa,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAClE,CAAC;IAEM,YAAY,CAAC,KAAa;QAChC,gBAAO,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAEM,YAAY;QAClB,OAAO,OAAO,CAAC,OAAO,CAAC,gBAAO,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IAEM,OAAO;QACb,OAAO,OAAO,CAAC,OAAO,CAAC,gBAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;IAEM,WAAW,CAAC,KAAqB,EAAE,IAAY,EAAE,UAAmB;QAC1E,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,EAAE,KAAK,IAAI,EAAE;YAChB,sBAAsB;YACtB,MAAM,KAAK,GAAG,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;gBAC/B,MAAM,EAAE,GAAW,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBACxE,gBAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;gBACtC,gBAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC1E;YACD,gBAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;SAChC;IACF,CAAC;IAEM,mBAAmB,CAAC,QAAgB,EAAE,KAAqB,EAAE,IAAY,EAAE,UAAmB;QACpG,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,EAAE,KAAK,IAAI,EAAE;YAChB,gBAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC5E,gBAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,gBAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;SAChC;IACF,CAAC;IAED,gBAAgB;IACT,oBAAoB,CAAC,QAAgB,EAAE,KAAqB;QAClE,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,EAAE,KAAK,IAAI,EAAE;YAChB,gBAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC5E,gBAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACjD,gBAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;SAChC;IACF,CAAC;IAED,gBAAgB;IACT,YAAY,CAAC,QAAgB,EAAE,SAA8B;QACnE,IAAI,CAAC,CAAC,SAAS,YAAY,kBAAkB,CAAC,EAAE;YAC/C,OAAO;SACP;QACD,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9B,IAAI,EAAE,KAAK,eAAe,EAAE;YAC3B,gBAAO,CAAC,qBAAqB,CAAC,EAAE,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC5E,gBAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;SACjD;IACF,CAAC;IAEM,eAAe;QACrB,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEM,yBAAyB,CAAC,QAAgB;QAChD,gBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;IACrG,CAAC;IAEM,gBAAgB,CAAC,aAAqB;QAC5C,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE;YAClC,gBAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;SAC3D;IACF,CAAC;IAED,gBAAgB;IACT,uBAAuB,CAAC,IAAY;QAC1C,OAAO,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAC7D,CAAC;CACD;;;ACzOkE;AAYnE,IAAI,gBAAY,CAAC;AAEjB,IAAI,oBAAqD,CAAC;AAC1D,IAAI,qBAAwD,CAAC;AAE7D,SAAS,uBAAa;IACrB,IAAI,gBAAO,EAAE;QACZ,OAAO;KACP;IAED,IAAI,OAAO,uBAAuB,KAAK,WAAW,EAAE;QACnD,gBAAO,GAAG,uBAAuB,CAAC,UAAU,CAAC;KAC7C;SAAM;QACN,gBAAO,GAAG,MAAM,CAAC;KACjB;IAED,oBAAoB;QACnB,gBAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7D,qBAAqB;QACpB,gBAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/D,CAAC;AAEc,MAAM,SAAS;IAG7B,YAAY;IACZ,YAAmB,QAAsB;QACxC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;IACtB,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,KAAkB,EAAE,EAAU;QAC5D,uBAAa,EAAE,CAAC;QAEhB,MAAM,KAAK,GAAG,gBAAO,CAAC,4BAA4B,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,EAAE,CAAC,CAAC;QAClF,IAAI,KAAK,KAAK,eAAe,EAAE;YAC9B,OAAO,IAAI,CAAC;SACZ;QACD,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAEM,OAAO;QACb,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAEM,SAAS,CAAC,IAAY,EAAE,SAAiB;QAC/C,MAAM,SAAS,GAAkB,gBAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7F,IAAI,SAAS,KAAK,eAAe,EAAE;YAClC,OAAO,IAAI,CAAC;SACZ;QACD,MAAM,IAAI,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,gBAAO,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QAC7D,MAAM,GAAG,GAAG,gBAAO,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QACrD,OAAO;YACN,SAAS,EAAE,IAAI;YACf,IAAI;YACJ,OAAO;YACP,GAAG;SACH,CAAC;IACH,CAAC;IAEM,iBAAiB;QACvB,MAAM,KAAK,GAAG,GAAG,EAAE;YAClB,gBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC;QACF,MAAM,IAAI,GAAG,GAAiC,EAAE;YAC/C,MAAM,SAAS,GAAG,gBAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjE,IAAI,SAAS,KAAK,CAAC,EAAE;gBACpB,OAAO;oBACN,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,SAAS;iBAChB,CAAC;aACF;iBAAM;gBACN,MAAM,IAAI,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBAC9C,MAAM,OAAO,GAAG,gBAAO,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;gBAC7D,MAAM,GAAG,GAAG,gBAAO,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBACrD,OAAO;oBACN,IAAI,EAAE,KAAK;oBACX,KAAK,EAAE;wBACN,SAAS,EAAE,IAAI;wBACf,IAAI;wBACJ,OAAO;wBACP,GAAG;qBACH;iBACD,CAAC;aACF;QACF,CAAC,CAAC;QACF,MAAM,QAAQ,GAAG,GAAqB,EAAE;YACvC,KAAK,EAAE,CAAC;YACR,OAAO;gBACN,IAAI;aACJ,CAAC;QACH,CAAC,CAAC;QACF,OAAO;YACN,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,QAAQ;SAC3B,CAAC;IACH,CAAC;CACD;;;AC9F+E;AAGzB;AACnB;AAEkB;AAClB;AAcpC,IAAI,kBAAY,CAAC;AACjB,IAAI,YAAqD,CAAC;AAC1D,IAAI,0BAA0C,CAAC;AAC/C,IAAI,GAAQ,CAAC;AAEb,6BAA6B;AAC7B,IAAI,qBAAkF,CAAC;AACvF,IAAI,qBAAkF,CAAC;AACvF,IAAI,qBAAkF,CAAC;AACvF,IAAI,iBAA2D,CAAC;AAChE,IAAI,kBAAuF,CAAC;AAC5F,IAAI,+BAA2G,CAAC;AAEhH,IAAI,MAAqC,CAAC;AAC1C,IAAI,IAAgC,CAAC;AAErC,IAAI,wBAA6E,CAAC;AAElF,SAAS,yBAAa;IACrB,IAAI,iBAAiB,EAAE;QACtB,kBAAkB;QAClB,OAAO;KACP;IAED,IAAI,OAAO,uBAAuB,KAAK,WAAW,EAAE;QACnD,kBAAO,GAAG,uBAAuB,CAAC,UAAU,CAAC;QAC7C,YAAY,GAAG,uBAAuB,CAAC,eAAe,CAAC;QACvD,0BAAe,GAAG,uBAAuB,CAAC,kBAAkB,CAAC;KAC7D;SAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACzC,kBAAO,GAAG,MAAM,CAAC;QACjB,YAAY,GAAG,WAAW,CAAC;QAC3B,0BAAe,GAAG,cAAc,CAAC;KACjC;SAAM;QACN,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;KACpF;IACD,GAAG,GAAG,kBAAO,CAAC,EAAE,CAAC;IAEjB,6BAA6B;IAC7B,qBAAqB;QACpB,kBAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAClF,qBAAqB;QACpB,kBAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAClF,qBAAqB;QACpB,kBAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAClF,iBAAiB;QAChB,kBAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC1D,kBAAkB;QACjB,kBAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC/E,+BAA+B;QAC9B,kBAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEtG,MAAM,GAAG,kBAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAO,CAAC,CAAC;IACvC,IAAI,GAAG,kBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAO,CAAC,CAAC;IAEnC,wBAAwB,GAAG,kBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,kBAAO,CAAC,CAAC;AACjF,CAAC;AAED,IAAI,yBAAoD,CAAC;AACzD,SAAS,kBAAkB;IAC1B,IAAI,yBAAyB,EAAE;QAC9B,OAAO,yBAAyB,CAAC;KACjC;IAED,IAAI,GAAQ,CAAC;IACb,IAAI,cAAiE,CAAC;IACtE,IAAI,OAAO,uBAAuB,KAAK,WAAW,EAAE;QACnD,GAAG,GAAG,uBAAuB,CAAC,UAAU,CAAC;QACzC,cAAc,GAAG,uBAAuB,CAAC,YAAY,CAAC;KACtD;SAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACzC,GAAG,GAAG,MAAM,CAAC;QACb,cAAc,GAAG,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC;KAChF;SAAM;QACN,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC,CAAC;KACrG;IACD,IAAI,GAAG,CAAC,SAAS,EAAE;QAClB,yBAAyB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAC9C,OAAO,yBAAyB,CAAC;KACjC;IACD,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;QAC1C,yBAAyB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACnD,MAAM,EAAE,GAA6B,kBAAO,CAAC,oBAAoB,CAAC;YAClE,kBAAO,CAAC,oBAAoB,GAAG,GAAG,EAAE;gBACnC,OAAO,EAAE,CAAC;gBACV,IAAI,EAAE,EAAE;oBACP,EAAE,EAAE,CAAC;iBACL;YACF,CAAC,CAAC;QACH,CAAC,CAAC,CAAC;KACH;SAAM;QACN,yBAAyB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACnD,cAAe,CAAC,OAAO,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;KACH;IACD,OAAO,yBAAyB,CAAC;AAClC,CAAC;AAED,SAAS,uBAAuB,CAAC,QAAoB,EAAE,IAAY,EAAE,KAA0B;IAC9F,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QACjC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACrD;AACF,CAAC;AACD,SAAS,sBAAsB,CAAC,QAAoB,EAAE,IAAY,EAAE,KAAyB;IAC5F,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QACjC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC7C;AACF,CAAC;AACD,SAAS,sBAAsB,CAAC,QAAoB,EAAE,IAAY,EAAE,KAAyB;IAC5F,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QACjC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC7C;AACF,CAAC;AACD,SAAS,sBAAsB,CAAC,QAAoB,EAAE,IAAY,EAAE,KAAyB;IAC5F,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;QACjC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC7C;AACF,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAc;IAC1C,MAAM,WAAW,GAAG,kBAAO,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;IACvE,IAAI,CAAC,WAAW,EAAE;QACjB,OAAO,CAAC,CAAC;KACT;IAED,gEAAgE;IAChE,wCAAwC;IAExC,wCAAwC;IACxC,8BAA8B;IAC9B,kCAAkC;IAClC,oDAAoD;IACpD,wBAAwB;IACxB,kCAAkC;IAClC,+CAA+C;IAC/C,8BAA8B;IAC9B,IAAI,iBAAiB,GAAG,GAAG,CAAC;IAC5B,IAAI,wBAAwB,GAAG,CAAC,KAAK,GAAG,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACpE,IAAI,sBAAsB,GAAG,kBAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;IACvE,IAAI,sBAAsB,KAAK,WAAW,EAAE;QAC3C,kBAAkB;QAClB,iBAAiB,IAAI,CAAC,CAAC;QACvB,wBAAwB,GAAG,CAAC,KAAK,GAAG,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QAChE,sBAAsB,GAAG,kBAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACnE,IAAI,sBAAsB,KAAK,WAAW,EAAE;YAC3C,oBAAoB;YACpB,MAAM,CAAC,GAAG,OAAO,CAAC;YAClB,CAAC,CAAC,IAAI,CACL,yEAAyE,CACzE,CAAC;YACF,OAAO,WAAW,CAAC;SACnB;KACD;IAED,MAAM,SAAS,GAAG,kBAAO,CAAC,OAAO,CAAC,CAAC,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,6BAA6B;IAC7B,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,kBAAO,CAAC,OAAO,CAAC,UAAU,EAAE;QAC1D,oBAAoB;QACpB,MAAM,CAAC,GAAG,OAAO,CAAC;QAClB,CAAC,CAAC,IAAI,CACL,yEAAyE,CACzE,CAAC;QACF,OAAO,WAAW,CAAC;KACnB;IAED,gEAAgE;IAChE,MAAM,UAAU,GAAG,kBAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;IAC7D,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE;QACpC,4BAA4B;QAC5B,MAAM,KAAK,GAAG,kBAAO,CAAC,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,EAAE;YACX,SAAS;SACT;QACD,yBAAyB;QACzB,MAAM,MAAM,GAAG,kBAAO,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACzC,qBAAqB;QACrB,IAAI,MAAM,KAAK,CAAC,EAAE;YACjB,SAAS,GAAG,IAAI,CAAC;YACjB,MAAM;SACN;KACD;IACD,IAAI,CAAC,SAAS,EAAE;QACf,IAAI,sBAAsB,KAAK,CAAC,EAAE;YACjC,MAAM,CAAC,GAAG,OAAO,CAAC;YAClB,CAAC,CAAC,IAAI,CACL,yEAAyE,EACzE,sBAAsB,CACtB,CAAC;SACF;QACD,kBAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QAC9C,OAAO,CAAC,CAAC;KACT;IAED,OAAO,WAAW,CAAC;AACpB,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAY,EAAE,GAAW;IACpD,OAAO,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,GAAG,GAAG,EAAE,CAAC;AAC5E,CAAC;AA4BD,SAAS,qBAAqB,CAAC,KAAkB,EAAE,EAAyB,EAAE,KAAU;IACvF,OAAO,CAAC,IAAiB,EAAE,KAAoB,EAAU,EAAE;QAC1D,MAAM,CAAC,GAAG,kBAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE,kBAAO,CAAC,EAAE,KAAK,CAAC,EAAE;YACvD,OAAO,CAAC,CAAC;SACT;QACD,OAAO,kBAAO,CAAC,8BAA8B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC,CAAC;AACH,CAAC;AAED,6CAA6C;AAC9B,MAAM,WAAW;IA+B/B;QACC,yBAAa,EAAE,CAAC;QAEhB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC;QAC9B,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC;QAE/B,IAAI,CAAC,KAAK,iBAAgC,CAAC;IAC5C,CAAC;IAED,oFAAoF;IAC7E,MAAM,CAAC,sBAAsB;QACnC,OAAO,kBAAkB,EAAE,CAAC;IAC7B,CAAC;IAEM,aAAa;QACnB,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC;IACxC,CAAC;IAED,6EAA6E;IACtE,iBAAiB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAEM,eAAe,CACrB,OAAqB,EACrB,SAAkB;QAElB,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,EAAE;YAC5C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,IAAI,CAAC,UAAkB,EAAE,QAA8B;QAC7D,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,kBAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAC7D,qBAAqB,CAAC,GAAG,EAAE,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAC5D,IAAI,QAAQ,EAAE;YACb,IAAI,OAAO,QAAQ,CAAC,WAAW,KAAK,WAAW,EAAE;gBAChD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC;aAClC;YACD,uBAAuB,CACtB,GAAG,EACH,qBAAqB,EACrB,QAAQ,CAAC,YAAY,CACrB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,oBAAoB,EACpB,QAAQ,CAAC,WAAW,CACpB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,oBAAoB,EACpB,QAAQ,CAAC,WAAW,CACpB,CAAC;YACF,sBAAsB,CAAC,GAAG,EAAE,iBAAiB,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAClE,sBAAsB,CACrB,GAAG,EACH,oBAAoB,EACpB,QAAQ,CAAC,WAAW,CACpB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,qBAAqB,EACrB,QAAQ,CAAC,gBAAgB,CACzB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,wBAAwB,EACxB,QAAQ,CAAC,cAAc,CACvB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,uBAAuB,EACvB,QAAQ,CAAC,aAAa,CACtB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,oBAAoB,EACpB,QAAQ,CAAC,WAAW,CACpB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,0BAA0B,EAC1B,QAAQ,CAAC,sBAAsB,CAC/B,CAAC;YACF,IAAI,OAAO,QAAQ,CAAC,yBAAyB,KAAK,WAAW,EAAE;gBAC9D,qBAAqB,CACpB,GAAG,EACH,mCAAmC,EACnC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,CAC5C,CAAC;aACF;YACD,sBAAsB,CACrB,GAAG,EACH,2BAA2B,EAC3B,QAAQ,CAAC,kBAAkB,CAC3B,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,yBAAyB,EACzB,QAAQ,CAAC,gBAAgB,CACzB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,0BAA0B,EAC1B,QAAQ,CAAC,iBAAiB,CAC1B,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,uBAAuB,EACvB,QAAQ,CAAC,cAAc,CACvB,CAAC;YACF,sBAAsB,CAAC,GAAG,EAAE,iBAAiB,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;YACnE,uBAAuB,CACtB,GAAG,EACH,qBAAqB,EACrB,QAAQ,CAAC,YAAY,CACrB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,mBAAmB,EACnB,QAAQ,CAAC,UAAU,CACnB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,oBAAoB,EACpB,QAAQ,CAAC,WAAW,CACpB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,wBAAwB,EACxB,QAAQ,CAAC,cAAc,CACvB,CAAC;YACF,sBAAsB,CACrB,GAAG,EACH,oBAAoB,EACpB,QAAQ,CAAC,WAAW,CACpB,CAAC;SACF;QACD,qBAAqB,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAErD,IAAI,CAAC,MAAM,GAAG,kBAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IAEM,KAAK;QACX,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,EAAE;YACpC,OAAO;SACP;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,kBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC;QAC9B,kBAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC;IAChC,CAAC;IAEM,SAAS;QACf,OAAO,CACN,IAAI,CAAC,MAAM,KAAK,eAAe;YAC/B,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CACpC,CAAC;IACH,CAAC;IAEM,gBAAgB,CAAC,KAA0B,EAAE,OAAgB;QACnE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YACnC,OAAO,GAAG,CAAC,CAAC,CAAC;SACb;QACD,kBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAEM,OAAO;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAEM,OAAO,CAAC,IAAY;QAC1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,kBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,GAAG,kBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IAEM,cAAc,CAAC,OAAe,EAAE,MAAe;QACrD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,kDAAkD;QAClD,kBAAO,CAAC,6BAA6B,CACpC,IAAI,CAAC,MAAM,EACX,OAAO,EACP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACd,CAAC;IACH,CAAC;IAEM,oBAAoB;QAC1B,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,CAAC;IAEM,SAAS,CAAC,GAAgB;QAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;QAE/B,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACxB,MAAM,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACvD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjB,OAAO,KAAK,KAAK,CAAC,CAAC;YAClB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5D,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAEM,WAAW,CAAC,EAAU;QAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,kBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAEM,gBAAgB,CAAC,EAAU;QACjC,gCAAgC;QAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YACxC,kBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,OAAe;QACpC,OAAO,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAEM,kBAAkB,CAAC,EAAU;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,OAAO,OAAO,CAAC,OAAO,CACrB,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAW,CAC/D,CAAC;IACH,CAAC;IACM,kBAAkB,CAAC,EAAU,EAAE,MAAc;QACnD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;IAC/D,CAAC;IAEM,MAAM,CAAC,SAAuC;QACpD,MAAM,UAAU,GACf,kBAAkB,IAAI,SAAS;YAC9B,CAAC,CAAC,SAAS,CAAC,MAAM;YAClB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACxB,MAAM,QAAQ,GACb,kBAAkB,IAAI,SAAS;YAC9B,CAAC,CAAC,SAAS,CAAC,gBAAgB;YAC5B,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;QACrB,MAAM,cAAc,GAAG,CAAC,GAAG,UAAU,CAAC;QACtC,MAAM,SAAS,GAAG,cAAc,GAAG,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,WAAW,GAAG,SAAS,EAAE;YACjC,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,EAAE;gBACrC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACnB;YACD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;SAC7B;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,QAAQ,GAAG,CAAE,IAAI,CAAC,OAAkB;YACzC,cAAc,CAAgB,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAE9C,MAAM,KAAK,GAAG,IAAI,YAAY,CAC7B,kBAAO,CAAC,MAAM,CAAC,MAAM,EACrB,OAAO,EACP,UAAU,CACV,CAAC;QACF,MAAM,MAAM,GACX,QAAQ,IAAI,CAAC;YACZ,CAAC,CAAC,IAAI,YAAY,CAAC,kBAAO,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC;YAC/D,CAAC,CAAC,IAAI,CAAC;QACT,IAAI,kBAAkB,IAAI,SAAS,EAAE;YACpC,IAAI,SAAS,CAAC,aAAa,EAAE;gBAC5B,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrC,IAAI,MAAM,EAAE;oBACX,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;iBACtC;aACD;iBAAM;gBACN,oDAAoD;gBACpD,MAAM,QAAQ,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC7C,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAC/C,IAAI,MAAM,EAAE;oBACX,MAAM,SAAS,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;iBACjD;aACD;SACD;aAAM;YACN,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACxB,IAAI,MAAM,EAAE;gBACX,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aACzB;SACD;QAED,iCAAiC;QACjC,IAAI,CAAC,eAAe,EAAE,CAAC;IACxB,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,GAAW,EAAE,GAAW;QACvD,kBAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC1D,CAAC;IACM,WAAW,CAAC,IAAY,EAAE,GAAW;QAC3C,kBAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACtD,CAAC;IACM,eAAe,CAAC,IAAY,EAAE,GAAW,EAAE,GAAW;QAC5D,kBAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAChE,CAAC;IACM,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,GAAW;QACzD,kBAAO,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;IACM,iBAAiB,CAAC,IAAY,EAAE,OAAe;QACrD,kBAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACjE,CAAC;IACM,mBAAmB,CAAC,IAAY,EAAE,GAAW;QACnD,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAC/D,CAAC;IACM,aAAa,CAAC,IAAY,EAAE,GAAW;QAC7C,kBAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACzD,CAAC;IACM,SAAS,CAAC,IAAgB;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACxB,kBAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9B,kBAAO,CAAC,kBAAkB,CACzB,IAAI,CAAC,MAAM,EACX,GAAG,EACH,GAAG,EACH,eAAe,EACf,eAAe,EACf,eAAe,EACf,CAAC,CACD,CAAC;QACF,IAAI,CAAC,GAAG,CAAC,CAAC;IACX,CAAC;IAEM,yBAAyB,CAAC,IAAY,EAAE,GAAW;QACzD,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAC/D,CAAC;IACM,cAAc,CAAC,IAAY,EAAE,IAAY;QAC/C,kBAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IACM,eAAe,CAAC,IAAY,EAAE,OAAe;QACnD,kBAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IACM,iBAAiB,CACvB,IAAY,EACZ,OAAe,EACf,IAAY,EACZ,SAAiB;QAEjB,kBAAO,CAAC,2BAA2B,CAClC,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,CACT,CAAC;IACH,CAAC;IACM,gBAAgB,CAAC,IAAY;QACnC,kBAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IACM,gBAAgB;QACtB,kBAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IACM,eAAe;QACrB,kBAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChD,CAAC;IACM,eAAe,CAAC,IAAa;QACnC,kBAAO,CAAC,0BAA0B,CACjC,IAAI,CAAC,MAAM,EACX,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CACvC,CAAC;IACH,CAAC;IACM,gBAAgB,CAAC,IAAa;QACpC,kBAAO,CAAC,2BAA2B,CAClC,IAAI,CAAC,MAAM,EACX,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CACvC,CAAC;IACH,CAAC;IACM,kBAAkB,CAAC,IAAY,EAAE,MAAe;QACtD,2BAA2B;QAC3B,wBAAwB;QACxB,kBAAO,CAAC,6BAA6B,CACpC,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,SAAS,CACf,QAAgB,EAChB,OAAe,EACf,KAAa,EACb,KAAa;QAEb,kBAAO,CAAC,uBAAuB,CAC9B,IAAI,CAAC,MAAM,EACX,QAAQ,EACR,OAAO,EACP,KAAK,EACL,KAAK,CACL,CAAC;IACH,CAAC;IACD;;OAEG;IACI,iBAAiB,CAAC,QAAgB;QACxC,kBAAO,CAAC,gCAAgC,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACjE,CAAC;IACD;;OAEG;IACI,aAAa,CAAC,OAAe;QACnC,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IACD;;OAEG;IACI,cAAc,CAAC,KAAa;QAClC,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,cAAc,CAAC,KAAa;QAClC,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,WAAW,CAAC,EAAW;QAC7B,kBAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IACD;;OAEG;IACI,iBAAiB;QACvB,OAAO,kBAAO,CAAC,gCAAgC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD;;OAEG;IACI,aAAa;QACnB,OAAO,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IACD;;OAEG;IACI,cAAc;QACpB,OAAO,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,cAAc;QACpB,OAAO,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,SAAS,CACf,UAAkB,EAClB,KAAa,EACb,KAAa,EACb,aAAqB,EACrB,IAAsB;QAEtB,kBAAO,CAAC,uBAAuB,CAC9B,IAAI,CAAC,MAAM,EACX,UAAU,EACV,KAAK,EACL,KAAK,EACL,aAAa,EACb,IAAI,CACJ,CAAC;IACH,CAAC;IACD;;OAEG;IACI,mBAAmB,CAAC,UAAkB;QAC5C,kBAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAC7D,CAAC;IACD;;OAEG;IACI,cAAc,CAAC,KAAa;QAClC,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,cAAc,CAAC,KAAa;QAClC,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,cAAc,CAAC,aAAqB;QAC1C,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IACnE,CAAC;IACD;;OAEG;IACI,aAAa,CAAC,IAAsB;QAC1C,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC;IACD;;OAEG;IACI,WAAW,CAAC,EAAW;QAC7B,kBAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IACD;;OAEG;IACI,mBAAmB;QACzB,OAAO,kBAAO,CAAC,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IACD;;OAEG;IACI,cAAc;QACpB,OAAO,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,cAAc;QACpB,OAAO,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,cAAc;QACpB,OAAO,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IACD;;OAEG;IACI,aAAa;QACnB,OAAO,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,OAAe,EAAE,KAAqB;QACzD,OAAO,kBAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IACD;;;;;OAKG;IACI,YAAY,CAAC,OAAe,EAAE,KAAqB,EAAE,KAAa;QACxE,kBAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IACD;;;;OAIG;IACI,aAAa,CAAC,OAAe;QACnC,kBAAO,CAAC,4BAA4B,CACnC,IAAI,CAAC,MAAM,EACX,OAAO,EACP,IAAI,CAAC,OAAO,CACZ,CAAC;QACF,OAAO,kBAAO,CAAC,MAAM,CAAE,IAAI,CAAC,OAAkB,IAAI,CAAC,CAAe,CAAC;IACpE,CAAC;IACD;;;;OAIG;IACI,aAAa,CAAC,OAAe,EAAE,IAAgB;QACrD,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAClE,CAAC;IACD;;;;OAIG;IACI,iBAAiB,CAAC,OAAe;QACvC,kBAAO,CAAC,gCAAgC,CACvC,IAAI,CAAC,MAAM,EACX,OAAO,EACP,IAAI,CAAC,OAAO,CACZ,CAAC;QACF,OAAO,kBAAO,CAAC,MAAM,CAAE,IAAI,CAAC,OAAkB,IAAI,CAAC,CAAmB,CAAC;IACxE,CAAC;IACD;;;;OAIG;IACI,iBAAiB,CAAC,OAAe,EAAE,IAAoB;QAC7D,kBAAO,CAAC,gCAAgC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;IACD;;;;OAIG;IACI,aAAa,CAAC,OAAe;QACnC,kBAAO,CAAC,4BAA4B,CACnC,IAAI,CAAC,MAAM,EACX,OAAO,EACP,IAAI,CAAC,OAAO,CACZ,CAAC;QACF,OAAO,kBAAO,CAAC,MAAM,CAAE,IAAI,CAAC,OAAkB,IAAI,CAAC,CAAW,CAAC;IAChE,CAAC;IACD;;;;OAIG;IACI,aAAa,CAAC,OAAe,EAAE,KAAa;QAClD,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,4EAA4E;IAErE,WAAW;QACjB,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YACpC,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACJ,CAAC;IAEM,WAAW;QACjB,IAAI,CAAC,YAAY,EAAE,CAAC;IACrB,CAAC;IAED,gBAAgB;IACR,WAAW;QAClB,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,MAAM,MAAM,GAAG,kBAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,MAAM,KAAK,eAAe,EAAE;YAC/B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,EAAE;gBACtC,kEAAkE;gBAClE,mFAAmF;gBACnF,0EAA0E;gBAC1E,MAAM,OAAO,GACZ,kBAAO,CAAC,OAAO,CAAC,CAAE,MAAiB,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,qCAAqC;gBACxF,MAAM,QAAQ,GACb,kBAAO,CAAC,OAAO,CAAC,CAAE,MAAiB,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,qCAAqC;gBACxF,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,EAAE;oBAC7B,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC;iBACnC;aACD;SACD;aAAM;YACN,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SACjC;IACF,CAAC;IAED,gBAAgB;IACR,YAAY;QACnB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,eAAe,EAAE;YAC1B,OAAO;SACP;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,kBAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC;QAC/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAChC,CAAC;IAEM,eAAe;QACrB,IAAI,IAAI,CAAC,cAAc,EAAE;YACxB,MAAM,MAAM,GAAG,kBAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9D,IAAI,MAAM,KAAK,CAAC,CAAC,wBAAwB,EAAE;gBAC1C,OAAO,IAAI,CAAC;aACZ;YACD,IAAI,CAAC,UAAU,EAAE,CAAC;SAClB;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,kBAAkB,CAAC,GAAgB;QACzC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,MAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC;QAC3B,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACxB,kBAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAW,kBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACxE,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,KAAK,CAAC,CAAC;YACd,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;YACnB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEM,UAAU;QAChB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,UAAU,EAAE,CAAC;SAClB;QAED,IAAI,kBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;YACpD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,QAAQ,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QACxB,MAAM,CAAC,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YACvC,QAAQ,GAAG,OAAO,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,GAAG;YACnB,OAAO,EAAE,CAAC;YACV,OAAO,EAAE,QAAQ;SACjB,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;IAEM,UAAU;QAChB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YAClD,OAAO;SACP;QACD,kBAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAC9B,kBAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAC9B,kBAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;SAC3B;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAC7B,CAAC;IAEM,yBAAyB;QAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,OAAO,OAAO,CAAC,OAAO,CACrB,kBAAO,CAAC,8BAA8B,CAAC,IAAI,CAAC,OAAO,CAAC,CACpD,CAAC;IACH,CAAC;IACM,wBAAwB;QAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,OAAO,OAAO,CAAC,OAAO,CACrB,kBAAO,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC,CACnD,CAAC;IACH,CAAC;IACM,iBAAiB;QACvB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACrE,CAAC;IACM,uBAAuB;QAC7B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,OAAO,OAAO,CAAC,OAAO,CACrB,kBAAO,CAAC,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,CAClD,CAAC;IACH,CAAC;IACM,UAAU,CAAC,KAAa;QAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,kBAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IACM,aAAa,CAAC,SAAiB;QACrC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,kBAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACzD,CAAC;IACM,cAAc,CAAC,SAA6B,EAAE,KAAa;QACjE,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,kBAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAC1B,QAAsC,EACtC,KAAW;QAEX,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACvC,IAAI,MAAM,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;YACzC,OAAO;SACP;QACD,MAAM,MAAM;QACX,yCAAyC;QACzC,QAAQ,KAAK,IAAI;YAChB,CAAC,CAAC,YAAY,CACZ,qBAAqB,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,EAC5C,KAAK,CACJ;YACH,CAAC,CAAC,0EAA0E;gBAC5E,0DAA0D;gBAC1D,IAAI,CAAC,mBAAmB,KAAK,IAAI;oBACjC,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,YAAY,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAClD,yFAAyF;QACzF,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;YACvC,mDAAmD;YACnD,kBAAO,CAAC,mCAAmC,CAC1C,IAAI,CAAC,OAAO,EACZ,MAAM,EACN,IAAI,CAAC,MAAM,CACX,CAAC;YACF,0BAAe,CAAC,MAAM,CAAC,CAAC;SACxB;aAAM;YACN,IAAI,MAAM,KAAK,IAAI,EAAE;gBACpB,8CAA8C;gBAC9C,kBAAO,CAAC,mCAAmC,CAC1C,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,mBAAoB,EACzB,IAAI,CAAC,MAAM,CACX,CAAC;gBACF,0BAAe,CAAC,MAAO,CAAC,CAAC;aACzB;iBAAM;gBACN,kBAAO,CAAC,mCAAmC,CAC1C,IAAI,CAAC,OAAO,EACZ,MAAM,EACN,IAAI,CAAC,MAAM,CACX,CAAC;aACF;SACD;QACD,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;IAClC,CAAC;IAED,gBAAgB;IACR,iBAAiB;QACxB,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SAClD;IACF,CAAC;IAED,gBAAgB;IACR,uBAAuB;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,EAAE;YACrC,IAAI,CAAC,WAAW,EAAE,CAAC;SACnB;IACF,CAAC;IAED,gBAAgB;IACR,SAAS,CAChB,OAAoB,EACpB,QAAqB,EACrB,UAAkB;QAElB,kBAAO,CAAC,wBAAwB,CAC/B,IAAI,CAAC,MAAM,EACX,UAAU,EACV,OAAO,EACP,CAAC,EACD,CAAC,EACD,QAAQ,EACR,CAAC,EACD,CAAC,CACD,CAAC;IACH,CAAC;IAED,gBAAgB;IACR,eAAe;QACtB,MAAM,UAAU,GAAG,KAAK,CAAC;QACzB,MAAM,IAAI,GAAG,CAAC,GAAG,UAAU,CAAC;QAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAC7B,MAAM,OAAO,GAAG,GAAG,CAAC;QACpB,MAAM,QAAQ,GAAG,CAAE,GAAc,GAAG,IAAI,CAAgB,CAAC;QACzD,OAAO,IAAI,CAAC,SAAS,EAAE,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;SAC9C;QACD,IAAI,CAAC,GAAG,CAAC,CAAC;IACX,CAAC;IAED,gBAAgB;IACR,gBAAgB;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SACzB;QACD,MAAM,UAAU,GAAG,KAAK,CAAC;QACzB,MAAM,IAAI,GAAG,CAAC,GAAG,UAAU,CAAC;QAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAC7B,MAAM,OAAO,GAAG,GAAG,CAAC;QACpB,MAAM,QAAQ,GAAG,CAAE,GAAc,GAAG,IAAI,CAAgB,CAAC;QACzD,MAAM,SAAS,GACd,OAAO,UAAU,KAAK,WAAW;YAChC,CAAC,CAAC,GAAG,EAAE;gBACL,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE,CACpC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CACtB,CAAC;YACF,CAAC;YACH,CAAC,CAAC,GAAG,EAAE;gBACL,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,CAAC,CAAC;QACN,SAAS,IAAI;YACZ,OAAO,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,SAAS,IAAI;YACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACtB,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;aACzB;YACD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC9C,OAAO,IAAI,EAAE,CAAC;QACf,CAAC;QACD,OAAO,IAAI,EAAE,CAAC;IACf,CAAC;IAEM,oBAAoB;QAC1B,OAAO,IAAI,CAAC,YAAY;YACvB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO;YAC3B,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe;QAC5B,yBAAa,EAAE,CAAC;QAChB,MAAM,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,uBAAuB,CACpC,GAAe,EACf,IAAY,EACZ,QAAiC,EACjC,KAAa;QAEb,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;SAClD;QACD,MAAM,GAAG,GAAG,YAAY,CACvB,CAAC,IAAY,EAAE,EAAe,EAAE,IAAY,EAAE,IAAY,EAAE,EAAE;YAC7D,MAAM,CAAC,GAAG,IAAI,kBAAkB,CAAC,EAAE,EAAE,kBAAO,CAAC,CAAC;YAC9C,MAAM,IAAI,GACT,kBAAO,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;YACnC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC,EACD,OAAO,CACP,CAAC;QACF,MAAM,CAAC,GAAG,+BAA+B,CACxC,GAAG,CAAC,MAAM,EAAE,EACZ,IAAI,EACJ,GAAG,EACH,KAAK,CACL,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACb,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;SAC5B;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,oBAAoB,CACjC,GAAe,EACf,QAAgB,EAChB,KAAqB;QAErB,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;SAClD;QACD,GAAG,CAAC,oBAAoB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IACD;;;;;OAKG;IACI,MAAM,CAAC,YAAY,CACzB,GAAe,EACf,QAAgB,EAChB,SAA8B;QAE9B,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;SAClD;QACD,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACvC,CAAC;IACD;;;;;;OAMG;IACI,MAAM,CAAC,uBAAuB,CAAC,GAAe,EAAE,IAAY;QAClE,IAAI,CAAC,CAAC,GAAG,YAAY,SAAS,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;SAClD;QACD,OAAO,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;CACD;;;ACrzCuC;AAExC;;GAEG;AACY,SAAS,YAAY;IACnC,OAAO,WAAW,CAAC,sBAAsB,EAAE,CAAC;AAC7C,CAAC;;;ACNyC;AA0C1C,gBAAgB;AACT,SAAS,kBAAkB,CACjC,IAAiB,EACjB,WAAmD;IAEnD,MAAM,QAAQ,GAAwB;QACrC,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,CAAC;KACV,CAAC;IACF,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/F,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,OAAO,QAAQ,CAAC;AACjB,CAAC;AAED,SAAS,wBAAwB,CAAC,GAAU;IAC3C,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,MAAM,OAAO,GAAU,EAAE,CAAC;IAC1B,IAAI,GAAG,GAAQ,GAAG,CAAC;IACnB,OAAO,GAAG,IAAI,GAAG,KAAK,MAAM,CAAC,SAAS,EAAE;QACvC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACrB,GAAG,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;KACjC;IACD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;QACrB,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAC7C,IAAI;gBACH,MAAM,IAAI,GAAI,GAAW,CAAC,GAAG,CAAC,CAAC;gBAC/B,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;oBAC3D,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;iBACnB;aACD;YAAC,OAAO,EAAE,EAAE,GAAG;QACjB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,OAAO;QACN,QAAQ,EAAE,GAAG,CAAC,IAAI;QAClB,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,MAAM,EAAE,MAAM;KACd,CAAC;AACH,CAAC;AAED,SAAS,2BAA2B,CAAC,GAAQ;IAC5C,OAAO,wBAAwB,CAAC,CAAC,GAAG,IAAI,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5F,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAuB;IAChD,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACtE,CAAC;AAED,SAAS,oBAAoB,CAAC,MAAgB,EAAE,IAA2C,EAAE,CAAe;IAC3G,MAAM,IAAI,GAA0B,CAAC,CAAC,IAAI,CAAC;IAC3C,IAAI,CAAC,IAAI,EAAE;QACV,OAAO;KACP;IACD,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;QACvB,OAAO;KACP;IACD,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,IAAI,KAAK,EAAE;QACV,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,KAAK,EAAE;YACf,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAC3C;aAAM;YACN,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACxB;KACD;SAAM;QACN,IAAI,IAAI,CAAC,KAAK,EAAE;YACf,MAAM,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnC;KACD;AACF,CAAC;AAKD,gBAAgB;AACT,SAAS,QAAQ,CAAC,EAAE,IAAI,EAAuB,EAAE,MAAc,EAAE,IAAW;IAClF,IAAI,CAAC,WAAW,CAAC;QAChB,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI;KACG,CAAC,CAAC;AAC3B,CAAC;AAED,gBAAgB;AACT,SAAS,mBAAmB,CAAI,QAA6B,EAAE,MAAc,EAAE,IAAW;IAChG,MAAM,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;IAC9B,IAAI,QAAQ,CAAC,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,OAAO,GAAG,CAAC,EAAE;QAC1D,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC;KACrB;IACD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAClD,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,MAAM,SAAS,GAAmB,EAAE,CAAC;IACrC,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,WAAW,EAAE;QACnC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KACxB;IACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;QACzB,EAAE,EAAE,MAAM,EAAE,IAAI;KACO,EAAE,SAAS,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC;AAChB,CAAC;AAYD,gBAAgB;AACT,SAAS,oBAAoB,CACnC,IAAiB,EACjB,kBAAwC,EACxC,kBAA6B,EAC7B,WAAiD;IAEjD,MAAM,QAAQ,GAA0B;QACvC,IAAI,EAAE,IAAI;KACV,CAAC;IACF,IAAI,kBAAkB,EAAE;QACvB,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;YACtC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;YACpB,IAAI,CAAC,IAAI,EAAE;gBACV,OAAO;aACP;YACD,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;KACH;SAAM;QACN,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;YACtC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;YACpB,IAAI,CAAC,IAAI,EAAE;gBACV,OAAO;aACP;YACD,kBAAkB,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;KACH;IACD,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,OAAO,QAAQ,CAAC;AACjB,CAAC;AAED,SAAS,kBAAkB,CAC1B,IAAiB,EACjB,IAAyB,EACzB,kBAA6B,EAC7B,IAA0C;IAE1C,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;QACvB,OAAO;KACP;IACD,MAAM,MAAM,GAAG,kBAAkB,EAAE,CAAC;IACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACzB,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;KAC9E;SAAM;QACN,IAAI;YACH,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACzF;QAAC,OAAO,CAAC,EAAE;YACX,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SACnD;KACD;AACF,CAAC;AAED,gBAAgB;AACT,SAAS,UAAU,CAAC,QAA+B,EAAE,EAAU,EAAE,MAAc,EAAE,KAAU;IACjG,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,cAAc,CAAC,IAAiB,EAAE,EAAU,EAAE,MAAc,EAAE,KAAU;IAChF,IAAI,KAAK,YAAY,OAAO,EAAE;QAC7B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,IAAI,EAAE,IAAI,CAAC,EAAE;gBACZ,IAAI,CAAC,WAAW,CAAC;oBAChB,EAAE;oBACF,MAAM;oBACN,GAAG,EAAE,CAAC;iBACmB,CAAC,CAAC;aAC5B;QACF,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC;gBAChB,EAAE;gBACF,MAAM;gBACN,KAAK,EAAE,2BAA2B,CAAC,KAAK,CAAC;aAChB,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;KACH;SAAM;QACN,IAAI,CAAC,WAAW,CAAC;YAChB,EAAE;YACF,MAAM;YACN,GAAG,EAAE,KAAK;SACe,CAAC,CAAC;KAC5B;AACF,CAAC;AAED,gBAAgB;AACT,SAAS,eAAe,CAAC,QAA+B,EAAE,EAAU,EAAE,MAAc,EAAE,KAAU;IACtG,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAiB,EAAE,EAAU,EAAE,MAAc,EAAE,KAAU;IACrF,IAAI,CAAC,WAAW,CAAC;QAChB,EAAE;QACF,MAAM;QACN,KAAK,EAAE,2BAA2B,CAAC,KAAK,CAAC;KAChB,CAAC,CAAC;AAC7B,CAAC;;;ACrPoD;AAEtC,MAAM,gBAAgB;IAIpC,YAAY;IACZ,YAAmB,IAAiB,EAAmB,IAAY;QAAZ,SAAI,GAAJ,IAAI,CAAQ;QAClE,IAAI,CAAC,UAAU,GAAG,kBAAkC,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEM,OAAO;QACb,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAEM,SAAS,CAAC,IAAY,EAAE,SAAiB;QAC/C,OAAO,mBAAmC,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAC7F,CAAC;IAEM,iBAAiB;QACvB,OAAO,mBAAmC,CAAW,IAAI,CAAC,UAAU,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC;IAChG,CAAC;CACD;;;ACnBuE;AAEnB;AAErD,gBAAgB;AACD,MAAM,gBAAgB;IAIpC,YAAY,IAAiB;QAC5B,IAAI,CAAC,UAAU,GAAG,kBAAkC,CAAC,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED,gBAAgB;IACT,MAAM;QACZ,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;IACpF,CAAC;IACD,gBAAgB;IACT,6BAA6B,CAAC,UAAkB,EAAE,YAAoB,EAAE,KAAa;QAC3F,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,mBAAmC,CACxE,IAAI,CAAC,UAAW,EAChB,+BAA+B,EAC/B,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC,CACzC,CAAC,CAAC;IACJ,CAAC;IAEM,KAAK;QACX,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;IACzD,CAAC;IACM,mBAAmB,CAAC,KAA4B;QACtD,IAAI,GAAoB,CAAC;QACzB,IAAI,KAAK,YAAY,2BAA2B,EAAE;YACjD,GAAG,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;SACjC;aAAM;YACN,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAC,CAAC;SACpF;QACD,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnH,CAAC;IACM,gBAAgB,CAAC,QAAgB;QACvC,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC5E,CAAC;IACM,uBAAuB;QAC7B,OAAO,mBAAmC,CAAe,IAAI,CAAC,UAAW,EAAE,yBAAyB,EAAE,EAAE,CAAC,CAAC;IAC3G,CAAC;IACM,cAAc;QACpB,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;IAC5F,CAAC;IACM,aAAa,CAAC,KAAa;QACjC,OAAO,mBAAmC,CAAa,IAAI,CAAC,UAAW,EAAE,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACpG,CAAC;IACM,YAAY,CAAC,KAAa;QAChC,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACrE,CAAC;IACM,YAAY;QAClB,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;IAC1F,CAAC;IACM,OAAO;QACb,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IACrF,CAAC;IACM,WAAW,CAAC,KAAqB,EAAE,IAAY,EAAE,UAAmB;QAC1E,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;IACtF,CAAC;IACM,mBAAmB,CAAC,QAAgB,EAAE,KAAqB,EAAE,IAAY,EAAE,UAAmB;QACpG,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,qBAAqB,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;IACxG,CAAC;IACM,eAAe;QACrB,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC;IACnE,CAAC;IACM,yBAAyB,CAAC,QAAgB;QAChD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,2BAA2B,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrF,CAAC;IAEM,gBAAgB,CAAC,aAAqB;QAC5C,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,kBAAkB,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;IACjF,CAAC;CACD;;;AChFD,IAAI,cAAY,CAAC;AACjB,IAAI,sBAA0C,CAAC;AAC/C,IAAI,qBAAqB,GAAoB,IAAI,CAAC;AAClD,MAAM,SAAS,GAA4C,EAAE,CAAC;AAE9D,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,kCAAkC;AAClC,MAAM,QAAQ,GAAG;IAChB,KAAK,EAAE,CAAC;IACR,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;CACC,CAAC;AAGS;AAEpB,SAAS,qBAAa;IACrB,IAAI,OAAO,uBAAuB,KAAK,WAAW,EAAE;QACnD,cAAO,GAAG,uBAAuB,CAAC,UAAU,CAAC;KAC7C;SAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACzC,cAAO,GAAG,MAAM,CAAC;KACjB;SAAM;QACN,MAAM,IAAI,KAAK,CACd,kEAAkE,CAClE,CAAC;KACF;AACF,CAAC;AAED;;;;GAIG;AACI,SAAS,cAAc,CAAC,QAAyB,QAAQ,CAAC,KAAK;IACrE,IAAI,qBAAqB,KAAK,KAAK,EAAE;QACpC,OAAO;KACP;IACD,qBAAa,EAAE,CAAC;IAChB,IAAI,KAAK,IAAI,IAAI,EAAE;QAClB,IAAI,sBAAsB,IAAI,IAAI,EAAE;YACnC,cAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,sBAAsB,EAAE,CAAC,CAAC,CAAC;YAC9D,cAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,sBAAsB,EAAE,CAAC,CAAC,CAAC;YAC9D,cAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,sBAAsB,EAAE,CAAC,CAAC,CAAC;YAC9D,cAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,sBAAsB,EAAE,CAAC,CAAC,CAAC;SAC9D;QACD,cAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACzC;SAAM;QACN,IAAI,GAAuB,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,eAAe,EAAE,EAAE,CAAC,EAAE;YAC7C,MAAM,CAAC,GAAG,cAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,EAAE;gBACzB,GAAG,GAAG,CAAC,CAAC;aACR;SACD;QACD,IAAI,GAAG,IAAI,IAAI,IAAI,sBAAsB,IAAI,IAAI,EAAE;YAClD,sBAAsB,GAAG,GAAG,CAAC;SAC7B;KACD;IACD,qBAAqB,GAAG,KAAK,CAAC;IAC9B,KAAK,MAAM,EAAE,IAAI,SAAS,EAAE;QAC3B,EAAE,CAAC,KAAK,CAAC,CAAC;KACV;AACF,CAAC;AAED;;GAEG;AACI,SAAS,cAAc;IAC7B,cAAc,CAAC,IAAI,CAAC,CAAC;AACtB,CAAC;AAED,YAAY;AACL,SAAS,uBAAuB;IACtC,OAAO,qBAAqB,CAAC;AAC9B,CAAC;AAED,YAAY;AACL,SAAS,8BAA8B,CAAC,EAAoC;IAClF,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB,CAAC;AAED,YAAY;AACL,SAAS,iCAAiC,CAAC,EAAoC;IACrF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QAC1C,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;YACxB,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACvB,OAAO;SACP;KACD;AACF,CAAC;;;ACtFiD;AACA;AACG;AACyC;AAkB9F,kDAAkD;AACnC,MAAM,2BAA2B;IAc/C;QACC,IAAI,CAAC,OAAO,GAAG;YACd,OAAO,EAAE,KAAK;YACd,aAAa,EAAE,KAAK;SACpB,CAAC;QACF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,KAAK,iBAAgC,CAAC;QAC3C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClE,8BAA8B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC3D,CAAC;IAED,sCAAsC;IACtC,IAAW,IAAI;QACd,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAqB,EAAE,QAA8B;QAC3E,MAAM,gBAAgB,GAAqB;YAC1C,QAAQ,EAAE,QAAQ;YAClB,oBAAoB,EAAE,uBAAuB,EAAE;SAC/C,CAAC;QACF,MAAM,IAAI,GAAG,IAAI,gBAAgB,CAAC,OAAO,kCAA2B;YACnE,cAAc,EAAE,CAAC;YACjB,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC;YACf,kBAAkB,EAAE,CAAC,CAAC,CAAC;YACvB,gBAAgB,EAAE,gBAAgB;SAClC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,UAAU,GAAG,kBAAkC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;YACxE,IAAI,IAAI,CAAC,MAAM,sCAA2B,EAAE;gBAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;gBACxB,OAAO,IAAI,CAAC;aACZ;YACD,OAAO,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,aAAa;QACnB,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;IACjC,CAAC;IAEM,IAAI,CAAC,WAAmB,EAAE,SAA+B;IAChE,CAAC;IAEM,KAAK;QACX,6BAA6B;QAC7B,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;IAEM,SAAS;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IAC7B,CAAC;IAEM,gBAAgB,CAAC,KAA0B,EAAE,OAAgB;QACnE,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,kBAAkB,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;IAClF,CAAC;IAEM,OAAO;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAEM,OAAO,CAAC,IAAY;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACxF,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC,CAAC;IACJ,CAAC;IAEM,cAAc,CAAC,OAAe,EAAE,MAAe;QACrD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IACjF,CAAC;IAEM,oBAAoB;QAC1B,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,sBAAsB,EAAE,EAAE,CAAC,CAAC;IAChG,CAAC;IAEM,SAAS,CAAC,GAAgB;QAChC,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1F,CAAC;IAEM,WAAW,CAAC,EAAU;QAC5B,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,gBAAgB,CAAC,EAAU;QACjC,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzF,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,OAAe;QACpC,MAAM,OAAO,GAAG,IAAI,cAAc,EAAE,CAAC;QACrC,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,gBAAgB,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC9H,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACJ,CAAC;IAEM,kBAAkB,CAAC,EAAU;QACnC,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,oBAAoB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClG,CAAC;IACM,kBAAkB,CAAC,EAAU,EAAE,MAAc;QACnD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,oBAAoB,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IAChF,CAAC;IAEM,MAAM;QACZ,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,GAAW,EAAE,GAAW;QACvD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,YAAY,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5E,CAAC;IACM,WAAW,CAAC,IAAY,EAAE,GAAW;QAC3C,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,aAAa,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IACxE,CAAC;IACM,eAAe,CAAC,IAAY,EAAE,GAAW,EAAE,GAAW;QAC5D,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,iBAAiB,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACjF,CAAC;IACM,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,GAAW;QACzD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAC9E,CAAC;IACM,iBAAiB,CAAC,IAAY,EAAE,OAAe;QACrD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,mBAAmB,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IAClF,CAAC;IACM,mBAAmB,CAAC,IAAY,EAAE,GAAW;QACnD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,qBAAqB,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAChF,CAAC;IACM,aAAa,CAAC,IAAY,EAAE,GAAW;QAC7C,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,eAAe,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IACM,SAAS,CAAC,IAAgB;QAChC,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;IAEM,yBAAyB,CAAC,IAAY,EAAE,GAAW;QACzD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,2BAA2B,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;IACtF,CAAC;IACM,cAAc,CAAC,IAAY,EAAE,IAAY;QAC/C,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,gBAAgB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IACM,eAAe,CAAC,IAAY,EAAE,OAAe;QACnD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,iBAAiB,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC;IACM,iBAAiB,CAAC,IAAY,EAAE,OAAe,EAAE,IAAY,EAAE,SAAiB;QACtF,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,mBAAmB,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IACnG,CAAC;IACM,gBAAgB,CAAC,IAAY;QACnC,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,kBAAkB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IACM,gBAAgB;QACtB,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAC;IACpE,CAAC;IACM,eAAe;QACrB,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC;IACnE,CAAC;IACM,eAAe,CAAC,IAAa;QACnC,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,iBAAiB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACvE,CAAC;IACM,gBAAgB,CAAC,IAAa;QACpC,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,kBAAkB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IACM,kBAAkB,CAAC,IAAY,EAAE,MAAe;QACtD,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,oBAAoB,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAClF,CAAC;IAEM,WAAW;QACjB,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;IACvF,CAAC;IAEM,WAAW;QACjB,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;IAC/D,CAAC;IAEM,eAAe;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;IACnC,CAAC;IAEM,kBAAkB,CAAC,GAAgB;QACzC,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,oBAAoB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACjG,CAAC;IAEM,UAAU;QAChB,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;IACtF,CAAC;IAEM,UAAU;QAChB,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,YAAY,EAAE,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEM,yBAAyB;QAC/B,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,2BAA2B,EAAE,EAAE,CAAC,CAAC;IACvG,CAAC;IACM,wBAAwB;QAC9B,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,0BAA0B,EAAE,EAAE,CAAC,CAAC;IACtG,CAAC;IACM,iBAAiB;QACvB,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC;IAC/F,CAAC;IACM,uBAAuB;QAC7B,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,yBAAyB,EAAE,EAAE,CAAC,CAAC;IACrG,CAAC;IACM,UAAU,CAAC,KAAa;QAC9B,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACnE,CAAC;IACM,aAAa,CAAC,SAAiB;QACrC,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,eAAe,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1E,CAAC;IACM,cAAc,CAAC,SAA6B,EAAE,KAAa;QACjE,QAAwB,CAAC,IAAI,CAAC,UAAW,EAAE,gBAAgB,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAClF,CAAC;IAEM,oBAAoB;QAC1B,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,sBAAsB,EAAE,EAAE,CAAC,CAAC;IAChG,CAAC;IAED;;OAEG;IACI,eAAe;QACrB,MAAM,OAAO,GAAG,IAAI,cAAc,EAAE,CAAC;QACrC,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,iBAAiB,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAChH,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,0BAA0B,CAAC,YAAuC,EAAE,KAAW;QACrF,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,4BAA4B,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;IACzH,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,6BAA6B,CAAC,GAAe,EAAE,UAAkB,EAAE,YAAoB,EAAE,KAAa;QAC5G,IAAI,CAAC,CAAC,GAAG,YAAY,gBAAgB,CAAC,EAAE;YACvC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC;SACjE;QACD,OAAO,GAAG,CAAC,6BAA6B,CAAC,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;;;;OAWG;IACI,YAAY,CAAC,IAAY,EAAE,KAAU;QAC3C,OAAO,mBAAmC,CAAO,IAAI,CAAC,UAAW,EAAE,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IACnG,CAAC;IAED,gBAAgB;IACT,kBAAkB;QACxB,OAAO,mBAAmC,CAAS,IAAI,CAAC,UAAW,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC;IAC/F,CAAC;IAED,gBAAgB;IACR,qBAAqB,CAAC,KAAsB;QACnD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC5B,OAAO;SACP;QACD,QAAwB,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACtE,CAAC;CACD;;;AClVwC;AAGqC;AAEpC;AAEc;AACoC;AAElD;AAC8B;AACH;AAsBnE", "sources": ["webpack://JSSynth/webpack/universalModuleDefinition", "webpack://JSSynth/webpack/bootstrap", "webpack://JSSynth/webpack/runtime/define property getters", "webpack://JSSynth/webpack/runtime/hasOwnProperty shorthand", "webpack://JSSynth/webpack/runtime/make namespace object", "webpack://JSSynth/./src/main/Constants.ts", "webpack://JSSynth/./src/main/PointerType.ts", "webpack://JSSynth/./src/main/SequencerEventData.ts", "webpack://JSSynth/./src/main/ISequencerEventData.ts", "webpack://JSSynth/./src/main/MessageError.ts", "webpack://JSSynth/./src/main/SequencerEvent.ts", "webpack://JSSynth/./src/main/MIDIEvent.ts", "webpack://JSSynth/./src/main/Sequencer.ts", "webpack://JSSynth/./src/main/Soundfont.ts", "webpack://JSSynth/./src/main/Synthesizer.ts", "webpack://JSSynth/./src/main/waitForReady.ts", "webpack://JSSynth/./src/main/MethodMessaging.ts", "webpack://JSSynth/./src/main/WorkletSoundfont.ts", "webpack://JSSynth/./src/main/WorkletSequencer.ts", "webpack://JSSynth/./src/main/logging.ts", "webpack://JSSynth/./src/main/AudioWorkletNodeSynthesizer.ts", "webpack://JSSynth/./src/main/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"js-synthesizer\"] = factory();\n\telse\n\t\troot[\"JSSynth\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "\n/** Default values for synthesizer instances */\nexport const enum SynthesizerDefaultValues {\n\tGain = 0.5\n}\n\n/** Interpolation values used by ISynthesizer.setInterpolation */\nexport const enum InterpolationValues {\n\t/** No interpolation: Fastest, but questionable audio quality */\n\tNone = 0,\n\t/** Straight-line interpolation: A bit slower, reasonable audio quality */\n\tLinear = 1,\n\t/** Fourth-order interpolation, good quality, the default */\n\tFourthOrder = 4,\n\t/** Seventh-order interpolation */\n\tSeventhOrder = 7,\n\t/** Default interpolation method */\n\tDefault = FourthOrder,\n\t/** Highest interpolation method */\n\tHighest = SeventhOrder,\n}\n\n/** Chorus modulation waveform type, used by Synthesizer.setChorus etc. */\nexport const enum ChorusModulation {\n\t/** Sine wave chorus modulation */\n\tSine = 0,\n\t/** Triangle wave chorus modulation */\n\tTriangle = 1\n}\n\n/** Generator type ID (specified in SoundFont format) */\nexport const enum GeneratorTypes {\n\tStartAddrsOffset = 0,\n\tEndAddrsOffset = 1,\n\tStartLoopAddrsOffset = 2,\n\tEndLoopAddrsOffset = 3,\n\tStartAddrsCoarseOffset = 4,\n\tModLfoToPitch = 5,\n\tVibLfoToPitch = 6,\n\tModEnvToPitch = 7,\n\tInitialFilterFc = 8,\n\tInitialFilterQ = 9,\n\tModLfoToFilterFc = 10,\n\tModEnvToFilterFc = 11,\n\tEndAddrsCoarseOffset = 12,\n\tModLfoToVolume = 13,\n\t// 14: unused\n\tChorusEffectsSend = 15,\n\tReverbEffectsSend = 16,\n\tPan = 17,\n\t// 18-20: unused\n\tDelayModLFO = 21,\n\tFreqModLFO = 22,\n\tDelayVibLFO = 23,\n\tFreqVibLFO = 24,\n\tDelayModEnv = 25,\n\tAttackModEnv = 26,\n\tHoldModEnv = 27,\n\tDecayModEnv = 28,\n\tSustainModEnv = 29,\n\tReleaseModEnv = 30,\n\tKeynumToModEnvHold = 31,\n\tKeynumToModEnvDecay = 32,\n\tDelayVolEnv = 33,\n\tAttackVolEnv = 34,\n\tHoldVolEnv = 35,\n\tDecayVolEnv = 36,\n\tSustainVolEnv = 37,\n\tReleaseVolEnv = 38,\n\tKeynumToVolEnvHold = 39,\n\tKeynumToVolEnvDecay = 40,\n\tInstrument = 41,\n\t// 42: reserved\n\tKeyRange = 43,\n\tVelRange = 44,\n\tStartloopAddrsCoarseOffset = 45,\n\tKeynum = 46,\n\tVelocity = 47,\n\tInitialAttenuation = 48,\n\t// 49: reserved2\n\tEndloopAddrsCoarseOffset = 50,\n\tCoarseTune = 51,\n\tFineTune = 52,\n\tSampleID = 53,\n\tSampleModes = 54,\n\t// 55: reserved3\n\tScaleTuning = 56,\n\tExclusiveClass = 57,\n\tOverridingRootKey = 58,\n\n\t// Not in SoundFont specification\n\t_Pitch = 59,\n\t_CustomBalance = 60,\n\t_CustomFilterFc = 61,\n\t_CustomFilterQ = 62\n}\n\n/** Mono legato mode */\nexport const enum LegatoMode {\n\tRetrigger = 0,\n\tMultiRetrigger = 1\n}\n\n/** Portamento mode */\nexport const enum PortamentoMode {\n\tEachNote = 0,\n\tLegatoOnly = 1,\n\tStaccatoOnly = 2\n}\n\n/** Breath mode flags */\nexport const enum BreathFlags {\n\tPoly = 0x10,\n\tMono = 0x20,\n\tSync = 0x40\n}\n\n/** Tempo type for `Synthesizer.setPlayerTempo` */\nconst PlayerSetTempoType = {\n\tInternal: 0,\n\tExternalBpm: 1,\n\tExternalMidi: 2,\n} as const;\n/** Tempo type for `Synthesizer.setPlayerTempo` */\ntype PlayerSetTempoType = (typeof PlayerSetTempoType)[keyof typeof PlayerSetTempoType];\nexport { PlayerSetTempoType };\n", "\ntype NullPointerType = number & { _null_pointer_marker: never; };\n\n/** @internal */\ntype PointerType = NullPointerType | (number & { _pointer_marker: never; });\n\nexport default PointerType;\n\ntype UniquePointerType<TMarker extends string> = NullPointerType | (number & {\n\t_pointer_marker: never;\n} & {\n\t[P in TMarker]: never;\n});\nexport { UniquePointerType };\n\nexport const INVALID_POINTER: NullPointerType = 0 as any as NullPointerType;\n", "\nimport { EventType } from './SequencerEvent';\nimport ISequencerEventData from './ISequencerEventData';\nimport PointerType, { INVALID_POINTER } from './PointerType';\n\n/** @internal */\nexport default class SequencerEventData implements ISequencerEventData {\n\t/** @internal */\n\tconstructor(private _ptr: PointerType, private _module: any) {\n\t}\n\n\t/** @internal */\n\tpublic getRaw() {\n\t\treturn this._ptr;\n\t}\n\n\t/** @internal */\n\tpublic dispose() {\n\t\tthis._ptr = INVALID_POINTER;\n\t}\n\n\tpublic getType(): EventType {\n\t\tif (this._ptr === INVALID_POINTER) return -1 as any as EventType;\n\t\treturn this._module._fluid_event_get_type(this._ptr);\n\t}\n\tpublic getSource(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_source(this._ptr);\n\t}\n\tpublic getDest(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_dest(this._ptr);\n\t}\n\tpublic getChannel(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_channel(this._ptr);\n\t}\n\tpublic getKey(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_key(this._ptr);\n\t}\n\tpublic getVelocity(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_velocity(this._ptr);\n\t}\n\tpublic getControl(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_control(this._ptr);\n\t}\n\tpublic getValue(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_value(this._ptr);\n\t}\n\tpublic getProgram(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_program(this._ptr);\n\t}\n\tpublic getData(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_data(this._ptr);\n\t}\n\tpublic getDuration(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_duration(this._ptr);\n\t}\n\tpublic getBank(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_bank(this._ptr);\n\t}\n\tpublic getPitch(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_pitch(this._ptr);\n\t}\n\tpublic getSFontId(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_sfont_id(this._ptr);\n\t}\n}\n", "\nimport SequencerEvent, { EventType } from './SequencerEvent';\n\n/** @internal */\nimport PointerType, { INVALID_POINTER } from './PointerType';\n/** @internal */\nimport SequencerEventData from './SequencerEventData';\n\nconst _module: any = typeof AudioWorkletGlobalScope !== 'undefined' ?\n\tAudioWorkletGlobalScope.wasmModule : Module;\n\n/** Event data for sequencer callback. Only available in the callback function due to the instance lifetime. */\nexport default interface ISequencerEventData {\n\t/** Returns the event type */\n\tgetType(): EventType;\n\t/** Returns the source client id of event */\n\tgetSource(): number;\n\t/** Returns the destination client id of event */\n\tgetDest(): number;\n\tgetChannel(): number;\n\tgetKey(): number;\n\tgetVelocity(): number;\n\tgetControl(): number;\n\tgetValue(): number;\n\tgetProgram(): number;\n\tgetData(): number;\n\tgetDuration(): number;\n\tgetBank(): number;\n\tgetPitch(): number;\n\tgetSFontId(): number;\n}\n\n/** @internal */\nexport function rewriteEventDataImpl(ev: PointerType, event: SequencerEvent): boolean {\n\tswitch (event.type) {\n\t\tcase EventType.Note:\n\t\tcase 'note':\n\t\t\t_module._fluid_event_note(ev, event.channel, event.key, event.vel, event.duration);\n\t\t\tbreak;\n\t\tcase EventType.NoteOn:\n\t\tcase 'noteon':\n\t\tcase 'note-on':\n\t\t\t_module._fluid_event_noteon(ev, event.channel, event.key, event.vel);\n\t\t\tbreak;\n\t\tcase EventType.NoteOff:\n\t\tcase 'noteoff':\n\t\tcase 'note-off':\n\t\t\t_module._fluid_event_noteoff(ev, event.channel, event.key);\n\t\t\tbreak;\n\t\tcase EventType.AllSoundsOff:\n\t\tcase 'allsoundsoff':\n\t\tcase 'all-sounds-off':\n\t\t\t_module._fluid_event_all_sounds_off(ev, event.channel);\n\t\t\tbreak;\n\t\tcase EventType.AllNotesOff:\n\t\tcase 'allnotesoff':\n\t\tcase 'all-notes-off':\n\t\t\t_module._fluid_event_all_notes_off(ev, event.channel);\n\t\t\tbreak;\n\t\tcase EventType.BankSelect:\n\t\tcase 'bankselect':\n\t\tcase 'bank-select':\n\t\t\t_module._fluid_event_bank_select(ev, event.channel, event.bank);\n\t\t\tbreak;\n\t\tcase EventType.ProgramChange:\n\t\tcase 'programchange':\n\t\tcase 'program-change':\n\t\t\t_module._fluid_event_program_change(ev, event.channel, event.preset);\n\t\t\tbreak;\n\t\tcase EventType.ProgramSelect:\n\t\tcase 'programselect':\n\t\tcase 'program-select':\n\t\t\t_module._fluid_event_program_select(ev, event.channel, event.sfontId, event.bank, event.preset);\n\t\t\tbreak;\n\t\tcase EventType.ControlChange:\n\t\tcase 'controlchange':\n\t\tcase 'control-change':\n\t\t\t_module._fluid_event_control_change(ev, event.channel, event.control, event.value);\n\t\t\tbreak;\n\t\tcase EventType.PitchBend:\n\t\tcase 'pitchbend':\n\t\tcase 'pitch-bend':\n\t\t\t_module._fluid_event_pitch_bend(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.PitchWheelSensitivity:\n\t\tcase 'pitchwheelsens':\n\t\tcase 'pitchwheelsensitivity':\n\t\tcase 'pitch-wheel-sens':\n\t\tcase 'pitch-wheel-sensitivity':\n\t\t\t_module._fluid_event_pitch_wheelsens(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Modulation:\n\t\tcase 'modulation':\n\t\t\t_module._fluid_event_modulation(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Sustain:\n\t\tcase 'sustain':\n\t\t\t_module._fluid_event_sustain(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Pan:\n\t\tcase 'pan':\n\t\t\t_module._fluid_event_pan(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Volume:\n\t\tcase 'volume':\n\t\t\t_module._fluid_event_volume(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ReverbSend:\n\t\tcase 'reverb':\n\t\tcase 'reverbsend':\n\t\tcase 'reverb-send':\n\t\t\t_module._fluid_event_reverb_send(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ChorusSend:\n\t\tcase 'chorus':\n\t\tcase 'chorussend':\n\t\tcase 'chorus-send':\n\t\t\t_module._fluid_event_chorus_send(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.KeyPressure:\n\t\tcase 'keypressure':\n\t\tcase 'key-pressure':\n\t\tcase 'aftertouch':\n\t\t\t_module._fluid_event_key_pressure(ev, event.channel, event.key, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ChannelPressure:\n\t\tcase 'channelpressure':\n\t\tcase 'channel-pressure':\n\t\tcase 'channel-aftertouch':\n\t\t\t_module._fluid_event_channel_pressure(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.SystemReset:\n\t\tcase 'systemreset':\n\t\tcase 'system-reset':\n\t\t\t_module._fluid_event_system_reset(ev);\n\t\t\tbreak;\n\t\tcase EventType.Timer:\n\t\tcase 'timer':\n\t\t\t_module._fluid_event_timer(ev, event.data);\n\t\t\tbreak;\n\t\tdefault:\n\t\t\t// 'typeof event' must be 'never' here\n\t\t\treturn false;\n\t}\n\treturn true;\n}\n\n/**\n * Rewrites event data with specified SequencerEvent object.\n * @param data destination instance\n * @param event source data\n * @return true if succeeded\n */\nexport function rewriteEventData(data: ISequencerEventData, event: SequencerEvent): boolean {\n\tif (!data || !(data instanceof SequencerEventData)) {\n\t\treturn false;\n\t}\n\tconst ev = data.getRaw();\n\tif (ev === INVALID_POINTER) {\n\t\treturn false;\n\t}\n\treturn rewriteEventDataImpl(ev, event);\n}\n", "\n/** Error object used for errors occurred in the message receiver (e.g. Worklet) */\nexport default class MessageError extends Error {\n\t/** The name of original error object if available */\n\tpublic baseName: any;\n\t/** Detailed properties of original error object if available */\n\tpublic detail: any;\n\n\tconstructor(baseName: string, message: string, detail?: any) {\n\t\tsuper(message);\n\t\tthis.baseName = baseName;\n\t\tthis.detail = detail;\n\t\tif (detail && detail.stack) {\n\t\t\tthis.stack = detail.stack;\n\t\t}\n\t}\n}\n", "\n/** Event type value */\nexport const enum EventType {\n\tNote = 0,\n\t<PERSON><PERSON><PERSON>,\n\t<PERSON>O<PERSON>,\n\tAllSoundsOff,\n\tAllNotesOff,\n\tBankSelect,\n\tProgramChange,\n\tProgramSelect,\n\tPitchBend,\n\tPitchWheelSensitivity,\n\tModulation,\n\tSustain,\n\tControlChange,\n\tPan,\n\tVolume,\n\tReverbSend,\n\tChorusSend,\n\tTimer,\n\t/** internally used */\n\t_AnyControlChange,\n\tChannelPressure,\n\tKeyPressure,\n\tSystemReset,\n\t/** internally used */\n\t_Unregistering\n}\n\nexport interface EventBase {\n\t/** event type */\n\ttype: EventType | string;\n}\n\n/** Note on/off event with duration */\nexport interface NoteEvent extends EventBase {\n\ttype: EventType.Note | 'note';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** MIDI note key (0-127) */\n\tkey: number;\n\t/** velocity value (0-127) */\n\tvel: number;\n\t/** duration in the time scale (milliseconds by default) */\n\tduration: number;\n}\n\n/** Note on event */\nexport interface NoteOnEvent extends EventBase {\n\ttype: EventType.NoteOn | 'noteon' | 'note-on';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** MIDI note key (0-127) */\n\tkey: number;\n\t/** velocity value (0-127) */\n\tvel: number;\n}\n\n/** Note off event */\nexport interface NoteOffEvent extends EventBase {\n\ttype: EventType.NoteOff | 'noteoff' | 'note-off';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** MIDI note key (0-127) */\n\tkey: number;\n}\n\n/** All sounds off event */\nexport interface AllSoundsOffEvent extends EventBase {\n\ttype: EventType.AllSoundsOff | 'allsoundsoff' | 'all-sounds-off';\n\t/** MIDI channel number */\n\tchannel: number;\n}\n\n/** All notes off event */\nexport interface AllNotesOffEvent extends EventBase {\n\ttype: EventType.AllNotesOff | 'allnotesoff' | 'all-notes-off';\n\t/** MIDI channel number */\n\tchannel: number;\n}\n\n/** Bank select event */\nexport interface BankSelectEvent extends EventBase {\n\ttype: EventType.BankSelect | 'bankselect' | 'bank-select';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** bank number (0-16383) */\n\tbank: number;\n}\n\n/** Program change event */\nexport interface ProgramChangeEvent extends EventBase {\n\ttype: EventType.ProgramChange | 'programchange' | 'program-change';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** preset number (0-127) */\n\tpreset: number;\n}\n\n/** Program select event */\nexport interface ProgramSelectEvent extends EventBase {\n\ttype: EventType.ProgramSelect | 'programselect' | 'program-select';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** SoundFont ID */\n\tsfontId: number;\n\t/** bank number (0-16383) */\n\tbank: number;\n\t/** preset number (0-127) */\n\tpreset: number;\n}\n\n/** General control change event */\nexport interface ControlChangeEvent extends EventBase {\n\ttype: EventType.ControlChange | 'controlchange' | 'control-change';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** control number (0-127) */\n\tcontrol: number;\n\t/** value for control (0-127) */\n\tvalue: number;\n}\n\n/** Pitch bend event */\nexport interface PitchBendEvent extends EventBase {\n\ttype: EventType.PitchBend | 'pitchbend' | 'pitch-bend';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value (0-16383, 8192 = no bend) */\n\tvalue: number;\n}\n\n/** Pitch-wheel sensitivity event */\nexport interface PitchWheelSensitivityEvent extends EventBase {\n\ttype: EventType.PitchWheelSensitivity | 'pitchwheelsens' | 'pitchwheelsensitivity' |\n\t\t'pitch-wheel-sens' | 'pitch-wheel-sensitivity';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value in semitones */\n\tvalue: number;\n}\n\n/** Modulation event */\nexport interface ModulationEvent extends EventBase {\n\ttype: EventType.Modulation | 'modulation';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value (0-127) */\n\tvalue: number;\n}\n\n/** Sustain event */\nexport interface SustainEvent extends EventBase {\n\ttype: EventType.Sustain | 'sustain';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value (0-127) */\n\tvalue: number;\n}\n\n/** Pan event */\nexport interface PanEvent extends EventBase {\n\ttype: EventType.Pan | 'pan';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value (0-127, 0: left, 127: right) */\n\tvalue: number;\n}\n\n/** Volume event */\nexport interface VolumeEvent extends EventBase {\n\ttype: EventType.Volume | 'volume';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value (0-127) */\n\tvalue: number;\n}\n\n/** Reverb-send event */\nexport interface ReverbSendEvent extends EventBase {\n\ttype: EventType.ReverbSend | 'reverb' | 'reverbsend' | 'reverb-send';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value (0-127) */\n\tvalue: number;\n}\n\n/** Chorus-send event */\nexport interface ChorusSendEvent extends EventBase {\n\ttype: EventType.ChorusSend | 'chorus' | 'chorussend' | 'chorus-send';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** value (0-127) */\n\tvalue: number;\n}\n\n/** Key pressure event */\nexport interface KeyPressureEvent extends EventBase {\n\ttype: EventType.KeyPressure | 'keypressure' | 'key-pressure' | 'aftertouch';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** MIDI note key */\n\tkey: number;\n\t/** aftertouch value (0-127) */\n\tvalue: number;\n}\n\n/** Channel pressure event */\nexport interface ChannelPressureEvent extends EventBase {\n\ttype: EventType.ChannelPressure | 'channelpressure' | 'channel-pressure' | 'channel-aftertouch';\n\t/** MIDI channel number */\n\tchannel: number;\n\t/** aftertouch value (0-127) */\n\tvalue: number;\n}\n\n/** System reset event */\nexport interface SystemResetEvent extends EventBase {\n\ttype: EventType.SystemReset | 'systemreset' | 'system-reset';\n}\n\n/** Timer event (used for marker; no effect for synthesizer) */\nexport interface TimerEvent extends EventBase {\n\ttype: EventType.Timer | 'timer';\n\t/** any number data */\n\tdata: number;\n}\n\n/** All available events type */\ntype SequencerEvent = NoteEvent | NoteOnEvent | NoteOffEvent | AllSoundsOffEvent | AllNotesOffEvent |\n\tBankSelectEvent | ProgramChangeEvent | ProgramSelectEvent |\n\tControlChangeEvent | PitchBendEvent | PitchWheelSensitivityEvent |\n\tModulationEvent | SustainEvent | PanEvent | VolumeEvent |\n\tReverbSendEvent | ChorusSendEvent |\n\tKeyPressureEvent | ChannelPressureEvent | SystemResetEvent | TimerEvent;\nexport default SequencerEvent;\n", "\nimport IMIDIEvent from './IMIDIEvent';\nimport PointerType, { UniquePointerType } from './PointerType';\n\n/** @internal */\nexport type MIDIEventType = UniquePointerType<'midi_event'>;\n\n/** @internal */\nexport default class MIDIEvent implements IMIDIEvent {\n\n\t/** @internal */\n\tconstructor(private _ptr: MIDIEventType, private _module: any) {\n\t}\n\n\tpublic getType(): number {\n\t\treturn this._module._fluid_midi_event_get_type(this._ptr);\n\t}\n\tpublic setType(value: number): void {\n\t\tthis._module._fluid_midi_event_set_type(this._ptr, value);\n\t}\n\tpublic getChannel(): number {\n\t\treturn this._module._fluid_midi_event_get_channel(this._ptr);\n\t}\n\tpublic setChannel(value: number): void {\n\t\tthis._module._fluid_midi_event_set_channel(this._ptr, value);\n\t}\n\tpublic getKey(): number {\n\t\treturn this._module._fluid_midi_event_get_key(this._ptr);\n\t}\n\tpublic setKey(value: number): void {\n\t\tthis._module._fluid_midi_event_set_key(this._ptr, value);\n\t}\n\tpublic getVelocity(): number {\n\t\treturn this._module._fluid_midi_event_get_velocity(this._ptr);\n\t}\n\tpublic setVelocity(value: number): void {\n\t\tthis._module._fluid_midi_event_set_velocity(this._ptr, value);\n\t}\n\tpublic getControl(): number {\n\t\treturn this._module._fluid_midi_event_get_control(this._ptr);\n\t}\n\tpublic setControl(value: number): void {\n\t\tthis._module._fluid_midi_event_set_control(this._ptr, value);\n\t}\n\tpublic getValue(): number {\n\t\treturn this._module._fluid_midi_event_get_value(this._ptr);\n\t}\n\tpublic setValue(value: number): void {\n\t\tthis._module._fluid_midi_event_set_value(this._ptr, value);\n\t}\n\tpublic getProgram(): number {\n\t\treturn this._module._fluid_midi_event_get_program(this._ptr);\n\t}\n\tpublic setProgram(value: number): void {\n\t\tthis._module._fluid_midi_event_set_program(this._ptr, value);\n\t}\n\tpublic getPitch(): number {\n\t\treturn this._module._fluid_midi_event_get_pitch(this._ptr);\n\t}\n\tpublic setPitch(value: number): void {\n\t\tthis._module._fluid_midi_event_set_pitch(this._ptr, value);\n\t}\n\n\tpublic setSysEx(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_sysex(this._ptr, ptr, size, 1);\n\t}\n\tpublic setText(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_text(this._ptr, ptr, size, 1);\n\t}\n\tpublic setLyrics(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_lyrics(this._ptr, ptr, size, 1);\n\t}\n}\n", "\nimport ISequencer, { ClientInfo } from './ISequencer';\nimport ISequencerEventData, { rewriteEventDataImpl } from './ISequencerEventData';\nimport ISynthesizer from './ISynthesizer';\nimport PointerType, { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport SequencerEvent from './SequencerEvent';\nimport SequencerEventData from './SequencerEventData';\n\nimport Synthesizer from './Synthesizer';\n\ntype SequencerPointer = UniquePointerType<'sequencer_ptr'>;\ntype SequencerId = number;\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n\tfunction removeFunction(funcPtr: number): void;\n}\n\nlet _module: any;\nlet _removeFunction: (funcPtr: number) => void;\n\nlet fluid_sequencer_get_client_name: (seq: number, id: number) => string;\n\nfunction bindFunctions() {\n\tif (_module) {\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t\t_removeFunction = AudioWorkletGlobalScope.wasmRemoveFunction;\n\t} else {\n\t\t_module = Module;\n\t\t_removeFunction = removeFunction;\n\t}\n\n\tfluid_sequencer_get_client_name =\n\t\t_module.cwrap('fluid_sequencer_get_client_name', 'string', ['number', 'number']);\n}\n\nfunction makeEvent(event: SequencerEvent): PointerType | null {\n\tconst ev = _module._new_fluid_event();\n\tif (!rewriteEventDataImpl(ev, event)) {\n\t\t_module._delete_fluid_event(ev);\n\t\treturn null;\n\t}\n\treturn ev;\n}\n\n/** @internal */\nexport default class Sequencer implements ISequencer {\n\n\tprivate _seq: SequencerPointer;\n\tprivate _seqId: SequencerId;\n\n\t/** @internal */\n\tpublic _clientFuncMap: { [id: number]: number };\n\n\tconstructor() {\n\t\tbindFunctions();\n\n\t\tthis._seq = INVALID_POINTER;\n\t\tthis._seqId = -1;\n\t\tthis._clientFuncMap = {};\n\t}\n\n\t/** @internal */\n\tpublic _initialize(): Promise<void> {\n\t\tthis.close();\n\t\tthis._seq = _module._new_fluid_sequencer2(0);\n\t\tthis._seqId = -1;\n\t\treturn Promise.resolve();\n\t}\n\n\t/** @internal */\n\tpublic getRaw() {\n\t\treturn this._seq;\n\t}\n\n\tpublic close() {\n\t\tif (this._seq !== INVALID_POINTER) {\n\t\t\tObject.keys(this._clientFuncMap).forEach((clientIdStr) => {\n\t\t\t\tthis.unregisterClient(Number(clientIdStr));\n\t\t\t});\n\t\t\tthis.unregisterClient(-1);\n\t\t\t_module._delete_fluid_sequencer(this._seq);\n\t\t\tthis._seq = INVALID_POINTER;\n\t\t}\n\t}\n\n\tpublic registerSynthesizer(synth: ISynthesizer | number): Promise<number> {\n\t\tif (this._seqId !== -1) {\n\t\t\t_module._fluid_sequencer_unregister_client(this._seq, this._seqId);\n\t\t\tthis._seqId = -1;\n\t\t}\n\t\tlet val: number;\n\t\tif (typeof synth === 'number') {\n\t\t\tval = synth;\n\t\t} else if (synth instanceof Synthesizer) {\n\t\t\tval = synth.getRawSynthesizer();\n\t\t} else {\n\t\t\treturn Promise.reject(new TypeError('\\'synth\\' is not a compatible type instance'));\n\t\t}\n\n\t\tthis._seqId = _module._fluid_sequencer_register_fluidsynth(this._seq, val);\n\t\treturn Promise.resolve(this._seqId);\n\t}\n\n\tpublic unregisterClient(clientId: number): void {\n\t\tif (clientId === -1) {\n\t\t\tclientId = this._seqId;\n\t\t\tif (clientId === -1) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\t// send 'unregistering' event\n\t\tconst ev = _module._new_fluid_event();\n\t\t_module._fluid_event_set_source(ev, -1);\n\t\t_module._fluid_event_set_dest(ev, clientId);\n\t\t_module._fluid_event_unregistering(ev);\n\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t_module._delete_fluid_event(ev);\n\n\t\t_module._fluid_sequencer_unregister_client(this._seq, clientId);\n\t\tif (this._seqId === clientId) {\n\t\t\tthis._seqId = -1;\n\t\t} else {\n\t\t\tconst map = this._clientFuncMap;\n\t\t\tif (map[clientId]) {\n\t\t\t\t_removeFunction(map[clientId]);\n\t\t\t\tdelete map[clientId];\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic getAllRegisteredClients(): Promise<ClientInfo[]> {\n\t\tconst c = _module._fluid_sequencer_count_clients(this._seq);\n\t\tconst r: ClientInfo[] = [];\n\t\tfor (let i = 0; i < c; ++i) {\n\t\t\tconst id = _module._fluid_sequencer_get_client_id(this._seq, i);\n\t\t\tconst name = fluid_sequencer_get_client_name(this._seq, id);\n\t\t\tr.push({ clientId: id, name: name });\n\t\t}\n\t\treturn Promise.resolve(r);\n\t}\n\n\tpublic getClientCount(): Promise<number> {\n\t\treturn Promise.resolve<number>(_module._fluid_sequencer_count_clients(this._seq));\n\t}\n\n\tpublic getClientInfo(index: number): Promise<ClientInfo> {\n\t\tconst id = _module._fluid_sequencer_get_client_id(this._seq, index);\n\t\tconst name = fluid_sequencer_get_client_name(this._seq, id);\n\t\treturn Promise.resolve<ClientInfo>({ clientId: id, name: name });\n\t}\n\n\tpublic setTimeScale(scale: number): void {\n\t\t_module._fluid_sequencer_set_time_scale(this._seq, scale);\n\t}\n\n\tpublic getTimeScale(): Promise<number> {\n\t\treturn Promise.resolve(_module._fluid_sequencer_get_time_scale(this._seq));\n\t}\n\n\tpublic getTick(): Promise<number> {\n\t\treturn Promise.resolve(_module._fluid_sequencer_get_tick(this._seq));\n\t}\n\n\tpublic sendEventAt(event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t// send to all clients\n\t\t\tconst count = _module._fluid_sequencer_count_clients(this._seq);\n\t\t\tfor (let i = 0; i < count; ++i) {\n\t\t\t\tconst id: number = _module._fluid_sequencer_get_client_id(this._seq, i);\n\t\t\t\t_module._fluid_event_set_dest(ev, id);\n\t\t\t\t_module._fluid_sequencer_send_at(this._seq, ev, tick, isAbsolute ? 1 : 0);\n\t\t\t}\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\tpublic sendEventToClientAt(clientId: number, event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_at(this._seq, ev, tick, isAbsolute ? 1 : 0);\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic sendEventToClientNow(clientId: number, event: SequencerEvent): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic sendEventNow(clientId: number, eventData: ISequencerEventData): void {\n\t\tif (!(eventData instanceof SequencerEventData)) {\n\t\t\treturn;\n\t\t}\n\t\tconst ev = eventData.getRaw();\n\t\tif (ev !== INVALID_POINTER) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t}\n\t}\n\n\tpublic removeAllEvents(): void {\n\t\t_module._fluid_sequencer_remove_events(this._seq, -1, -1, -1);\n\t}\n\n\tpublic removeAllEventsFromClient(clientId: number): void {\n\t\t_module._fluid_sequencer_remove_events(this._seq, -1, clientId === -1 ? this._seqId : clientId, -1);\n\t}\n\n\tpublic processSequencer(msecToProcess: number) {\n\t\tif (this._seq !== INVALID_POINTER) {\n\t\t\t_module._fluid_sequencer_process(this._seq, msecToProcess);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic setIntervalForSequencer(msec: number) {\n\t\treturn setInterval(() => this.processSequencer(msec), msec);\n\t}\n}\n", "import { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport Preset from './Preset';\nimport Synthesizer from './Synthesizer';\n\ntype SFontPointer = UniquePointerType<'sfont_ptr'>;\ntype PresetPointer = UniquePointerType<'preset_ptr'>;\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n}\n\nlet _module: any;\n\nlet fluid_sfont_get_name: (sfont: SFontPointer) => string;\nlet fluid_preset_get_name: (preset: PresetPointer) => string;\n\nfunction bindFunctions() {\n\tif (_module) {\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t} else {\n\t\t_module = Module;\n\t}\n\n\tfluid_sfont_get_name =\n\t\t_module.cwrap('fluid_sfont_get_name', 'string', ['number']);\n\tfluid_preset_get_name =\n\t\t_module.cwrap('fluid_preset_get_name', 'string', ['number']);\n}\n\nexport default class Soundfont {\n\tprivate readonly _ptr: SFontPointer;\n\n\t// @internal\n\tpublic constructor(sfontPtr: SFontPointer) {\n\t\tthis._ptr = sfontPtr;\n\t}\n\n\tpublic static getSoundfontById(synth: Synthesizer, id: number): Soundfont | null {\n\t\tbindFunctions();\n\n\t\tconst sfont = _module._fluid_synth_get_sfont_by_id(synth.getRawSynthesizer(), id);\n\t\tif (sfont === INVALID_POINTER) {\n\t\t\treturn null;\n\t\t}\n\t\treturn new Soundfont(sfont);\n\t}\n\n\tpublic getName(): string {\n\t\treturn fluid_sfont_get_name(this._ptr);\n\t}\n\n\tpublic getPreset(bank: number, presetNum: number): Preset | null {\n\t\tconst presetPtr: PresetPointer = _module._fluid_sfont_get_preset(this._ptr, bank, presetNum);\n\t\tif (presetPtr === INVALID_POINTER) {\n\t\t\treturn null;\n\t\t}\n\t\tconst name = fluid_preset_get_name(presetPtr);\n\t\tconst bankNum = _module._fluid_preset_get_banknum(presetPtr);\n\t\tconst num = _module._fluid_preset_get_num(presetPtr);\n\t\treturn {\n\t\t\tsoundfont: this,\n\t\t\tname,\n\t\t\tbankNum,\n\t\t\tnum\n\t\t};\n\t}\n\n\tpublic getPresetIterable(): Iterable<Preset> {\n\t\tconst reset = () => {\n\t\t\t_module._fluid_sfont_iteration_start(this._ptr);\n\t\t};\n\t\tconst next = (): IteratorResult<Preset, void> => {\n\t\t\tconst presetPtr = _module._fluid_sfont_iteration_next(this._ptr);\n\t\t\tif (presetPtr === 0) {\n\t\t\t\treturn {\n\t\t\t\t\tdone: true,\n\t\t\t\t\tvalue: undefined\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tconst name = fluid_preset_get_name(presetPtr);\n\t\t\t\tconst bankNum = _module._fluid_preset_get_banknum(presetPtr);\n\t\t\t\tconst num = _module._fluid_preset_get_num(presetPtr);\n\t\t\t\treturn {\n\t\t\t\t\tdone: false,\n\t\t\t\t\tvalue: {\n\t\t\t\t\t\tsoundfont: this,\n\t\t\t\t\t\tname,\n\t\t\t\t\t\tbankNum,\n\t\t\t\t\t\tnum\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t};\n\t\tconst iterator = (): Iterator<Preset> => {\n\t\t\treset();\n\t\t\treturn {\n\t\t\t\tnext,\n\t\t\t};\n\t\t};\n\t\treturn {\n\t\t\t[Symbol.iterator]: iterator,\n\t\t};\n\t}\n}\n", "\nimport {\n\tSynthesizerDefaultValues,\n\tInterpolationValues,\n\tChorusModulation,\n\tGeneratorTypes,\n\tLegatoMode,\n\tPortamentoMode,\n\tPlayerSetTempoType,\n} from './Constants';\nimport IMIDIEvent from './IMIDIEvent';\nimport ISequencer from './ISequencer';\nimport ISequencerEventData from './ISequencerEventData';\nimport ISynthesizer from './ISynthesizer';\nimport PointerType, { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport SynthesizerSettings from './SynthesizerSettings';\n\nimport MIDIEvent, { MIDIEventType } from './MIDIEvent';\nimport Sequencer from './Sequencer';\nimport SequencerEvent, { EventType as SequencerEventType } from './SequencerEvent';\nimport SequencerEventData from './SequencerEventData';\nimport Soundfont from './Soundfont';\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n\tfunction addFunction(func: Function, sig: string): number;\n\tfunction removeFunction(funcPtr: number): void;\n\tfunction addOnPostRun(cb: (Module: any) => void): void;\n}\n\ntype SettingsId = UniquePointerType<'settings_id'>;\ntype SynthId = UniquePointerType<'synth_id'>;\ntype PlayerId = UniquePointerType<'player_id'>;\n\nlet _module: any;\nlet _addFunction: (func: Function, sig: string) => number;\nlet _removeFunction: (funcPtr: number) => void;\nlet _fs: any;\n\n// wrapper to use String type\nlet fluid_settings_setint: (settings: SettingsId, name: string, val: number) => number;\nlet fluid_settings_setnum: (settings: SettingsId, name: string, val: number) => number;\nlet fluid_settings_setstr: (settings: SettingsId, name: string, str: string) => number;\nlet fluid_synth_error: undefined | ((synth: SynthId) => string);\nlet fluid_synth_sfload: (synth: SynthId, filename: string, reset_presets: number) => number;\nlet fluid_sequencer_register_client: (seq: PointerType, name: string, callback: number, data: number) => number;\n\nlet malloc: (size: number) => PointerType;\nlet free: (ptr: PointerType) => void;\n\nlet defaultMIDIEventCallback: (data: PointerType, event: MIDIEventType) => number;\n\nfunction bindFunctions() {\n\tif (fluid_synth_error) {\n\t\t// (already bound)\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t\t_addFunction = AudioWorkletGlobalScope.wasmAddFunction;\n\t\t_removeFunction = AudioWorkletGlobalScope.wasmRemoveFunction;\n\t} else if (typeof Module !== 'undefined') {\n\t\t_module = Module;\n\t\t_addFunction = addFunction;\n\t\t_removeFunction = removeFunction;\n\t} else {\n\t\tthrow new Error('wasm module is not available. libfluidsynth-*.js must be loaded.');\n\t}\n\t_fs = _module.FS;\n\n\t// wrapper to use String type\n\tfluid_settings_setint =\n\t\t_module.cwrap('fluid_settings_setint', 'number', ['number', 'string', 'number']);\n\tfluid_settings_setnum =\n\t\t_module.cwrap('fluid_settings_setnum', 'number', ['number', 'string', 'number']);\n\tfluid_settings_setstr =\n\t\t_module.cwrap('fluid_settings_setstr', 'number', ['number', 'string', 'string']);\n\tfluid_synth_error =\n\t\t_module.cwrap('fluid_synth_error', 'string', ['number']);\n\tfluid_synth_sfload =\n\t\t_module.cwrap('fluid_synth_sfload', 'number', ['number', 'string', 'number']);\n\tfluid_sequencer_register_client =\n\t\t_module.cwrap('fluid_sequencer_register_client', 'number', ['number', 'string', 'number', 'number']);\n\n\tmalloc = _module._malloc.bind(_module);\n\tfree = _module._free.bind(_module);\n\n\tdefaultMIDIEventCallback = _module._fluid_synth_handle_midi_event.bind(_module);\n}\n\nlet promiseWaitForInitialized: Promise<void> | undefined;\nfunction waitForInitialized() {\n\tif (promiseWaitForInitialized) {\n\t\treturn promiseWaitForInitialized;\n\t}\n\n\tlet mod: any;\n\tlet addOnPostRunFn: ((cb: (Module: any) => void) => void) | undefined;\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\tmod = AudioWorkletGlobalScope.wasmModule;\n\t\taddOnPostRunFn = AudioWorkletGlobalScope.addOnPostRun;\n\t} else if (typeof Module !== 'undefined') {\n\t\tmod = Module;\n\t\taddOnPostRunFn = typeof addOnPostRun !== 'undefined' ? addOnPostRun : undefined;\n\t} else {\n\t\treturn Promise.reject(new Error('wasm module is not available. libfluidsynth-*.js must be loaded.'));\n\t}\n\tif (mod.calledRun) {\n\t\tpromiseWaitForInitialized = Promise.resolve();\n\t\treturn promiseWaitForInitialized;\n\t}\n\tif (typeof addOnPostRunFn === 'undefined') {\n\t\tpromiseWaitForInitialized = new Promise((resolve) => {\n\t\t\tconst fn: (() => void) | undefined = _module.onRuntimeInitialized;\n\t\t\t_module.onRuntimeInitialized = () => {\n\t\t\t\tresolve();\n\t\t\t\tif (fn) {\n\t\t\t\t\tfn();\n\t\t\t\t}\n\t\t\t};\n\t\t});\n\t} else {\n\t\tpromiseWaitForInitialized = new Promise((resolve) => {\n\t\t\taddOnPostRunFn!(resolve);\n\t\t});\n\t}\n\treturn promiseWaitForInitialized;\n}\n\nfunction setBoolValueForSettings(settings: SettingsId, name: string, value: boolean | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setint(settings, name, value ? 1 : 0);\n\t}\n}\nfunction setIntValueForSettings(settings: SettingsId, name: string, value: number | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setint(settings, name, value);\n\t}\n}\nfunction setNumValueForSettings(settings: SettingsId, name: string, value: number | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setnum(settings, name, value);\n\t}\n}\nfunction setStrValueForSettings(settings: SettingsId, name: string, value: string | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setstr(settings, name, value);\n\t}\n}\n\nfunction getActiveVoiceCount(synth: SynthId): number {\n\tconst actualCount = _module._fluid_synth_get_active_voice_count(synth);\n\tif (!actualCount) {\n\t\treturn 0;\n\t}\n\n\t// FluidSynth may return incorrect value for active voice count,\n\t// so check internal data and correct it\n\n\t// check if the structure is not changed\n\t// for fluidsynth 2.0.x-2.1.x:\n\t//   140 === offset [synth->voice]\n\t//   144 === offset [synth->active_voice_count] for \n\t// for fluidsynth 2.2.x:\n\t//   144 === offset [synth->voice]\n\t//   148 === offset [synth->active_voice_count]\n\t// first check 2.1.x structure\n\tlet baseOffsetOfVoice = 140;\n\tlet offsetOfActiveVoiceCount = (synth + baseOffsetOfVoice + 4) >> 2;\n\tlet structActiveVoiceCount = _module.HEAPU32[offsetOfActiveVoiceCount];\n\tif (structActiveVoiceCount !== actualCount) {\n\t\t// add 4 for 2.2.x\n\t\tbaseOffsetOfVoice += 4;\n\t\toffsetOfActiveVoiceCount = (synth + baseOffsetOfVoice + 4) >> 2;\n\t\tstructActiveVoiceCount = _module.HEAPU32[offsetOfActiveVoiceCount];\n\t\tif (structActiveVoiceCount !== actualCount) {\n\t\t\t// unknown structure\n\t\t\tconst c = console;\n\t\t\tc.warn(\n\t\t\t\t'js-synthesizer: cannot check synthesizer internal data (may be changed)'\n\t\t\t);\n\t\t\treturn actualCount;\n\t\t}\n\t}\n\n\tconst voiceList = _module.HEAPU32[(synth + baseOffsetOfVoice) >> 2];\n\t// (voice should not be NULL)\n\tif (!voiceList || voiceList >= _module.HEAPU32.byteLength) {\n\t\t// unknown structure\n\t\tconst c = console;\n\t\tc.warn(\n\t\t\t'js-synthesizer: cannot check synthesizer internal data (may be changed)'\n\t\t);\n\t\treturn actualCount;\n\t}\n\n\t// count of internal voice data is restricted to polyphony value\n\tconst voiceCount = _module._fluid_synth_get_polyphony(synth);\n\tlet isRunning = false;\n\tfor (let i = 0; i < voiceCount; ++i) {\n\t\t// auto voice = voiceList[i]\n\t\tconst voice = _module.HEAPU32[(voiceList >> 2) + i];\n\t\tif (!voice) {\n\t\t\tcontinue;\n\t\t}\n\t\t// offset [voice->status]\n\t\tconst status = _module.HEAPU8[voice + 4];\n\t\t// 4: FLUID_VOICE_OFF\n\t\tif (status !== 4) {\n\t\t\tisRunning = true;\n\t\t\tbreak;\n\t\t}\n\t}\n\tif (!isRunning) {\n\t\tif (structActiveVoiceCount !== 0) {\n\t\t\tconst c = console;\n\t\t\tc.warn(\n\t\t\t\t'js-synthesizer: Active voice count is not zero, but all voices are off:',\n\t\t\t\tstructActiveVoiceCount,\n\t\t\t);\n\t\t}\n\t\t_module.HEAPU32[offsetOfActiveVoiceCount] = 0;\n\t\treturn 0;\n\t}\n\n\treturn actualCount;\n}\n\nfunction makeRandomFileName(type: string, ext: string) {\n\treturn `/${type}-r${Math.random() * 65535}-${Math.random() * 65535}${ext}`;\n}\n\n/** Hook callback function type */\nexport interface HookMIDIEventCallback {\n\t/**\n\t * Hook callback function type.\n\t * @param synth the base synthesizer instance\n\t * @param eventType MIDI event type (e.g. 0x90 is note-on event)\n\t * @param eventData detailed event data\n\t * @param param parameter data passed to the registration method\n\t * @return true if the event data is processed, or false if the default processing is necessary\n\t */\n\t(synth: Synthesizer, eventType: number, eventData: IMIDIEvent, param: any): boolean;\n}\n\n/** Client callback function type for sequencer object */\nexport interface SequencerClientCallback {\n\t/**\n\t * Client callback function type for sequencer object.\n\t * @param time the sequencer tick value\n\t * @param eventType sequencer event type\n\t * @param event actual event data (can only be used in this callback function)\n\t * @param sequencer the base sequencer object\n\t * @param param parameter data passed to the registration method\n\t */\n\t(time: number, eventType: SequencerEventType, event: ISequencerEventData, sequencer: ISequencer, param: number): void;\n}\n\nfunction makeMIDIEventCallback(synth: Synthesizer, cb: HookMIDIEventCallback, param: any) {\n\treturn (data: PointerType, event: MIDIEventType): number => {\n\t\tconst t = _module._fluid_midi_event_get_type(event);\n\t\tif (cb(synth, t, new MIDIEvent(event, _module), param)) {\n\t\t\treturn 0;\n\t\t}\n\t\treturn _module._fluid_synth_handle_midi_event(data, event);\n\t};\n}\n\n/** Default implementation of ISynthesizer */\nexport default class Synthesizer implements ISynthesizer {\n\t/** @internal */\n\tprivate _settings: SettingsId;\n\t/** @internal */\n\tprivate _synth: SynthId;\n\t/** @internal */\n\tprivate _player: PlayerId;\n\t/** @internal */\n\tprivate _playerPlaying: boolean;\n\t/** @internal */\n\tprivate _playerDefer:\n\t\t| undefined\n\t\t| {\n\t\t\t\tpromise: Promise<void>;\n\t\t\t\tresolve: () => void;\n\t\t  };\n\t/** @internal */\n\tprivate _playerCallbackPtr: number | null;\n\t/** @internal */\n\tprivate _fluidSynthCallback: PointerType | null;\n\n\t/** @internal */\n\tprivate _buffer: PointerType;\n\t/** @internal */\n\tprivate _bufferSize: number;\n\t/** @internal */\n\tprivate _numPtr: PointerType;\n\n\t/** @internal */\n\tprivate _gain: number;\n\n\tconstructor() {\n\t\tbindFunctions();\n\n\t\tthis._settings = INVALID_POINTER;\n\t\tthis._synth = INVALID_POINTER;\n\t\tthis._player = INVALID_POINTER;\n\t\tthis._playerPlaying = false;\n\t\tthis._playerCallbackPtr = null;\n\t\tthis._fluidSynthCallback = null;\n\n\t\tthis._buffer = INVALID_POINTER;\n\t\tthis._bufferSize = 0;\n\t\tthis._numPtr = INVALID_POINTER;\n\n\t\tthis._gain = SynthesizerDefaultValues.Gain;\n\t}\n\n\t/** Return the promise object that resolves when WebAssembly has been initialized */\n\tpublic static waitForWasmInitialized(): Promise<void> {\n\t\treturn waitForInitialized();\n\t}\n\n\tpublic isInitialized() {\n\t\treturn this._synth !== INVALID_POINTER;\n\t}\n\n\t/** Return the raw synthesizer instance value (pointer for libfluidsynth). */\n\tpublic getRawSynthesizer(): number {\n\t\treturn this._synth;\n\t}\n\n\tpublic createAudioNode(\n\t\tcontext: AudioContext,\n\t\tframeSize?: number\n\t): AudioNode {\n\t\tconst node = context.createScriptProcessor(frameSize, 0, 2);\n\t\tnode.addEventListener(\"audioprocess\", (ev) => {\n\t\t\tthis.render(ev.outputBuffer);\n\t\t});\n\t\treturn node;\n\t}\n\n\tpublic init(sampleRate: number, settings?: SynthesizerSettings) {\n\t\tthis.close();\n\n\t\tconst set = (this._settings = _module._new_fluid_settings());\n\t\tfluid_settings_setnum(set, \"synth.sample-rate\", sampleRate);\n\t\tif (settings) {\n\t\t\tif (typeof settings.initialGain !== \"undefined\") {\n\t\t\t\tthis._gain = settings.initialGain;\n\t\t\t}\n\t\t\tsetBoolValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.active\",\n\t\t\t\tsettings.chorusActive\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.depth\",\n\t\t\t\tsettings.chorusDepth\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.level\",\n\t\t\t\tsettings.chorusLevel\n\t\t\t);\n\t\t\tsetIntValueForSettings(set, \"synth.chorus.nr\", settings.chorusNr);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.speed\",\n\t\t\t\tsettings.chorusSpeed\n\t\t\t);\n\t\t\tsetIntValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.midi-channels\",\n\t\t\t\tsettings.midiChannelCount\n\t\t\t);\n\t\t\tsetStrValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.midi-bank-select\",\n\t\t\t\tsettings.midiBankSelect\n\t\t\t);\n\t\t\tsetIntValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.min-note-length\",\n\t\t\t\tsettings.minNoteLength\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.age\",\n\t\t\t\tsettings.overflowAge\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.important\",\n\t\t\t\tsettings.overflowImportantValue\n\t\t\t);\n\t\t\tif (typeof settings.overflowImportantChannels !== \"undefined\") {\n\t\t\t\tfluid_settings_setstr(\n\t\t\t\t\tset,\n\t\t\t\t\t\"synth.overflow.important-channels\",\n\t\t\t\t\tsettings.overflowImportantChannels.join(\",\")\n\t\t\t\t);\n\t\t\t}\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.percussion\",\n\t\t\t\tsettings.overflowPercussion\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.released\",\n\t\t\t\tsettings.overflowReleased\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.sustained\",\n\t\t\t\tsettings.overflowSustained\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.volume\",\n\t\t\t\tsettings.overflowVolume\n\t\t\t);\n\t\t\tsetIntValueForSettings(set, \"synth.polyphony\", settings.polyphony);\n\t\t\tsetBoolValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.active\",\n\t\t\t\tsettings.reverbActive\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.damp\",\n\t\t\t\tsettings.reverbDamp\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.level\",\n\t\t\t\tsettings.reverbLevel\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.room-size\",\n\t\t\t\tsettings.reverbRoomSize\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.width\",\n\t\t\t\tsettings.reverbWidth\n\t\t\t);\n\t\t}\n\t\tfluid_settings_setnum(set, \"synth.gain\", this._gain);\n\n\t\tthis._synth = _module._new_fluid_synth(this._settings);\n\n\t\tthis._numPtr = malloc(8);\n\t}\n\n\tpublic close() {\n\t\tif (this._synth === INVALID_POINTER) {\n\t\t\treturn;\n\t\t}\n\t\tthis._closePlayer();\n\t\t_module._delete_fluid_synth(this._synth);\n\t\tthis._synth = INVALID_POINTER;\n\t\t_module._delete_fluid_settings(this._settings);\n\t\tthis._settings = INVALID_POINTER;\n\t\tfree(this._numPtr);\n\t\tthis._numPtr = INVALID_POINTER;\n\t}\n\n\tpublic isPlaying() {\n\t\treturn (\n\t\t\tthis._synth !== INVALID_POINTER &&\n\t\t\tgetActiveVoiceCount(this._synth) > 0\n\t\t);\n\t}\n\n\tpublic setInterpolation(value: InterpolationValues, channel?: number) {\n\t\tthis.ensureInitialized();\n\t\tif (typeof channel === \"undefined\") {\n\t\t\tchannel = -1;\n\t\t}\n\t\t_module._fluid_synth_set_interp_method(this._synth, channel, value);\n\t}\n\n\tpublic getGain() {\n\t\treturn this._gain;\n\t}\n\n\tpublic setGain(gain: number) {\n\t\tthis.ensureInitialized();\n\t\t_module._fluid_synth_set_gain(this._synth, gain);\n\t\tthis._gain = _module._fluid_synth_get_gain(this._synth);\n\t}\n\n\tpublic setChannelType(channel: number, isDrum: boolean) {\n\t\tthis.ensureInitialized();\n\t\t// CHANNEL_TYPE_MELODIC = 0, CHANNEL_TYPE_DRUM = 1\n\t\t_module._fluid_synth_set_channel_type(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tisDrum ? 1 : 0\n\t\t);\n\t}\n\n\tpublic waitForVoicesStopped() {\n\t\treturn this.flushFramesAsync();\n\t}\n\n\tpublic loadSFont(bin: ArrayBuffer) {\n\t\tthis.ensureInitialized();\n\n\t\tconst name = makeRandomFileName(\"sfont\", \".sf2\");\n\t\tconst ub = new Uint8Array(bin);\n\n\t\t_fs.writeFile(name, ub);\n\t\tconst sfont = fluid_synth_sfload(this._synth, name, 1);\n\t\t_fs.unlink(name);\n\t\treturn sfont === -1\n\t\t\t? Promise.reject(new Error(fluid_synth_error!(this._synth)))\n\t\t\t: Promise.resolve(sfont);\n\t}\n\n\tpublic unloadSFont(id: number) {\n\t\tthis.ensureInitialized();\n\t\tthis.stopPlayer();\n\t\tthis.flushFramesSync();\n\n\t\t_module._fluid_synth_sfunload(this._synth, id, 1);\n\t}\n\n\tpublic unloadSFontAsync(id: number) {\n\t\t// not throw with Promise.reject\n\t\tthis.ensureInitialized();\n\t\tthis.stopPlayer();\n\t\treturn this.flushFramesAsync().then(() => {\n\t\t\t_module._fluid_synth_sfunload(this._synth, id, 1);\n\t\t});\n\t}\n\n\t/**\n\t * Returns the `Soundfont` instance for specified SoundFont.\n\t * @param sfontId loaded SoundFont id ({@link loadSFont} returns this)\n\t * @return `Soundfont` instance or `null` if `sfontId` is not valid or loaded\n\t */\n\tpublic getSFontObject(sfontId: number): Soundfont | null {\n\t\treturn Soundfont.getSoundfontById(this, sfontId);\n\t}\n\n\tpublic getSFontBankOffset(id: number) {\n\t\tthis.ensureInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_synth_get_bank_offset(this._synth, id) as number\n\t\t);\n\t}\n\tpublic setSFontBankOffset(id: number, offset: number) {\n\t\tthis.ensureInitialized();\n\t\t_module._fluid_synth_set_bank_offset(this._synth, id, offset);\n\t}\n\n\tpublic render(outBuffer: AudioBuffer | Float32Array[]) {\n\t\tconst frameCount =\n\t\t\t\"numberOfChannels\" in outBuffer\n\t\t\t\t? outBuffer.length\n\t\t\t\t: outBuffer[0].length;\n\t\tconst channels =\n\t\t\t\"numberOfChannels\" in outBuffer\n\t\t\t\t? outBuffer.numberOfChannels\n\t\t\t\t: outBuffer.length;\n\t\tconst sizePerChannel = 4 * frameCount;\n\t\tconst totalSize = sizePerChannel * 2;\n\t\tif (this._bufferSize < totalSize) {\n\t\t\tif (this._buffer !== INVALID_POINTER) {\n\t\t\t\tfree(this._buffer);\n\t\t\t}\n\t\t\tthis._buffer = malloc(totalSize);\n\t\t\tthis._bufferSize = totalSize;\n\t\t}\n\n\t\tconst memLeft = this._buffer;\n\t\tconst memRight = ((this._buffer as number) +\n\t\t\tsizePerChannel) as PointerType;\n\t\tthis.renderRaw(memLeft, memRight, frameCount);\n\n\t\tconst aLeft = new Float32Array(\n\t\t\t_module.HEAPU8.buffer,\n\t\t\tmemLeft,\n\t\t\tframeCount\n\t\t);\n\t\tconst aRight =\n\t\t\tchannels >= 2\n\t\t\t\t? new Float32Array(_module.HEAPU8.buffer, memRight, frameCount)\n\t\t\t\t: null;\n\t\tif (\"numberOfChannels\" in outBuffer) {\n\t\t\tif (outBuffer.copyToChannel) {\n\t\t\t\toutBuffer.copyToChannel(aLeft, 0, 0);\n\t\t\t\tif (aRight) {\n\t\t\t\t\toutBuffer.copyToChannel(aRight, 1, 0);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// copyToChannel API not exist in Safari AudioBuffer\n\t\t\t\tconst leftData = outBuffer.getChannelData(0);\n\t\t\t\taLeft.forEach((val, i) => (leftData[i] = val));\n\t\t\t\tif (aRight) {\n\t\t\t\t\tconst rightData = outBuffer.getChannelData(1);\n\t\t\t\t\taRight.forEach((val, i) => (rightData[i] = val));\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\toutBuffer[0].set(aLeft);\n\t\t\tif (aRight) {\n\t\t\t\toutBuffer[1].set(aRight);\n\t\t\t}\n\t\t}\n\n\t\t// check and update player status\n\t\tthis.isPlayerPlaying();\n\t}\n\n\tpublic midiNoteOn(chan: number, key: number, vel: number) {\n\t\t_module._fluid_synth_noteon(this._synth, chan, key, vel);\n\t}\n\tpublic midiNoteOff(chan: number, key: number) {\n\t\t_module._fluid_synth_noteoff(this._synth, chan, key);\n\t}\n\tpublic midiKeyPressure(chan: number, key: number, val: number) {\n\t\t_module._fluid_synth_key_pressure(this._synth, chan, key, val);\n\t}\n\tpublic midiControl(chan: number, ctrl: number, val: number) {\n\t\t_module._fluid_synth_cc(this._synth, chan, ctrl, val);\n\t}\n\tpublic midiProgramChange(chan: number, prognum: number) {\n\t\t_module._fluid_synth_program_change(this._synth, chan, prognum);\n\t}\n\tpublic midiChannelPressure(chan: number, val: number) {\n\t\t_module._fluid_synth_channel_pressure(this._synth, chan, val);\n\t}\n\tpublic midiPitchBend(chan: number, val: number) {\n\t\t_module._fluid_synth_pitch_bend(this._synth, chan, val);\n\t}\n\tpublic midiSysEx(data: Uint8Array) {\n\t\tconst len = data.byteLength;\n\t\tconst mem = malloc(len);\n\t\t_module.HEAPU8.set(data, mem);\n\t\t_module._fluid_synth_sysex(\n\t\t\tthis._synth,\n\t\t\tmem,\n\t\t\tlen,\n\t\t\tINVALID_POINTER,\n\t\t\tINVALID_POINTER,\n\t\t\tINVALID_POINTER,\n\t\t\t0\n\t\t);\n\t\tfree(mem);\n\t}\n\n\tpublic midiPitchWheelSensitivity(chan: number, val: number) {\n\t\t_module._fluid_synth_pitch_wheel_sens(this._synth, chan, val);\n\t}\n\tpublic midiBankSelect(chan: number, bank: number) {\n\t\t_module._fluid_synth_bank_select(this._synth, chan, bank);\n\t}\n\tpublic midiSFontSelect(chan: number, sfontId: number) {\n\t\t_module._fluid_synth_sfont_select(this._synth, chan, sfontId);\n\t}\n\tpublic midiProgramSelect(\n\t\tchan: number,\n\t\tsfontId: number,\n\t\tbank: number,\n\t\tpresetNum: number\n\t) {\n\t\t_module._fluid_synth_program_select(\n\t\t\tthis._synth,\n\t\t\tchan,\n\t\t\tsfontId,\n\t\t\tbank,\n\t\t\tpresetNum\n\t\t);\n\t}\n\tpublic midiUnsetProgram(chan: number) {\n\t\t_module._fluid_synth_unset_program(this._synth, chan);\n\t}\n\tpublic midiProgramReset() {\n\t\t_module._fluid_synth_program_reset(this._synth);\n\t}\n\tpublic midiSystemReset() {\n\t\t_module._fluid_synth_system_reset(this._synth);\n\t}\n\tpublic midiAllNotesOff(chan?: number) {\n\t\t_module._fluid_synth_all_notes_off(\n\t\t\tthis._synth,\n\t\t\ttypeof chan === \"undefined\" ? -1 : chan\n\t\t);\n\t}\n\tpublic midiAllSoundsOff(chan?: number) {\n\t\t_module._fluid_synth_all_sounds_off(\n\t\t\tthis._synth,\n\t\t\ttypeof chan === \"undefined\" ? -1 : chan\n\t\t);\n\t}\n\tpublic midiSetChannelType(chan: number, isDrum: boolean) {\n\t\t// CHANNEL_TYPE_MELODIC = 0\n\t\t// CHANNEL_TYPE_DRUM = 1\n\t\t_module._fluid_synth_set_channel_type(\n\t\t\tthis._synth,\n\t\t\tchan,\n\t\t\tisDrum ? 1 : 0\n\t\t);\n\t}\n\n\t/**\n\t * Set reverb parameters to the synthesizer.\n\t */\n\tpublic setReverb(\n\t\troomsize: number,\n\t\tdamping: number,\n\t\twidth: number,\n\t\tlevel: number\n\t) {\n\t\t_module._fluid_synth_set_reverb(\n\t\t\tthis._synth,\n\t\t\troomsize,\n\t\t\tdamping,\n\t\t\twidth,\n\t\t\tlevel\n\t\t);\n\t}\n\t/**\n\t * Set reverb roomsize parameter to the synthesizer.\n\t */\n\tpublic setReverbRoomsize(roomsize: number) {\n\t\t_module._fluid_synth_set_reverb_roomsize(this._synth, roomsize);\n\t}\n\t/**\n\t * Set reverb damping parameter to the synthesizer.\n\t */\n\tpublic setReverbDamp(damping: number) {\n\t\t_module._fluid_synth_set_reverb_damp(this._synth, damping);\n\t}\n\t/**\n\t * Set reverb width parameter to the synthesizer.\n\t */\n\tpublic setReverbWidth(width: number) {\n\t\t_module._fluid_synth_set_reverb_width(this._synth, width);\n\t}\n\t/**\n\t * Set reverb level to the synthesizer.\n\t */\n\tpublic setReverbLevel(level: number) {\n\t\t_module._fluid_synth_set_reverb_level(this._synth, level);\n\t}\n\t/**\n\t * Enable or disable reverb effect of the synthesizer.\n\t */\n\tpublic setReverbOn(on: boolean) {\n\t\t_module._fluid_synth_set_reverb_on(this._synth, on ? 1 : 0);\n\t}\n\t/**\n\t * Get reverb roomsize parameter of the synthesizer.\n\t */\n\tpublic getReverbRoomsize(): number {\n\t\treturn _module._fluid_synth_get_reverb_roomsize(this._synth);\n\t}\n\t/**\n\t * Get reverb damping parameter of the synthesizer.\n\t */\n\tpublic getReverbDamp(): number {\n\t\treturn _module._fluid_synth_get_reverb_damp(this._synth);\n\t}\n\t/**\n\t * Get reverb level of the synthesizer.\n\t */\n\tpublic getReverbLevel(): number {\n\t\treturn _module._fluid_synth_get_reverb_level(this._synth);\n\t}\n\t/**\n\t * Get reverb width parameter of the synthesizer.\n\t */\n\tpublic getReverbWidth(): number {\n\t\treturn _module._fluid_synth_get_reverb_width(this._synth);\n\t}\n\n\t/**\n\t * Set chorus parameters to the synthesizer.\n\t */\n\tpublic setChorus(\n\t\tvoiceCount: number,\n\t\tlevel: number,\n\t\tspeed: number,\n\t\tdepthMillisec: number,\n\t\ttype: ChorusModulation\n\t) {\n\t\t_module._fluid_synth_set_chorus(\n\t\t\tthis._synth,\n\t\t\tvoiceCount,\n\t\t\tlevel,\n\t\t\tspeed,\n\t\t\tdepthMillisec,\n\t\t\ttype\n\t\t);\n\t}\n\t/**\n\t * Set chorus voice count parameter to the synthesizer.\n\t */\n\tpublic setChorusVoiceCount(voiceCount: number) {\n\t\t_module._fluid_synth_set_chorus_nr(this._synth, voiceCount);\n\t}\n\t/**\n\t * Set chorus level parameter to the synthesizer.\n\t */\n\tpublic setChorusLevel(level: number) {\n\t\t_module._fluid_synth_set_chorus_level(this._synth, level);\n\t}\n\t/**\n\t * Set chorus speed parameter to the synthesizer.\n\t */\n\tpublic setChorusSpeed(speed: number) {\n\t\t_module._fluid_synth_set_chorus_speed(this._synth, speed);\n\t}\n\t/**\n\t * Set chorus depth parameter to the synthesizer.\n\t */\n\tpublic setChorusDepth(depthMillisec: number) {\n\t\t_module._fluid_synth_set_chorus_depth(this._synth, depthMillisec);\n\t}\n\t/**\n\t * Set chorus modulation type to the synthesizer.\n\t */\n\tpublic setChorusType(type: ChorusModulation) {\n\t\t_module._fluid_synth_set_chorus_type(this._synth, type);\n\t}\n\t/**\n\t * Enable or disable chorus effect of the synthesizer.\n\t */\n\tpublic setChorusOn(on: boolean) {\n\t\t_module._fluid_synth_set_chorus_on(this._synth, on ? 1 : 0);\n\t}\n\t/**\n\t * Get chorus voice count of the synthesizer.\n\t */\n\tpublic getChorusVoiceCount(): number {\n\t\treturn _module._fluid_synth_get_chorus_nr(this._synth);\n\t}\n\t/**\n\t * Get chorus level of the synthesizer.\n\t */\n\tpublic getChorusLevel(): number {\n\t\treturn _module._fluid_synth_get_chorus_level(this._synth);\n\t}\n\t/**\n\t * Get chorus speed of the synthesizer.\n\t */\n\tpublic getChorusSpeed(): number {\n\t\treturn _module._fluid_synth_get_chorus_speed(this._synth);\n\t}\n\t/**\n\t * Get chorus depth (in milliseconds) of the synthesizer.\n\t */\n\tpublic getChorusDepth(): number {\n\t\treturn _module._fluid_synth_get_chorus_depth(this._synth);\n\t}\n\t/**\n\t * Get chorus modulation type of the synthesizer.\n\t */\n\tpublic getChorusType(): ChorusModulation {\n\t\treturn _module._fluid_synth_get_chorus_type(this._synth);\n\t}\n\n\t/**\n\t * Get generator value assigned to the MIDI channel.\n\t * @param channel MIDI channel number\n\t * @param param generator ID\n\t * @return a value related to the generator\n\t */\n\tpublic getGenerator(channel: number, param: GeneratorTypes): number {\n\t\treturn _module._fluid_synth_get_gen(this._synth, channel, param);\n\t}\n\t/**\n\t * Set generator value assigned to the MIDI channel.\n\t * @param channel MIDI channel number\n\t * @param param generator ID\n\t * @param value a value related to the generator\n\t */\n\tpublic setGenerator(channel: number, param: GeneratorTypes, value: number) {\n\t\t_module._fluid_synth_set_gen(this._synth, channel, param, value);\n\t}\n\t/**\n\t * Return the current legato mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return legato mode\n\t */\n\tpublic getLegatoMode(channel: number) {\n\t\t_module._fluid_synth_get_legato_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as LegatoMode;\n\t}\n\t/**\n\t * Set the current legato mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param mode legato mode\n\t */\n\tpublic setLegatoMode(channel: number, mode: LegatoMode) {\n\t\t_module._fluid_synth_set_legato_mode(this._synth, channel, mode);\n\t}\n\t/**\n\t * Return the current portamento mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return portamento mode\n\t */\n\tpublic getPortamentoMode(channel: number) {\n\t\t_module._fluid_synth_get_portamento_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as PortamentoMode;\n\t}\n\t/**\n\t * Set the current portamento mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param mode portamento mode\n\t */\n\tpublic setPortamentoMode(channel: number, mode: PortamentoMode) {\n\t\t_module._fluid_synth_set_portamento_mode(this._synth, channel, mode);\n\t}\n\t/**\n\t * Return the current breath mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return breath mode (BreathFlags)\n\t */\n\tpublic getBreathMode(channel: number) {\n\t\t_module._fluid_synth_get_breath_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as number;\n\t}\n\t/**\n\t * Set the current breath mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param flags breath mode flags (BreathFlags)\n\t */\n\tpublic setBreathMode(channel: number, flags: number) {\n\t\t_module._fluid_synth_set_breath_mode(this._synth, channel, flags);\n\t}\n\n\t////////////////////////////////////////////////////////////////////////////\n\n\tpublic resetPlayer() {\n\t\treturn new Promise<void>((resolve) => {\n\t\t\tthis._initPlayer();\n\t\t\tresolve();\n\t\t});\n\t}\n\n\tpublic closePlayer() {\n\t\tthis._closePlayer();\n\t}\n\n\t/** @internal */\n\tprivate _initPlayer() {\n\t\tthis._closePlayer();\n\n\t\tconst player = _module._new_fluid_player(this._synth);\n\t\tthis._player = player;\n\t\tif (player !== INVALID_POINTER) {\n\t\t\tif (this._fluidSynthCallback === null) {\n\t\t\t\t// hacky retrieve 'fluid_synth_handle_midi_event' callback pointer\n\t\t\t\t// * 'playback_callback' is filled with 'fluid_synth_handle_midi_event' by default.\n\t\t\t\t// * 'playback_userdata' is filled with the synthesizer pointer by default\n\t\t\t\tconst funcPtr: PointerType =\n\t\t\t\t\t_module.HEAPU32[((player as number) + 588) >> 2]; // _fluid_player_t::playback_callback\n\t\t\t\tconst synthPtr: SynthId =\n\t\t\t\t\t_module.HEAPU32[((player as number) + 592) >> 2]; // _fluid_player_t::playback_userdata\n\t\t\t\tif (synthPtr === this._synth) {\n\t\t\t\t\tthis._fluidSynthCallback = funcPtr;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tthrow new Error(\"Out of memory\");\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate _closePlayer() {\n\t\tconst p = this._player;\n\t\tif (p === INVALID_POINTER) {\n\t\t\treturn;\n\t\t}\n\t\tthis.stopPlayer();\n\t\t_module._delete_fluid_player(p);\n\t\tthis._player = INVALID_POINTER;\n\t\tthis._playerCallbackPtr = null;\n\t}\n\n\tpublic isPlayerPlaying() {\n\t\tif (this._playerPlaying) {\n\t\t\tconst status = _module._fluid_player_get_status(this._player);\n\t\t\tif (status === 1 /*FLUID_PLAYER_PLAYING*/) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tthis.stopPlayer();\n\t\t}\n\t\treturn false;\n\t}\n\n\tpublic addSMFDataToPlayer(bin: ArrayBuffer) {\n\t\tthis.ensurePlayerInitialized();\n\t\tconst len = bin.byteLength;\n\t\tconst mem = malloc(len);\n\t\t_module.HEAPU8.set(new Uint8Array(bin), mem);\n\t\tconst r: number = _module._fluid_player_add_mem(this._player, mem, len);\n\t\tfree(mem);\n\t\treturn r !== -1\n\t\t\t? Promise.resolve()\n\t\t\t: Promise.reject(new Error(fluid_synth_error!(this._synth)));\n\t}\n\n\tpublic playPlayer() {\n\t\tthis.ensurePlayerInitialized();\n\t\tif (this._playerPlaying) {\n\t\t\tthis.stopPlayer();\n\t\t}\n\n\t\tif (_module._fluid_player_play(this._player) === -1) {\n\t\t\treturn Promise.reject(new Error(fluid_synth_error!(this._synth)));\n\t\t}\n\t\tthis._playerPlaying = true;\n\t\tlet resolver = () => {};\n\t\tconst p = new Promise<void>((resolve) => {\n\t\t\tresolver = resolve;\n\t\t});\n\t\tthis._playerDefer = {\n\t\t\tpromise: p,\n\t\t\tresolve: resolver,\n\t\t};\n\t\treturn Promise.resolve();\n\t}\n\n\tpublic stopPlayer() {\n\t\tconst p = this._player;\n\t\tif (p === INVALID_POINTER || !this._playerPlaying) {\n\t\t\treturn;\n\t\t}\n\t\t_module._fluid_player_stop(p);\n\t\t_module._fluid_player_join(p);\n\t\t_module._fluid_synth_all_sounds_off(this._synth, -1);\n\t\tif (this._playerDefer) {\n\t\t\tthis._playerDefer.resolve();\n\t\t\tthis._playerDefer = void 0;\n\t\t}\n\t\tthis._playerPlaying = false;\n\t}\n\n\tpublic retrievePlayerCurrentTick(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_current_tick(this._player)\n\t\t);\n\t}\n\tpublic retrievePlayerTotalTicks(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_total_ticks(this._player)\n\t\t);\n\t}\n\tpublic retrievePlayerBpm(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(_module._fluid_player_get_bpm(this._player));\n\t}\n\tpublic retrievePlayerMIDITempo(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_midi_tempo(this._player)\n\t\t);\n\t}\n\tpublic seekPlayer(ticks: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_seek(this._player, ticks);\n\t}\n\tpublic setPlayerLoop(loopTimes: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_set_loop(this._player, loopTimes);\n\t}\n\tpublic setPlayerTempo(tempoType: PlayerSetTempoType, tempo: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_set_tempo(this._player, tempoType, tempo);\n\t}\n\n\t/**\n\t * Hooks MIDI events sent by the player.\n\t * initPlayer() must be called before calling this method.\n\t * @param callback hook callback function, or null to unhook\n\t * @param param any additional data passed to the callback\n\t */\n\tpublic hookPlayerMIDIEvents(\n\t\tcallback: HookMIDIEventCallback | null,\n\t\tparam?: any\n\t) {\n\t\tthis.ensurePlayerInitialized();\n\n\t\tconst oldPtr = this._playerCallbackPtr;\n\t\tif (oldPtr === null && callback === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst newPtr =\n\t\t\t// if callback is specified, add function\n\t\t\tcallback !== null\n\t\t\t\t? _addFunction(\n\t\t\t\t\t\tmakeMIDIEventCallback(this, callback, param),\n\t\t\t\t\t\t\"iii\"\n\t\t\t\t  )\n\t\t\t\t: // if _fluidSynthCallback is filled, set null to use it for reset callback\n\t\t\t\t// if not, add function defaultMIDIEventCallback for reset\n\t\t\t\tthis._fluidSynthCallback !== null\n\t\t\t\t? null\n\t\t\t\t: _addFunction(defaultMIDIEventCallback, \"iii\");\n\t\t// the third parameter of 'fluid_player_set_playback_callback' should be 'fluid_synth_t*'\n\t\tif (oldPtr !== null && newPtr !== null) {\n\t\t\t// (using defaultMIDIEventCallback also comes here)\n\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\tthis._player,\n\t\t\t\tnewPtr,\n\t\t\t\tthis._synth\n\t\t\t);\n\t\t\t_removeFunction(oldPtr);\n\t\t} else {\n\t\t\tif (newPtr === null) {\n\t\t\t\t// newPtr === null --> use _fluidSynthCallback\n\t\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\t\tthis._player,\n\t\t\t\t\tthis._fluidSynthCallback!,\n\t\t\t\t\tthis._synth\n\t\t\t\t);\n\t\t\t\t_removeFunction(oldPtr!);\n\t\t\t} else {\n\t\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\t\tthis._player,\n\t\t\t\t\tnewPtr,\n\t\t\t\t\tthis._synth\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t\tthis._playerCallbackPtr = newPtr;\n\t}\n\n\t/** @internal */\n\tprivate ensureInitialized() {\n\t\tif (this._synth === INVALID_POINTER) {\n\t\t\tthrow new Error(\"Synthesizer is not initialized\");\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate ensurePlayerInitialized() {\n\t\tthis.ensureInitialized();\n\t\tif (this._player === INVALID_POINTER) {\n\t\t\tthis._initPlayer();\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate renderRaw(\n\t\tmemLeft: PointerType,\n\t\tmemRight: PointerType,\n\t\tframeCount: number\n\t) {\n\t\t_module._fluid_synth_write_float(\n\t\t\tthis._synth,\n\t\t\tframeCount,\n\t\t\tmemLeft,\n\t\t\t0,\n\t\t\t1,\n\t\t\tmemRight,\n\t\t\t0,\n\t\t\t1\n\t\t);\n\t}\n\n\t/** @internal */\n\tprivate flushFramesSync() {\n\t\tconst frameCount = 65536;\n\t\tconst size = 4 * frameCount;\n\t\tconst mem = malloc(size * 2);\n\t\tconst memLeft = mem;\n\t\tconst memRight = ((mem as number) + size) as PointerType;\n\t\twhile (this.isPlaying()) {\n\t\t\tthis.renderRaw(memLeft, memRight, frameCount);\n\t\t}\n\t\tfree(mem);\n\t}\n\n\t/** @internal */\n\tprivate flushFramesAsync() {\n\t\tif (!this.isPlaying()) {\n\t\t\treturn Promise.resolve();\n\t\t}\n\t\tconst frameCount = 65536;\n\t\tconst size = 4 * frameCount;\n\t\tconst mem = malloc(size * 2);\n\t\tconst memLeft = mem;\n\t\tconst memRight = ((mem as number) + size) as PointerType;\n\t\tconst nextFrame =\n\t\t\ttypeof setTimeout !== \"undefined\"\n\t\t\t\t? () => {\n\t\t\t\t\t\treturn new Promise<void>((resolve) =>\n\t\t\t\t\t\t\tsetTimeout(resolve, 0)\n\t\t\t\t\t\t);\n\t\t\t\t  }\n\t\t\t\t: () => {\n\t\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t  };\n\t\tfunction head(): Promise<void> {\n\t\t\treturn nextFrame().then(tail);\n\t\t}\n\t\tconst self = this;\n\t\tfunction tail(): Promise<void> {\n\t\t\tif (!self.isPlaying()) {\n\t\t\t\tfree(mem);\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\t\t\tself.renderRaw(memLeft, memRight, frameCount);\n\t\t\treturn head();\n\t\t}\n\t\treturn head();\n\t}\n\n\tpublic waitForPlayerStopped() {\n\t\treturn this._playerDefer\n\t\t\t? this._playerDefer.promise\n\t\t\t: Promise.resolve();\n\t}\n\n\t/**\n\t * Create the sequencer object for this class.\n\t */\n\tpublic static createSequencer(): Promise<ISequencer> {\n\t\tbindFunctions();\n\t\tconst seq = new Sequencer();\n\t\treturn seq._initialize().then(() => seq);\n\t}\n\n\t/**\n\t * Registers the user-defined client to the sequencer.\n\t * The client can receive events in the time from sequencer process.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param name the client name\n\t * @param callback the client callback function that processes event data\n\t * @param param additional parameter passed to the callback\n\t * @return registered sequencer client id (can be passed to seq.unregisterClient())\n\t */\n\tpublic static registerSequencerClient(\n\t\tseq: ISequencer,\n\t\tname: string,\n\t\tcallback: SequencerClientCallback,\n\t\tparam: number\n\t): number {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tconst ptr = _addFunction(\n\t\t\t(time: number, ev: PointerType, _seq: number, data: number) => {\n\t\t\t\tconst e = new SequencerEventData(ev, _module);\n\t\t\t\tconst type: SequencerEventType =\n\t\t\t\t\t_module._fluid_event_get_type(ev);\n\t\t\t\tcallback(time, type, e, seq, data);\n\t\t\t},\n\t\t\t\"viiii\"\n\t\t);\n\t\tconst r = fluid_sequencer_register_client(\n\t\t\tseq.getRaw(),\n\t\t\tname,\n\t\t\tptr,\n\t\t\tparam\n\t\t);\n\t\tif (r !== -1) {\n\t\t\tseq._clientFuncMap[r] = ptr;\n\t\t}\n\t\treturn r;\n\t}\n\n\t/**\n\t * Send sequencer event immediately to the specific client.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param clientId registered client id (-1 for registered synthesizer)\n\t * @param event event data\n\t */\n\tpublic static sendEventToClientNow(\n\t\tseq: ISequencer,\n\t\tclientId: number,\n\t\tevent: SequencerEvent\n\t): void {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tseq.sendEventToClientNow(clientId, event);\n\t}\n\t/**\n\t * (Re-)send event data immediately.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param clientId registered client id (-1 for registered synthesizer)\n\t * @param eventData event data which can be retrieved in SequencerClientCallback\n\t */\n\tpublic static sendEventNow(\n\t\tseq: ISequencer,\n\t\tclientId: number,\n\t\teventData: ISequencerEventData\n\t): void {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tseq.sendEventNow(clientId, eventData);\n\t}\n\t/**\n\t * Set interval timer process to call processSequencer for this sequencer.\n\t * This method uses 'setInterval' global method to register timer.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param msec time in milliseconds passed to both setInterval and processSequencer\n\t * @return return value of 'setInterval' (usually passing to 'clearInterval' will reset event)\n\t */\n\tpublic static setIntervalForSequencer(seq: ISequencer, msec: number) {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\treturn seq.setIntervalForSequencer(msec);\n\t}\n}\n", "import Synthesizer from './Synthesizer';\n\n/**\n * Returns the Promise object which resolves when the synthesizer engine is ready.\n */\nexport default function waitForReady(): Promise<void> {\n\treturn Synthesizer.waitForWasmInitialized();\n}\n", "\nimport MessageError from './MessageError';\n\nexport interface MethodCallEventData {\n\tid: number;\n\tmethod: string;\n\targs: any[];\n}\n\nexport interface MethodReturnEventData {\n\tid: number;\n\tmethod: string;\n\tval: any;\n\terror?: MessageErrorData;\n}\n\nexport interface MessageErrorData {\n\tbaseName: string;\n\tmessage: string;\n\tdetail: any;\n}\n\n/** @internal */\nexport interface Defer<T> {\n\tresolve(value: T): void;\n\treject(reason: any): void;\n}\n\n/** @internal */\nexport interface DeferMap {\n\t[id: number]: Defer<any>;\n}\n\n/** @internal */\nexport type HookReturnMessageCallback = (data: MethodReturnEventData) => boolean;\n\n/** @internal */\nexport interface CallMessageInstance {\n\tport: MessagePort;\n\tdefers: DeferMap;\n\tdeferId: number;\n}\n\n/** @internal */\nexport function initializeCallPort(\n\tport: MessagePort,\n\thookMessage?: HookReturnMessageCallback | undefined\n): CallMessageInstance {\n\tconst instance: CallMessageInstance = {\n\t\tport: port,\n\t\tdefers: {},\n\t\tdeferId: 0\n\t};\n\tport.addEventListener('message', (e) => processReturnMessage(instance.defers, hookMessage, e));\n\tport.start();\n\treturn instance;\n}\n\nfunction convertErrorTransferable(err: Error): MessageErrorData {\n\tconst result: any = {};\n\tconst objList: any[] = [];\n\tlet obj: any = err;\n\twhile (obj && obj !== Object.prototype) {\n\t\tobjList.unshift(obj);\n\t\tobj = Object.getPrototypeOf(obj);\n\t}\n\tobjList.forEach((o) => {\n\t\tObject.getOwnPropertyNames(o).forEach((key) => {\n\t\t\ttry {\n\t\t\t\tconst data = (err as any)[key];\n\t\t\t\tif (typeof data !== 'function' && typeof data !== 'symbol') {\n\t\t\t\t\tresult[key] = data;\n\t\t\t\t}\n\t\t\t} catch (_e) { }\n\t\t});\n\t});\n\treturn {\n\t\tbaseName: err.name,\n\t\tmessage: err.message,\n\t\tdetail: result\n\t};\n}\n\nfunction convertAnyErrorTransferable(err: any): MessageErrorData {\n\treturn convertErrorTransferable((err && err instanceof Error) ? err : new Error(`${err}`));\n}\n\nfunction makeMessageError(error: MessageErrorData): MessageError {\n\treturn new MessageError(error.baseName, error.message, error.detail);\n}\n\nfunction processReturnMessage(defers: DeferMap, hook: HookReturnMessageCallback | undefined, e: MessageEvent) {\n\tconst data: MethodReturnEventData = e.data;\n\tif (!data) {\n\t\treturn;\n\t}\n\tif (hook && hook(data)) {\n\t\treturn;\n\t}\n\tconst defer = defers[data.id];\n\tif (defer) {\n\t\tdelete defers[data.id];\n\t\tif (data.error) {\n\t\t\tdefer.reject(makeMessageError(data.error));\n\t\t} else {\n\t\t\tdefer.resolve(data.val);\n\t\t}\n\t} else {\n\t\tif (data.error) {\n\t\t\tthrow makeMessageError(data.error);\n\t\t}\n\t}\n}\n\n/** @internal */\nexport function postCall(instance: CallMessageInstance, method: string, args: any[]): void;\n\n/** @internal */\nexport function postCall({ port }: CallMessageInstance, method: string, args: any[]) {\n\tport.postMessage({\n\t\tid: -1, method, args\n\t} as MethodCallEventData);\n}\n\n/** @internal */\nexport function postCallWithPromise<T>(instance: CallMessageInstance, method: string, args: any[]): Promise<T> {\n\tconst id = instance.deferId++;\n\tif (instance.deferId === Infinity || instance.deferId < 0) {\n\t\tinstance.deferId = 0;\n\t}\n\tconst promise = new Promise<T>((resolve, reject) => {\n\t\tinstance.defers[id] = { resolve, reject };\n\t});\n\tconst transfers: Transferable[] = [];\n\tif (args[0] instanceof MessagePort) {\n\t\ttransfers.push(args[0]);\n\t}\n\tinstance.port.postMessage({\n\t\tid, method, args\n\t} as MethodCallEventData, transfers);\n\treturn promise;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport type HookCallMessageCallback = (data: MethodCallEventData) => boolean;\n\n/** @internal */\nexport interface ReturnMessageInstance {\n\tport: MessagePort;\n}\n\n/** @internal */\nexport function initializeReturnPort(\n\tport: MessagePort,\n\tpromiseInitialized: Promise<void> | null,\n\ttargetObjectHolder: () => any,\n\thookMessage?: HookCallMessageCallback | undefined\n): ReturnMessageInstance {\n\tconst instance: ReturnMessageInstance = {\n\t\tport: port\n\t};\n\tif (promiseInitialized) {\n\t\tport.addEventListener('message', (e) => {\n\t\t\tconst data = e.data;\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpromiseInitialized.then(() => processCallMessage(instance.port, data, targetObjectHolder, hookMessage));\n\t\t});\n\t} else {\n\t\tport.addEventListener('message', (e) => {\n\t\t\tconst data = e.data;\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tprocessCallMessage(instance.port, data, targetObjectHolder, hookMessage);\n\t\t});\n\t}\n\tport.start();\n\treturn instance;\n}\n\nfunction processCallMessage(\n\tport: MessagePort,\n\tdata: MethodCallEventData,\n\ttargetObjectHolder: () => any,\n\thook?: HookCallMessageCallback | undefined\n) {\n\tif (hook && hook(data)) {\n\t\treturn;\n\t}\n\tconst target = targetObjectHolder();\n\tif (!target[data.method]) {\n\t\tpostReturnErrorImpl(port, data.id, data.method, new Error('Not implemented'));\n\t} else {\n\t\ttry {\n\t\t\tpostReturnImpl(port, data.id, data.method, target[data.method].apply(target, data.args));\n\t\t} catch (e) {\n\t\t\tpostReturnErrorImpl(port, data.id, data.method, e);\n\t\t}\n\t}\n}\n\n/** @internal */\nexport function postReturn(instance: ReturnMessageInstance, id: number, method: string, value: any) {\n\tpostReturnImpl(instance.port, id, method, value);\n}\n\nfunction postReturnImpl(port: MessagePort, id: number, method: string, value: any) {\n\tif (value instanceof Promise) {\n\t\tvalue.then((v) => {\n\t\t\tif (id >= 0) {\n\t\t\t\tport.postMessage({\n\t\t\t\t\tid,\n\t\t\t\t\tmethod,\n\t\t\t\t\tval: v\n\t\t\t\t} as MethodReturnEventData);\n\t\t\t}\n\t\t}, (error) => {\n\t\t\tport.postMessage({\n\t\t\t\tid,\n\t\t\t\tmethod,\n\t\t\t\terror: convertAnyErrorTransferable(error)\n\t\t\t} as MethodReturnEventData);\n\t\t});\n\t} else {\n\t\tport.postMessage({\n\t\t\tid,\n\t\t\tmethod,\n\t\t\tval: value\n\t\t} as MethodReturnEventData);\n\t}\n}\n\n/** @internal */\nexport function postReturnError(instance: ReturnMessageInstance, id: number, method: string, error: any) {\n\tpostReturnErrorImpl(instance.port, id, method, error);\n}\n\nfunction postReturnErrorImpl(port: MessagePort, id: number, method: string, error: any) {\n\tport.postMessage({\n\t\tid,\n\t\tmethod,\n\t\terror: convertAnyErrorTransferable(error)\n\t} as MethodReturnEventData);\n}\n", "import Preset from './Preset';\n\nimport * as MethodMessaging from './MethodMessaging';\n\nexport default class WorkletSoundfont {\n\t// @internal\n\tprivate _messaging: MethodMessaging.CallMessageInstance;\n\n\t// @internal\n\tpublic constructor(port: MessagePort, private readonly name: string) {\n\t\tthis._messaging = MethodMessaging.initializeCallPort(port);\n\t}\n\n\tpublic getName(): string {\n\t\treturn this.name;\n\t}\n\n\tpublic getPreset(bank: number, presetNum: number): Promise<Preset | null> {\n\t\treturn MethodMessaging.postCallWithPromise(this._messaging, 'getPreset', [bank, presetNum]);\n\t}\n\n\tpublic getPresetIterable(): Promise<Iterable<Preset>> {\n\t\treturn MethodMessaging.postCallWithPromise<Preset[]>(this._messaging, 'getPresetIterable', []);\n\t}\n}\n", "\nimport ISequencer, { ClientInfo } from './ISequencer';\nimport ISynthesizer from './ISynthesizer';\nimport Sequencer<PERSON><PERSON> from './SequencerEvent';\n\nimport AudioWorkletNodeSynthesizer from './AudioWorkletNodeSynthesizer';\n\nimport * as MethodMessaging from './MethodMessaging';\n\n/** @internal */\nexport default class WorkletSequencer implements ISequencer {\n\t/** @internal */\n\tprivate _messaging: MethodMessaging.CallMessageInstance | null;\n\n\tconstructor(port: MessagePort) {\n\t\tthis._messaging = MethodMessaging.initializeCallPort(port);\n\t}\n\n\t/** @internal */\n\tpublic getRaw(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getRaw', []);\n\t}\n\t/** @internal */\n\tpublic registerSequencerClientByName(clientName: string, callbackName: string, param: number): Promise<number> {\n\t\treturn this.getRaw().then((seqPtr) => MethodMessaging.postCallWithPromise<number>(\n\t\t\tthis._messaging!,\n\t\t\t'registerSequencerClientByName',\n\t\t\t[seqPtr, clientName, callbackName, param]\n\t\t));\n\t}\n\n\tpublic close(): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'close', []);\n\t}\n\tpublic registerSynthesizer(synth: ISynthesizer | number): Promise<number> {\n\t\tlet val: Promise<number>;\n\t\tif (synth instanceof AudioWorkletNodeSynthesizer) {\n\t\t\tval = synth._getRawSynthesizer();\n\t\t} else {\n\t\t\treturn Promise.reject(new TypeError('\\'synth\\' is not a compatible type instance'));\n\t\t}\n\t\treturn val.then((v) => MethodMessaging.postCallWithPromise<number>(this._messaging!, 'registerSynthesizer', [v]));\n\t}\n\tpublic unregisterClient(clientId: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'unregisterClient', [clientId]);\n\t}\n\tpublic getAllRegisteredClients(): Promise<ClientInfo[]> {\n\t\treturn MethodMessaging.postCallWithPromise<ClientInfo[]>(this._messaging!, 'getAllRegisteredClients', []);\n\t}\n\tpublic getClientCount(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getClientCount', []);\n\t}\n\tpublic getClientInfo(index: number): Promise<ClientInfo> {\n\t\treturn MethodMessaging.postCallWithPromise<ClientInfo>(this._messaging!, 'getClientInfo', [index]);\n\t}\n\tpublic setTimeScale(scale: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'setTimeScale', [scale]);\n\t}\n\tpublic getTimeScale(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getTimeScale', []);\n\t}\n\tpublic getTick(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getTick', []);\n\t}\n\tpublic sendEventAt(event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'sendEventAt', [event, tick, isAbsolute]);\n\t}\n\tpublic sendEventToClientAt(clientId: number, event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'sendEventToClientAt', [clientId, event, tick, isAbsolute]);\n\t}\n\tpublic removeAllEvents(): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'removeAllEvents', []);\n\t}\n\tpublic removeAllEventsFromClient(clientId: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'removeAllEventsFromClient', [clientId]);\n\t}\n\n\tpublic processSequencer(msecToProcess: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'processSequencer', [msecToProcess]);\n\t}\n}\n", "let _module: any;\nlet _ptrDefaultLogFunction: number | undefined;\nlet _disabledLoggingLevel: LogLevel | null = null;\nconst _handlers: Array<(level: LogLevel | null) => void> = [];\n\nconst LOG_LEVEL_COUNT = 5;\n/** Log level for libfluidsynth */\nconst LogLevel = {\n\tPanic: 0,\n\tError: 1,\n\tWarning: 2,\n\tInfo: 3,\n\tDebug: 4,\n} as const;\n/** Log level for libfluidsynth */\ntype LogLevel = (typeof LogLevel)[keyof typeof LogLevel];\nexport { LogLevel };\n\nfunction bindFunctions() {\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t} else if (typeof Module !== 'undefined') {\n\t\t_module = Module;\n\t} else {\n\t\tthrow new Error(\n\t\t\t'wasm module is not available. libfluidsynth-*.js must be loaded.'\n\t\t);\n\t}\n}\n\n/**\n * Disable log output from libfluidsynth.\n * @param level disable log level (when `LogLevel.Warning` is specified, `Warning` `Info` `Debug` is disabled)\n * - If `null` is specified, log output feature is restored to the default.\n */\nexport function disableLogging(level: LogLevel | null = LogLevel.Panic): void {\n\tif (_disabledLoggingLevel === level) {\n\t\treturn;\n\t}\n\tbindFunctions();\n\tif (level == null) {\n\t\tif (_ptrDefaultLogFunction != null) {\n\t\t\t_module._fluid_set_log_function(0, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(1, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(2, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(3, _ptrDefaultLogFunction, 0);\n\t\t}\n\t\t_module._fluid_set_log_function(4, 0, 0);\n\t} else {\n\t\tlet ptr: number | undefined;\n\t\tfor (let l = level; l < LOG_LEVEL_COUNT; ++l) {\n\t\t\tconst p = _module._fluid_set_log_function(l, 0, 0);\n\t\t\tif (l !== LogLevel.Debug) {\n\t\t\t\tptr = p;\n\t\t\t}\n\t\t}\n\t\tif (ptr != null && _ptrDefaultLogFunction == null) {\n\t\t\t_ptrDefaultLogFunction = ptr;\n\t\t}\n\t}\n\t_disabledLoggingLevel = level;\n\tfor (const fn of _handlers) {\n\t\tfn(level);\n\t}\n}\n\n/**\n * Restores the log output from libfluidsynth. Same for calling `disableLogging(null)`.\n */\nexport function restoreLogging(): void {\n\tdisableLogging(null);\n}\n\n// @internal\nexport function getDisabledLoggingLevel(): LogLevel | null {\n\treturn _disabledLoggingLevel;\n}\n\n// @internal\nexport function addLoggingStatusChangedHandler(fn: (level: LogLevel | null) => void): void {\n\t_handlers.push(fn);\n}\n\n// @internal\nexport function removeLoggingStatusChangedHandler(fn: (level: LogLevel | null) => void): void {\n\tfor (let i = 0; i < _handlers.length; ++i) {\n\t\tif (_handlers[i] === fn) {\n\t\t\t_handlers.splice(i, 1);\n\t\t\treturn;\n\t\t}\n\t}\n}\n", "\nimport { SynthesizerDefaultValues, InterpolationValues, PlayerSetTempoType } from './Constants';\nimport ISequencer from './ISequencer';\nimport ISynthesizer from './ISynthesizer';\nimport SynthesizerSettings from './SynthesizerSettings';\nimport WorkletSoundfont from './WorkletSoundfont';\nimport WorkletSequencer from './WorkletSequencer';\nimport * as MethodMessaging from './MethodMessaging';\nimport { addLoggingStatusChangedHandler, getDisabledLoggingLevel, LogLevel } from './logging';\n\n/** @internal */\nexport const enum Constants {\n\tProcessorName = 'fluid-js',\n\tUpdateStatus = 'updateStatus',\n}\n/** @internal */\nexport interface SynthesizerStatus {\n\tplaying: boolean;\n\tplayerPlaying: boolean;\n}\n/** @internal */\nexport interface ProcessorOptions {\n\tsettings?: SynthesizerSettings;\n\tdisabledLoggingLevel?: LogLevel | null;\n}\n\n/** An synthesizer object with AudioWorkletNode */\nexport default class AudioWorkletNodeSynthesizer implements ISynthesizer {\n\n\t/** @internal */\n\tprivate _status: SynthesizerStatus;\n\t/** @internal */\n\tprivate _messaging: MethodMessaging.CallMessageInstance | null;\n\t/** @internal */\n\tprivate _node: AudioWorkletNode | null;\n\t/** @internal */\n\tprivate _gain: number;\n\n\t/** @internal */\n\tprivate handleLoggingChanged: (level: LogLevel | null) => void;\n\n\tconstructor() {\n\t\tthis._status = {\n\t\t\tplaying: false,\n\t\t\tplayerPlaying: false\n\t\t};\n\t\tthis._messaging = null;\n\t\tthis._node = null;\n\t\tthis._gain = SynthesizerDefaultValues.Gain;\n\t\tthis.handleLoggingChanged = this._handleLoggingChanged.bind(this);\n\t\taddLoggingStatusChangedHandler(this.handleLoggingChanged);\n\t}\n\n\t/** Audio node for this synthesizer */\n\tpublic get node(): AudioWorkletNode | null {\n\t\treturn this._node;\n\t}\n\n\t/**\n\t * Create AudiWorkletNode instance\n\t */\n\tpublic createAudioNode(context: AudioContext, settings?: SynthesizerSettings) {\n\t\tconst processorOptions: ProcessorOptions = {\n\t\t\tsettings: settings,\n\t\t\tdisabledLoggingLevel: getDisabledLoggingLevel(),\n\t\t};\n\t\tconst node = new AudioWorkletNode(context, Constants.ProcessorName, {\n\t\t\tnumberOfInputs: 0,\n\t\t\tnumberOfOutputs: 1,\n\t\t\tchannelCount: 2,\n\t\t\toutputChannelCount: [2],\n\t\t\tprocessorOptions: processorOptions,\n\t\t});\n\t\tthis._node = node;\n\n\t\tthis._messaging = MethodMessaging.initializeCallPort(node.port, (data) => {\n\t\t\tif (data.method === Constants.UpdateStatus) {\n\t\t\t\tthis._status = data.val;\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t});\n\t\treturn node;\n\t}\n\n\tpublic isInitialized() {\n\t\treturn this._messaging !== null;\n\t}\n\n\tpublic init(_sampleRate: number, _settings?: SynthesizerSettings) {\n\t}\n\n\tpublic close() {\n\t\t// call init instead of close\n\t\tMethodMessaging.postCall(this._messaging!, 'init', [0]);\n\t}\n\n\tpublic isPlaying() {\n\t\treturn this._status.playing;\n\t}\n\n\tpublic setInterpolation(value: InterpolationValues, channel?: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'setInterpolation', [value, channel]);\n\t}\n\n\tpublic getGain() {\n\t\treturn this._gain;\n\t}\n\n\tpublic setGain(gain: number) {\n\t\tthis._gain = gain;\n\t\tMethodMessaging.postCallWithPromise<void>(this._messaging!, 'setGain', [gain]).then(() => {\n\t\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getGain', []);\n\t\t}).then((value) => {\n\t\t\tthis._gain = value;\n\t\t});\n\t}\n\n\tpublic setChannelType(channel: number, isDrum: boolean) {\n\t\tMethodMessaging.postCall(this._messaging!, 'setChannelType', [channel, isDrum]);\n\t}\n\n\tpublic waitForVoicesStopped() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'waitForVoicesStopped', []);\n\t}\n\n\tpublic loadSFont(bin: ArrayBuffer) {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'loadSFont', [bin]);\n\t}\n\n\tpublic unloadSFont(id: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'unloadSFont', [id]);\n\t}\n\n\tpublic unloadSFontAsync(id: number) {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'unloadSFont', [id]);\n\t}\n\n\t/**\n\t * Returns the `Soundfont` instance for specified SoundFont.\n\t * @param sfontId loaded SoundFont id ({@link loadSFont} returns this)\n\t * @return resolve with `Soundfont` instance (rejected if `sfontId` is not valid or loaded)\n\t */\n\tpublic getSFontObject(sfontId: number): Promise<WorkletSoundfont> {\n\t\tconst channel = new MessageChannel();\n\t\treturn MethodMessaging.postCallWithPromise<string>(this._messaging!, 'getSFontObject', [channel.port2, sfontId]).then((name) => {\n\t\t\treturn new WorkletSoundfont(channel.port1, name);\n\t\t});\n\t}\n\n\tpublic getSFontBankOffset(id: number) {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getSFontBankOffset', [id]);\n\t}\n\tpublic setSFontBankOffset(id: number, offset: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'setSFontBankOffset', [id, offset]);\n\t}\n\n\tpublic render() {\n\t\tthrow new Error('Unexpected call');\n\t}\n\n\tpublic midiNoteOn(chan: number, key: number, vel: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiNoteOn', [chan, key, vel]);\n\t}\n\tpublic midiNoteOff(chan: number, key: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiNoteOff', [chan, key]);\n\t}\n\tpublic midiKeyPressure(chan: number, key: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiKeyPressure', [chan, key, val]);\n\t}\n\tpublic midiControl(chan: number, ctrl: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiControl', [chan, ctrl, val]);\n\t}\n\tpublic midiProgramChange(chan: number, prognum: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiProgramChange', [chan, prognum]);\n\t}\n\tpublic midiChannelPressure(chan: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiChannelPressure', [chan, val]);\n\t}\n\tpublic midiPitchBend(chan: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiPitchBend', [chan, val]);\n\t}\n\tpublic midiSysEx(data: Uint8Array) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSysEx', [data]);\n\t}\n\n\tpublic midiPitchWheelSensitivity(chan: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiPitchWheelSensitivity', [chan, val]);\n\t}\n\tpublic midiBankSelect(chan: number, bank: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiBankSelect', [chan, bank]);\n\t}\n\tpublic midiSFontSelect(chan: number, sfontId: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSFontSelect', [chan, sfontId]);\n\t}\n\tpublic midiProgramSelect(chan: number, sfontId: number, bank: number, presetNum: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiProgramSelect', [chan, sfontId, bank, presetNum]);\n\t}\n\tpublic midiUnsetProgram(chan: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiUnsetProgram', [chan]);\n\t}\n\tpublic midiProgramReset() {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiProgramReset', []);\n\t}\n\tpublic midiSystemReset() {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSystemReset', []);\n\t}\n\tpublic midiAllNotesOff(chan?: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiAllNotesOff', [chan]);\n\t}\n\tpublic midiAllSoundsOff(chan?: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiAllSoundsOff', [chan]);\n\t}\n\tpublic midiSetChannelType(chan: number, isDrum: boolean) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSetChannelType', [chan, isDrum]);\n\t}\n\n\tpublic resetPlayer() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'resetPlayer', []);\n\t}\n\n\tpublic closePlayer() {\n\t\tMethodMessaging.postCall(this._messaging!, 'closePlayer', []);\n\t}\n\n\tpublic isPlayerPlaying() {\n\t\treturn this._status.playerPlaying;\n\t}\n\n\tpublic addSMFDataToPlayer(bin: ArrayBuffer) {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'addSMFDataToPlayer', [bin]);\n\t}\n\n\tpublic playPlayer() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'playPlayer', []);\n\t}\n\n\tpublic stopPlayer() {\n\t\tMethodMessaging.postCall(this._messaging!, 'stopPlayer', []);\n\t}\n\n\tpublic retrievePlayerCurrentTick(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerCurrentTick', []);\n\t}\n\tpublic retrievePlayerTotalTicks(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerTotalTicks', []);\n\t}\n\tpublic retrievePlayerBpm(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerBpm', []);\n\t}\n\tpublic retrievePlayerMIDITempo(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerMIDITempo', []);\n\t}\n\tpublic seekPlayer(ticks: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'seekPlayer', [ticks]);\n\t}\n\tpublic setPlayerLoop(loopTimes: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'setPlayerLoop', [loopTimes]);\n\t}\n\tpublic setPlayerTempo(tempoType: PlayerSetTempoType, tempo: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'setPlayerTempo', [tempoType, tempo]);\n\t}\n\n\tpublic waitForPlayerStopped() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'waitForPlayerStopped', []);\n\t}\n\n\t/**\n\t * Creates a sequencer instance associated with this worklet node.\n\t */\n\tpublic createSequencer(): Promise<ISequencer> {\n\t\tconst channel = new MessageChannel();\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'createSequencer', [channel.port2]).then(() => {\n\t\t\treturn new WorkletSequencer(channel.port1);\n\t\t});\n\t}\n\n\t/**\n\t * Hooks MIDI events sent by the player. The hook callback function defined on\n\t * AudioWorkletGlobalScope object available in the worklet is used.\n\t * @param callbackName hook callback function name available as 'AudioWorkletGlobalScope[callbackName]',\n\t *     or falsy value ('', null, or undefined) to unhook.\n\t *     The type of 'AudioWorkletGlobalScope[callbackName]' must be HookMIDIEventCallback.\n\t * @param param any additional data passed to the callback.\n\t *     This data must be 'Transferable' data.\n\t * @return Promise object that resolves when succeeded, or rejects when failed\n\t */\n\tpublic hookPlayerMIDIEventsByName(callbackName: string | null | undefined, param?: any): Promise<void> {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'hookPlayerMIDIEventsByName', [callbackName, param]);\n\t}\n\n\t/**\n\t * Registers the user-defined client to the sequencer.\n\t * The client callback function defined on AudioWorkletGlobalScope\n\t * object available in the worklet is used.\n\t * The client can receive events in the time from sequencer process.\n\t * @param seq the sequencer instance created by AudioWorkletNodeSynthesizer.createSequencer\n\t * @param clientName the client name\n\t * @param callbackName callback function name available as 'AudioWorkletGlobalScope[callbackName]',\n\t *     or falsy value ('', null, or undefined) to unhook.\n\t *     The type of 'AudioWorkletGlobalScope[callbackName]' must be SequencerClientCallback.\n\t * @param param additional parameter passed to the callback\n\t * @return Promise object that resolves with registered client id when succeeded, or rejects when failed\n\t */\n\tpublic registerSequencerClientByName(seq: ISequencer, clientName: string, callbackName: string, param: number): Promise<number> {\n\t\tif (!(seq instanceof WorkletSequencer)) {\n\t\t\treturn Promise.reject(new TypeError('Invalid sequencer object'));\n\t\t}\n\t\treturn seq.registerSequencerClientByName(clientName, callbackName, param);\n\t}\n\n\t/**\n\t * Call a function defined in the AudioWorklet.\n\t *\n\t * The function will receive two parameters; the first parameter is a Synthesizer instance\n\t * (not AudioWorkletNodeSynthesizer instance), and the second is the data passed to 'param'.\n\t * This method is useful when the script loaded in AudioWorklet wants to\n\t * retrieve Synthesizer instance.\n\t *\n\t * @param name a function name (must be retrieved from AudioWorkletGlobalScope[name])\n\t * @param param any parameter (must be Transferable)\n\t * @return Promise object that resolves when the function process has done, or rejects when failed\n\t */\n\tpublic callFunction(name: string, param: any) {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'callFunction', [name, param]);\n\t}\n\n\t/** @internal */\n\tpublic _getRawSynthesizer(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getRawSynthesizer', []);\n\t}\n\n\t/** @internal */\n\tprivate _handleLoggingChanged(level: LogLevel | null) {\n\t\tif (this._messaging == null) {\n\t\t\treturn;\n\t\t}\n\t\tMethodMessaging.postCall(this._messaging, 'loggingChanged', [level]);\n\t}\n}\n", "\nimport * as Constants from './Constants';\nimport IMIDI<PERSON>vent from './IMIDIEvent';\nimport ISequencer from './ISequencer';\nimport ISequencerEventData, { rewriteEventData } from './ISequencerEventData';\nimport ISynthesizer from './ISynthesizer';\nimport MessageError from './MessageError';\nimport SequencerEvent, { EventType } from './SequencerEvent';\nimport * as SequencerEventTypes from './SequencerEvent';\nimport Synthesizer, { HookMIDIEventCallback, SequencerClientCallback } from './Synthesizer';\nimport SynthesizerSettings from './SynthesizerSettings';\nimport waitForReady from './waitForReady';\nimport AudioWorkletNodeSynthesizer from './AudioWorkletNodeSynthesizer';\nimport { disableLogging, restoreLogging, LogLevel } from './logging';\n\nexport {\n\tConstants,\n\tEventType,\n\tIMIDIEvent,\n\tISequencer,\n\tISequencerEventData,\n\tISynthesizer,\n\tHookMIDIEventCallback,\n\tMessageError,\n\trewrite<PERSON>ventD<PERSON>,\n\tSequencer<PERSON><PERSON><PERSON>allback,\n\tSequencerEvent,\n\tSequencerEventTypes,\n\tSynthesizer,\n\tSynthesizerSettings,\n\twaitForReady,\n\tAudioWorkletNodeSynthesizer,\n\tdisableLogging,\n\trestoreLogging,\n\tLogLevel,\n};\n"], "names": [], "sourceRoot": ""}