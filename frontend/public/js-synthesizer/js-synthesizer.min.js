/*!
js-synthesizer version 1.10.0

@license

Copyright (C) 2023 jet
All rights reserved.

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

  1. Redistributions of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.
  2. Redistributions in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.
  3. The name of the author may not be used to endorse or promote products derived
     from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["js-synthesizer"]=t():e.JSSynth=t()}(this,(()=>(()=>{"use strict";var e={d:(t,s)=>{for(var n in s)e.o(s,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:s[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{AudioWorkletNodeSynthesizer:()=>te,Constants:()=>s,LogLevel:()=>Y,MessageError:()=>h,SequencerEventTypes:()=>n,Synthesizer:()=>W,disableLogging:()=>Z,restoreLogging:()=>ee,rewriteEventData:()=>a,waitForReady:()=>G});var s={};e.r(s),e.d(s,{PlayerSetTempoType:()=>i});var n={};e.r(n);const i={Internal:0,ExternalBpm:1,ExternalMidi:2},r=0;class _{constructor(e,t){this._ptr=e,this._module=t}getRaw(){return this._ptr}dispose(){this._ptr=r}getType(){return this._ptr===r?-1:this._module._fluid_event_get_type(this._ptr)}getSource(){return this._ptr===r?-1:this._module._fluid_event_get_source(this._ptr)}getDest(){return this._ptr===r?-1:this._module._fluid_event_get_dest(this._ptr)}getChannel(){return this._ptr===r?-1:this._module._fluid_event_get_channel(this._ptr)}getKey(){return this._ptr===r?-1:this._module._fluid_event_get_key(this._ptr)}getVelocity(){return this._ptr===r?-1:this._module._fluid_event_get_velocity(this._ptr)}getControl(){return this._ptr===r?-1:this._module._fluid_event_get_control(this._ptr)}getValue(){return this._ptr===r?-1:this._module._fluid_event_get_value(this._ptr)}getProgram(){return this._ptr===r?-1:this._module._fluid_event_get_program(this._ptr)}getData(){return this._ptr===r?-1:this._module._fluid_event_get_data(this._ptr)}getDuration(){return this._ptr===r?-1:this._module._fluid_event_get_duration(this._ptr)}getBank(){return this._ptr===r?-1:this._module._fluid_event_get_bank(this._ptr)}getPitch(){return this._ptr===r?-1:this._module._fluid_event_get_pitch(this._ptr)}getSFontId(){return this._ptr===r?-1:this._module._fluid_event_get_sfont_id(this._ptr)}}const l="undefined"!=typeof AudioWorkletGlobalScope?AudioWorkletGlobalScope.wasmModule:Module;function o(e,t){switch(t.type){case 0:case"note":l._fluid_event_note(e,t.channel,t.key,t.vel,t.duration);break;case 1:case"noteon":case"note-on":l._fluid_event_noteon(e,t.channel,t.key,t.vel);break;case 2:case"noteoff":case"note-off":l._fluid_event_noteoff(e,t.channel,t.key);break;case 3:case"allsoundsoff":case"all-sounds-off":l._fluid_event_all_sounds_off(e,t.channel);break;case 4:case"allnotesoff":case"all-notes-off":l._fluid_event_all_notes_off(e,t.channel);break;case 5:case"bankselect":case"bank-select":l._fluid_event_bank_select(e,t.channel,t.bank);break;case 6:case"programchange":case"program-change":l._fluid_event_program_change(e,t.channel,t.preset);break;case 7:case"programselect":case"program-select":l._fluid_event_program_select(e,t.channel,t.sfontId,t.bank,t.preset);break;case 12:case"controlchange":case"control-change":l._fluid_event_control_change(e,t.channel,t.control,t.value);break;case 8:case"pitchbend":case"pitch-bend":l._fluid_event_pitch_bend(e,t.channel,t.value);break;case 9:case"pitchwheelsens":case"pitchwheelsensitivity":case"pitch-wheel-sens":case"pitch-wheel-sensitivity":l._fluid_event_pitch_wheelsens(e,t.channel,t.value);break;case 10:case"modulation":l._fluid_event_modulation(e,t.channel,t.value);break;case 11:case"sustain":l._fluid_event_sustain(e,t.channel,t.value);break;case 13:case"pan":l._fluid_event_pan(e,t.channel,t.value);break;case 14:case"volume":l._fluid_event_volume(e,t.channel,t.value);break;case 15:case"reverb":case"reverbsend":case"reverb-send":l._fluid_event_reverb_send(e,t.channel,t.value);break;case 16:case"chorus":case"chorussend":case"chorus-send":l._fluid_event_chorus_send(e,t.channel,t.value);break;case 20:case"keypressure":case"key-pressure":case"aftertouch":l._fluid_event_key_pressure(e,t.channel,t.key,t.value);break;case 19:case"channelpressure":case"channel-pressure":case"channel-aftertouch":l._fluid_event_channel_pressure(e,t.channel,t.value);break;case 21:case"systemreset":case"system-reset":l._fluid_event_system_reset(e);break;case 17:case"timer":l._fluid_event_timer(e,t.data);break;default:return!1}return!0}function a(e,t){if(!(e&&e instanceof _))return!1;const s=e.getRaw();return s!==r&&o(s,t)}class h extends Error{constructor(e,t,s){super(t),this.baseName=e,this.detail=s,s&&s.stack&&(this.stack=s.stack)}}class u{constructor(e,t){this._ptr=e,this._module=t}getType(){return this._module._fluid_midi_event_get_type(this._ptr)}setType(e){this._module._fluid_midi_event_set_type(this._ptr,e)}getChannel(){return this._module._fluid_midi_event_get_channel(this._ptr)}setChannel(e){this._module._fluid_midi_event_set_channel(this._ptr,e)}getKey(){return this._module._fluid_midi_event_get_key(this._ptr)}setKey(e){this._module._fluid_midi_event_set_key(this._ptr,e)}getVelocity(){return this._module._fluid_midi_event_get_velocity(this._ptr)}setVelocity(e){this._module._fluid_midi_event_set_velocity(this._ptr,e)}getControl(){return this._module._fluid_midi_event_get_control(this._ptr)}setControl(e){this._module._fluid_midi_event_set_control(this._ptr,e)}getValue(){return this._module._fluid_midi_event_get_value(this._ptr)}setValue(e){this._module._fluid_midi_event_set_value(this._ptr,e)}getProgram(){return this._module._fluid_midi_event_get_program(this._ptr)}setProgram(e){this._module._fluid_midi_event_set_program(this._ptr,e)}getPitch(){return this._module._fluid_midi_event_get_pitch(this._ptr)}setPitch(e){this._module._fluid_midi_event_set_pitch(this._ptr,e)}setSysEx(e){const t=e.byteLength,s=this._module._malloc(t);new Uint8Array(this._module.HEAPU8.buffer,s,t).set(e),this._module._fluid_midi_event_set_sysex(this._ptr,s,t,1)}setText(e){const t=e.byteLength,s=this._module._malloc(t);new Uint8Array(this._module.HEAPU8.buffer,s,t).set(e),this._module._fluid_midi_event_set_text(this._ptr,s,t,1)}setLyrics(e){const t=e.byteLength,s=this._module._malloc(t);new Uint8Array(this._module.HEAPU8.buffer,s,t).set(e),this._module._fluid_midi_event_set_lyrics(this._ptr,s,t,1)}}let d,c,f,y,g,m,p,v,b,P,w,S,k,C,q,I,A,E,T,F,z,R;function M(e){const t=d._new_fluid_event();return o(t,e)?t:(d._delete_fluid_event(t),null)}class O{constructor(){d||("undefined"!=typeof AudioWorkletGlobalScope?(d=AudioWorkletGlobalScope.wasmModule,c=AudioWorkletGlobalScope.wasmRemoveFunction):(d=Module,c=removeFunction),f=d.cwrap("fluid_sequencer_get_client_name","string",["number","number"])),this._seq=r,this._seqId=-1,this._clientFuncMap={}}_initialize(){return this.close(),this._seq=d._new_fluid_sequencer2(0),this._seqId=-1,Promise.resolve()}getRaw(){return this._seq}close(){this._seq!==r&&(Object.keys(this._clientFuncMap).forEach((e=>{this.unregisterClient(Number(e))})),this.unregisterClient(-1),d._delete_fluid_sequencer(this._seq),this._seq=r)}registerSynthesizer(e){let t;if(-1!==this._seqId&&(d._fluid_sequencer_unregister_client(this._seq,this._seqId),this._seqId=-1),"number"==typeof e)t=e;else{if(!(e instanceof W))return Promise.reject(new TypeError("'synth' is not a compatible type instance"));t=e.getRawSynthesizer()}return this._seqId=d._fluid_sequencer_register_fluidsynth(this._seq,t),Promise.resolve(this._seqId)}unregisterClient(e){if(-1===e&&-1===(e=this._seqId))return;const t=d._new_fluid_event();if(d._fluid_event_set_source(t,-1),d._fluid_event_set_dest(t,e),d._fluid_event_unregistering(t),d._fluid_sequencer_send_now(this._seq,t),d._delete_fluid_event(t),d._fluid_sequencer_unregister_client(this._seq,e),this._seqId===e)this._seqId=-1;else{const t=this._clientFuncMap;t[e]&&(c(t[e]),delete t[e])}}getAllRegisteredClients(){const e=d._fluid_sequencer_count_clients(this._seq),t=[];for(let s=0;s<e;++s){const e=d._fluid_sequencer_get_client_id(this._seq,s),n=f(this._seq,e);t.push({clientId:e,name:n})}return Promise.resolve(t)}getClientCount(){return Promise.resolve(d._fluid_sequencer_count_clients(this._seq))}getClientInfo(e){const t=d._fluid_sequencer_get_client_id(this._seq,e),s=f(this._seq,t);return Promise.resolve({clientId:t,name:s})}setTimeScale(e){d._fluid_sequencer_set_time_scale(this._seq,e)}getTimeScale(){return Promise.resolve(d._fluid_sequencer_get_time_scale(this._seq))}getTick(){return Promise.resolve(d._fluid_sequencer_get_tick(this._seq))}sendEventAt(e,t,s){const n=M(e);if(null!==n){const e=d._fluid_sequencer_count_clients(this._seq);for(let i=0;i<e;++i){const e=d._fluid_sequencer_get_client_id(this._seq,i);d._fluid_event_set_dest(n,e),d._fluid_sequencer_send_at(this._seq,n,t,s?1:0)}d._delete_fluid_event(n)}}sendEventToClientAt(e,t,s,n){const i=M(t);null!==i&&(d._fluid_event_set_dest(i,-1===e?this._seqId:e),d._fluid_sequencer_send_at(this._seq,i,s,n?1:0),d._delete_fluid_event(i))}sendEventToClientNow(e,t){const s=M(t);null!==s&&(d._fluid_event_set_dest(s,-1===e?this._seqId:e),d._fluid_sequencer_send_now(this._seq,s),d._delete_fluid_event(s))}sendEventNow(e,t){if(!(t instanceof _))return;const s=t.getRaw();s!==r&&(d._fluid_event_set_dest(s,-1===e?this._seqId:e),d._fluid_sequencer_send_now(this._seq,s))}removeAllEvents(){d._fluid_sequencer_remove_events(this._seq,-1,-1,-1)}removeAllEventsFromClient(e){d._fluid_sequencer_remove_events(this._seq,-1,-1===e?this._seqId:e,-1)}processSequencer(e){this._seq!==r&&d._fluid_sequencer_process(this._seq,e)}setIntervalForSequencer(e){return setInterval((()=>this.processSequencer(e)),e)}}class N{constructor(e){this._ptr=e}static getSoundfontById(e,t){y||(y="undefined"!=typeof AudioWorkletGlobalScope?AudioWorkletGlobalScope.wasmModule:Module,g=y.cwrap("fluid_sfont_get_name","string",["number"]),m=y.cwrap("fluid_preset_get_name","string",["number"]));const s=y._fluid_synth_get_sfont_by_id(e.getRawSynthesizer(),t);return s===r?null:new N(s)}getName(){return g(this._ptr)}getPreset(e,t){const s=y._fluid_sfont_get_preset(this._ptr,e,t);if(s===r)return null;return{soundfont:this,name:m(s),bankNum:y._fluid_preset_get_banknum(s),num:y._fluid_preset_get_num(s)}}getPresetIterable(){const e=()=>{y._fluid_sfont_iteration_start(this._ptr)},t=()=>{const e=y._fluid_sfont_iteration_next(this._ptr);if(0===e)return{done:!0,value:void 0};return{done:!1,value:{soundfont:this,name:m(e),bankNum:y._fluid_preset_get_banknum(e),num:y._fluid_preset_get_num(e)}}};return{[Symbol.iterator]:()=>(e(),{next:t})}}}function j(){if(!C){if("undefined"!=typeof AudioWorkletGlobalScope)p=AudioWorkletGlobalScope.wasmModule,v=AudioWorkletGlobalScope.wasmAddFunction,b=AudioWorkletGlobalScope.wasmRemoveFunction;else{if("undefined"==typeof Module)throw new Error("wasm module is not available. libfluidsynth-*.js must be loaded.");p=Module,v=addFunction,b=removeFunction}P=p.FS,w=p.cwrap("fluid_settings_setint","number",["number","string","number"]),S=p.cwrap("fluid_settings_setnum","number",["number","string","number"]),k=p.cwrap("fluid_settings_setstr","number",["number","string","string"]),C=p.cwrap("fluid_synth_error","string",["number"]),q=p.cwrap("fluid_synth_sfload","number",["number","string","number"]),I=p.cwrap("fluid_sequencer_register_client","number",["number","string","number","number"]),A=p._malloc.bind(p),E=p._free.bind(p),T=p._fluid_synth_handle_midi_event.bind(p)}}function L(e,t,s){void 0!==s&&w(e,t,s?1:0)}function B(e,t,s){void 0!==s&&w(e,t,s)}function D(e,t,s){void 0!==s&&S(e,t,s)}class W{constructor(){j(),this._settings=r,this._synth=r,this._player=r,this._playerPlaying=!1,this._playerCallbackPtr=null,this._fluidSynthCallback=null,this._buffer=r,this._bufferSize=0,this._numPtr=r,this._gain=.5}static waitForWasmInitialized(){return function(){if(F)return F;let e,t;if("undefined"!=typeof AudioWorkletGlobalScope)e=AudioWorkletGlobalScope.wasmModule,t=AudioWorkletGlobalScope.addOnPostRun;else{if("undefined"==typeof Module)return Promise.reject(new Error("wasm module is not available. libfluidsynth-*.js must be loaded."));e=Module,t="undefined"!=typeof addOnPostRun?addOnPostRun:void 0}return e.calledRun?(F=Promise.resolve(),F):(F=new Promise(void 0===t?e=>{const t=p.onRuntimeInitialized;p.onRuntimeInitialized=()=>{e(),t&&t()}}:e=>{t(e)}),F)}()}isInitialized(){return this._synth!==r}getRawSynthesizer(){return this._synth}createAudioNode(e,t){const s=e.createScriptProcessor(t,0,2);return s.addEventListener("audioprocess",(e=>{this.render(e.outputBuffer)})),s}init(e,t){this.close();const s=this._settings=p._new_fluid_settings();S(s,"synth.sample-rate",e),t&&(void 0!==t.initialGain&&(this._gain=t.initialGain),L(s,"synth.chorus.active",t.chorusActive),D(s,"synth.chorus.depth",t.chorusDepth),D(s,"synth.chorus.level",t.chorusLevel),B(s,"synth.chorus.nr",t.chorusNr),D(s,"synth.chorus.speed",t.chorusSpeed),B(s,"synth.midi-channels",t.midiChannelCount),function(e,t,s){void 0!==s&&k(e,t,s)}(s,"synth.midi-bank-select",t.midiBankSelect),B(s,"synth.min-note-length",t.minNoteLength),D(s,"synth.overflow.age",t.overflowAge),D(s,"synth.overflow.important",t.overflowImportantValue),void 0!==t.overflowImportantChannels&&k(s,"synth.overflow.important-channels",t.overflowImportantChannels.join(",")),D(s,"synth.overflow.percussion",t.overflowPercussion),D(s,"synth.overflow.released",t.overflowReleased),D(s,"synth.overflow.sustained",t.overflowSustained),D(s,"synth.overflow.volume",t.overflowVolume),B(s,"synth.polyphony",t.polyphony),L(s,"synth.reverb.active",t.reverbActive),D(s,"synth.reverb.damp",t.reverbDamp),D(s,"synth.reverb.level",t.reverbLevel),D(s,"synth.reverb.room-size",t.reverbRoomSize),D(s,"synth.reverb.width",t.reverbWidth)),S(s,"synth.gain",this._gain),this._synth=p._new_fluid_synth(this._settings),this._numPtr=A(8)}close(){this._synth!==r&&(this._closePlayer(),p._delete_fluid_synth(this._synth),this._synth=r,p._delete_fluid_settings(this._settings),this._settings=r,E(this._numPtr),this._numPtr=r)}isPlaying(){return this._synth!==r&&function(e){const t=p._fluid_synth_get_active_voice_count(e);if(!t)return 0;let s=140,n=e+s+4>>2,i=p.HEAPU32[n];if(i!==t&&(s+=4,n=e+s+4>>2,i=p.HEAPU32[n],i!==t))return console.warn("js-synthesizer: cannot check synthesizer internal data (may be changed)"),t;const r=p.HEAPU32[e+s>>2];if(!r||r>=p.HEAPU32.byteLength)return console.warn("js-synthesizer: cannot check synthesizer internal data (may be changed)"),t;const _=p._fluid_synth_get_polyphony(e);let l=!1;for(let e=0;e<_;++e){const t=p.HEAPU32[(r>>2)+e];if(t&&4!==p.HEAPU8[t+4]){l=!0;break}}if(!l)return 0!==i&&console.warn("js-synthesizer: Active voice count is not zero, but all voices are off:",i),p.HEAPU32[n]=0,0;return t}(this._synth)>0}setInterpolation(e,t){this.ensureInitialized(),void 0===t&&(t=-1),p._fluid_synth_set_interp_method(this._synth,t,e)}getGain(){return this._gain}setGain(e){this.ensureInitialized(),p._fluid_synth_set_gain(this._synth,e),this._gain=p._fluid_synth_get_gain(this._synth)}setChannelType(e,t){this.ensureInitialized(),p._fluid_synth_set_channel_type(this._synth,e,t?1:0)}waitForVoicesStopped(){return this.flushFramesAsync()}loadSFont(e){this.ensureInitialized();const t=(s=".sf2",`/${"sfont"}-r${65535*Math.random()}-${65535*Math.random()}${s}`);var s;const n=new Uint8Array(e);P.writeFile(t,n);const i=q(this._synth,t,1);return P.unlink(t),-1===i?Promise.reject(new Error(C(this._synth))):Promise.resolve(i)}unloadSFont(e){this.ensureInitialized(),this.stopPlayer(),this.flushFramesSync(),p._fluid_synth_sfunload(this._synth,e,1)}unloadSFontAsync(e){return this.ensureInitialized(),this.stopPlayer(),this.flushFramesAsync().then((()=>{p._fluid_synth_sfunload(this._synth,e,1)}))}getSFontObject(e){return N.getSoundfontById(this,e)}getSFontBankOffset(e){return this.ensureInitialized(),Promise.resolve(p._fluid_synth_get_bank_offset(this._synth,e))}setSFontBankOffset(e,t){this.ensureInitialized(),p._fluid_synth_set_bank_offset(this._synth,e,t)}render(e){const t="numberOfChannels"in e?e.length:e[0].length,s="numberOfChannels"in e?e.numberOfChannels:e.length,n=4*t,i=2*n;this._bufferSize<i&&(this._buffer!==r&&E(this._buffer),this._buffer=A(i),this._bufferSize=i);const _=this._buffer,l=this._buffer+n;this.renderRaw(_,l,t);const o=new Float32Array(p.HEAPU8.buffer,_,t),a=s>=2?new Float32Array(p.HEAPU8.buffer,l,t):null;if("numberOfChannels"in e)if(e.copyToChannel)e.copyToChannel(o,0,0),a&&e.copyToChannel(a,1,0);else{const t=e.getChannelData(0);if(o.forEach(((e,s)=>t[s]=e)),a){const t=e.getChannelData(1);a.forEach(((e,s)=>t[s]=e))}}else e[0].set(o),a&&e[1].set(a);this.isPlayerPlaying()}midiNoteOn(e,t,s){p._fluid_synth_noteon(this._synth,e,t,s)}midiNoteOff(e,t){p._fluid_synth_noteoff(this._synth,e,t)}midiKeyPressure(e,t,s){p._fluid_synth_key_pressure(this._synth,e,t,s)}midiControl(e,t,s){p._fluid_synth_cc(this._synth,e,t,s)}midiProgramChange(e,t){p._fluid_synth_program_change(this._synth,e,t)}midiChannelPressure(e,t){p._fluid_synth_channel_pressure(this._synth,e,t)}midiPitchBend(e,t){p._fluid_synth_pitch_bend(this._synth,e,t)}midiSysEx(e){const t=e.byteLength,s=A(t);p.HEAPU8.set(e,s),p._fluid_synth_sysex(this._synth,s,t,r,r,r,0),E(s)}midiPitchWheelSensitivity(e,t){p._fluid_synth_pitch_wheel_sens(this._synth,e,t)}midiBankSelect(e,t){p._fluid_synth_bank_select(this._synth,e,t)}midiSFontSelect(e,t){p._fluid_synth_sfont_select(this._synth,e,t)}midiProgramSelect(e,t,s,n){p._fluid_synth_program_select(this._synth,e,t,s,n)}midiUnsetProgram(e){p._fluid_synth_unset_program(this._synth,e)}midiProgramReset(){p._fluid_synth_program_reset(this._synth)}midiSystemReset(){p._fluid_synth_system_reset(this._synth)}midiAllNotesOff(e){p._fluid_synth_all_notes_off(this._synth,void 0===e?-1:e)}midiAllSoundsOff(e){p._fluid_synth_all_sounds_off(this._synth,void 0===e?-1:e)}midiSetChannelType(e,t){p._fluid_synth_set_channel_type(this._synth,e,t?1:0)}setReverb(e,t,s,n){p._fluid_synth_set_reverb(this._synth,e,t,s,n)}setReverbRoomsize(e){p._fluid_synth_set_reverb_roomsize(this._synth,e)}setReverbDamp(e){p._fluid_synth_set_reverb_damp(this._synth,e)}setReverbWidth(e){p._fluid_synth_set_reverb_width(this._synth,e)}setReverbLevel(e){p._fluid_synth_set_reverb_level(this._synth,e)}setReverbOn(e){p._fluid_synth_set_reverb_on(this._synth,e?1:0)}getReverbRoomsize(){return p._fluid_synth_get_reverb_roomsize(this._synth)}getReverbDamp(){return p._fluid_synth_get_reverb_damp(this._synth)}getReverbLevel(){return p._fluid_synth_get_reverb_level(this._synth)}getReverbWidth(){return p._fluid_synth_get_reverb_width(this._synth)}setChorus(e,t,s,n,i){p._fluid_synth_set_chorus(this._synth,e,t,s,n,i)}setChorusVoiceCount(e){p._fluid_synth_set_chorus_nr(this._synth,e)}setChorusLevel(e){p._fluid_synth_set_chorus_level(this._synth,e)}setChorusSpeed(e){p._fluid_synth_set_chorus_speed(this._synth,e)}setChorusDepth(e){p._fluid_synth_set_chorus_depth(this._synth,e)}setChorusType(e){p._fluid_synth_set_chorus_type(this._synth,e)}setChorusOn(e){p._fluid_synth_set_chorus_on(this._synth,e?1:0)}getChorusVoiceCount(){return p._fluid_synth_get_chorus_nr(this._synth)}getChorusLevel(){return p._fluid_synth_get_chorus_level(this._synth)}getChorusSpeed(){return p._fluid_synth_get_chorus_speed(this._synth)}getChorusDepth(){return p._fluid_synth_get_chorus_depth(this._synth)}getChorusType(){return p._fluid_synth_get_chorus_type(this._synth)}getGenerator(e,t){return p._fluid_synth_get_gen(this._synth,e,t)}setGenerator(e,t,s){p._fluid_synth_set_gen(this._synth,e,t,s)}getLegatoMode(e){return p._fluid_synth_get_legato_mode(this._synth,e,this._numPtr),p.HEAP32[this._numPtr>>2]}setLegatoMode(e,t){p._fluid_synth_set_legato_mode(this._synth,e,t)}getPortamentoMode(e){return p._fluid_synth_get_portamento_mode(this._synth,e,this._numPtr),p.HEAP32[this._numPtr>>2]}setPortamentoMode(e,t){p._fluid_synth_set_portamento_mode(this._synth,e,t)}getBreathMode(e){return p._fluid_synth_get_breath_mode(this._synth,e,this._numPtr),p.HEAP32[this._numPtr>>2]}setBreathMode(e,t){p._fluid_synth_set_breath_mode(this._synth,e,t)}resetPlayer(){return new Promise((e=>{this._initPlayer(),e()}))}closePlayer(){this._closePlayer()}_initPlayer(){this._closePlayer();const e=p._new_fluid_player(this._synth);if(this._player=e,e===r)throw new Error("Out of memory");if(null===this._fluidSynthCallback){const t=p.HEAPU32[e+588>>2];p.HEAPU32[e+592>>2]===this._synth&&(this._fluidSynthCallback=t)}}_closePlayer(){const e=this._player;e!==r&&(this.stopPlayer(),p._delete_fluid_player(e),this._player=r,this._playerCallbackPtr=null)}isPlayerPlaying(){if(this._playerPlaying){if(1===p._fluid_player_get_status(this._player))return!0;this.stopPlayer()}return!1}addSMFDataToPlayer(e){this.ensurePlayerInitialized();const t=e.byteLength,s=A(t);p.HEAPU8.set(new Uint8Array(e),s);const n=p._fluid_player_add_mem(this._player,s,t);return E(s),-1!==n?Promise.resolve():Promise.reject(new Error(C(this._synth)))}playPlayer(){if(this.ensurePlayerInitialized(),this._playerPlaying&&this.stopPlayer(),-1===p._fluid_player_play(this._player))return Promise.reject(new Error(C(this._synth)));this._playerPlaying=!0;let e=()=>{};const t=new Promise((t=>{e=t}));return this._playerDefer={promise:t,resolve:e},Promise.resolve()}stopPlayer(){const e=this._player;e!==r&&this._playerPlaying&&(p._fluid_player_stop(e),p._fluid_player_join(e),p._fluid_synth_all_sounds_off(this._synth,-1),this._playerDefer&&(this._playerDefer.resolve(),this._playerDefer=void 0),this._playerPlaying=!1)}retrievePlayerCurrentTick(){return this.ensurePlayerInitialized(),Promise.resolve(p._fluid_player_get_current_tick(this._player))}retrievePlayerTotalTicks(){return this.ensurePlayerInitialized(),Promise.resolve(p._fluid_player_get_total_ticks(this._player))}retrievePlayerBpm(){return this.ensurePlayerInitialized(),Promise.resolve(p._fluid_player_get_bpm(this._player))}retrievePlayerMIDITempo(){return this.ensurePlayerInitialized(),Promise.resolve(p._fluid_player_get_midi_tempo(this._player))}seekPlayer(e){this.ensurePlayerInitialized(),p._fluid_player_seek(this._player,e)}setPlayerLoop(e){this.ensurePlayerInitialized(),p._fluid_player_set_loop(this._player,e)}setPlayerTempo(e,t){this.ensurePlayerInitialized(),p._fluid_player_set_tempo(this._player,e,t)}hookPlayerMIDIEvents(e,t){this.ensurePlayerInitialized();const s=this._playerCallbackPtr;if(null===s&&null===e)return;const n=null!==e?v(function(e,t,s){return(n,i)=>{const r=p._fluid_midi_event_get_type(i);return t(e,r,new u(i,p),s)?0:p._fluid_synth_handle_midi_event(n,i)}}(this,e,t),"iii"):null!==this._fluidSynthCallback?null:v(T,"iii");null!==s&&null!==n?(p._fluid_player_set_playback_callback(this._player,n,this._synth),b(s)):null===n?(p._fluid_player_set_playback_callback(this._player,this._fluidSynthCallback,this._synth),b(s)):p._fluid_player_set_playback_callback(this._player,n,this._synth),this._playerCallbackPtr=n}ensureInitialized(){if(this._synth===r)throw new Error("Synthesizer is not initialized")}ensurePlayerInitialized(){this.ensureInitialized(),this._player===r&&this._initPlayer()}renderRaw(e,t,s){p._fluid_synth_write_float(this._synth,s,e,0,1,t,0,1)}flushFramesSync(){const e=262144,t=A(524288),s=t,n=t+e;for(;this.isPlaying();)this.renderRaw(s,n,65536);E(t)}flushFramesAsync(){if(!this.isPlaying())return Promise.resolve();const e=262144,t=A(524288),s=t,n=t+e,i="undefined"!=typeof setTimeout?()=>new Promise((e=>setTimeout(e,0))):()=>Promise.resolve();function r(){return i().then(l)}const _=this;function l(){return _.isPlaying()?(_.renderRaw(s,n,65536),r()):(E(t),Promise.resolve())}return r()}waitForPlayerStopped(){return this._playerDefer?this._playerDefer.promise:Promise.resolve()}static createSequencer(){j();const e=new O;return e._initialize().then((()=>e))}static registerSequencerClient(e,t,s,n){if(!(e instanceof O))throw new TypeError("Invalid sequencer instance");const i=v(((t,n,i,r)=>{const l=new _(n,p),o=p._fluid_event_get_type(n);s(t,o,l,e,r)}),"viiii"),r=I(e.getRaw(),t,i,n);return-1!==r&&(e._clientFuncMap[r]=i),r}static sendEventToClientNow(e,t,s){if(!(e instanceof O))throw new TypeError("Invalid sequencer instance");e.sendEventToClientNow(t,s)}static sendEventNow(e,t,s){if(!(e instanceof O))throw new TypeError("Invalid sequencer instance");e.sendEventNow(t,s)}static setIntervalForSequencer(e,t){if(!(e instanceof O))throw new TypeError("Invalid sequencer instance");return e.setIntervalForSequencer(t)}}function G(){return W.waitForWasmInitialized()}function U(e,t){const s={port:e,defers:{},deferId:0};return e.addEventListener("message",(e=>function(e,t,s){const n=s.data;if(!n)return;if(t&&t(n))return;const i=e[n.id];if(i)delete e[n.id],n.error?i.reject(H(n.error)):i.resolve(n.val);else if(n.error)throw H(n.error)}(s.defers,t,e))),e.start(),s}function H(e){return new h(e.baseName,e.message,e.detail)}function x({port:e},t,s){e.postMessage({id:-1,method:t,args:s})}function V(e,t,s){const n=e.deferId++;(e.deferId===1/0||e.deferId<0)&&(e.deferId=0);const i=new Promise(((t,s)=>{e.defers[n]={resolve:t,reject:s}})),r=[];return s[0]instanceof MessagePort&&r.push(s[0]),e.port.postMessage({id:n,method:t,args:s},r),i}class K{constructor(e,t){this.name=t,this._messaging=U(e)}getName(){return this.name}getPreset(e,t){return V(this._messaging,"getPreset",[e,t])}getPresetIterable(){return V(this._messaging,"getPresetIterable",[])}}class ${constructor(e){this._messaging=U(e)}getRaw(){return V(this._messaging,"getRaw",[])}registerSequencerClientByName(e,t,s){return this.getRaw().then((n=>V(this._messaging,"registerSequencerClientByName",[n,e,t,s])))}close(){x(this._messaging,"close",[])}registerSynthesizer(e){let t;return e instanceof te?(t=e._getRawSynthesizer(),t.then((e=>V(this._messaging,"registerSynthesizer",[e])))):Promise.reject(new TypeError("'synth' is not a compatible type instance"))}unregisterClient(e){x(this._messaging,"unregisterClient",[e])}getAllRegisteredClients(){return V(this._messaging,"getAllRegisteredClients",[])}getClientCount(){return V(this._messaging,"getClientCount",[])}getClientInfo(e){return V(this._messaging,"getClientInfo",[e])}setTimeScale(e){x(this._messaging,"setTimeScale",[e])}getTimeScale(){return V(this._messaging,"getTimeScale",[])}getTick(){return V(this._messaging,"getTick",[])}sendEventAt(e,t,s){x(this._messaging,"sendEventAt",[e,t,s])}sendEventToClientAt(e,t,s,n){x(this._messaging,"sendEventToClientAt",[e,t,s,n])}removeAllEvents(){x(this._messaging,"removeAllEvents",[])}removeAllEventsFromClient(e){x(this._messaging,"removeAllEventsFromClient",[e])}processSequencer(e){x(this._messaging,"processSequencer",[e])}}let J=null;const Q=[],X=5,Y={Panic:0,Error:1,Warning:2,Info:3,Debug:4};function Z(e=Y.Panic){if(J!==e){if(function(){if("undefined"!=typeof AudioWorkletGlobalScope)z=AudioWorkletGlobalScope.wasmModule;else{if("undefined"==typeof Module)throw new Error("wasm module is not available. libfluidsynth-*.js must be loaded.");z=Module}}(),null==e)null!=R&&(z._fluid_set_log_function(0,R,0),z._fluid_set_log_function(1,R,0),z._fluid_set_log_function(2,R,0),z._fluid_set_log_function(3,R,0)),z._fluid_set_log_function(4,0,0);else{let t;for(let s=e;s<X;++s){const e=z._fluid_set_log_function(s,0,0);s!==Y.Debug&&(t=e)}null!=t&&null==R&&(R=t)}J=e;for(const t of Q)t(e)}}function ee(){Z(null)}class te{constructor(){var e;this._status={playing:!1,playerPlaying:!1},this._messaging=null,this._node=null,this._gain=.5,this.handleLoggingChanged=this._handleLoggingChanged.bind(this),e=this.handleLoggingChanged,Q.push(e)}get node(){return this._node}createAudioNode(e,t){const s=new AudioWorkletNode(e,"fluid-js",{numberOfInputs:0,numberOfOutputs:1,channelCount:2,outputChannelCount:[2],processorOptions:{settings:t,disabledLoggingLevel:J}});return this._node=s,this._messaging=U(s.port,(e=>"updateStatus"===e.method&&(this._status=e.val,!0))),s}isInitialized(){return null!==this._messaging}init(e,t){}close(){x(this._messaging,"init",[0])}isPlaying(){return this._status.playing}setInterpolation(e,t){x(this._messaging,"setInterpolation",[e,t])}getGain(){return this._gain}setGain(e){this._gain=e,V(this._messaging,"setGain",[e]).then((()=>V(this._messaging,"getGain",[]))).then((e=>{this._gain=e}))}setChannelType(e,t){x(this._messaging,"setChannelType",[e,t])}waitForVoicesStopped(){return V(this._messaging,"waitForVoicesStopped",[])}loadSFont(e){return V(this._messaging,"loadSFont",[e])}unloadSFont(e){x(this._messaging,"unloadSFont",[e])}unloadSFontAsync(e){return V(this._messaging,"unloadSFont",[e])}getSFontObject(e){const t=new MessageChannel;return V(this._messaging,"getSFontObject",[t.port2,e]).then((e=>new K(t.port1,e)))}getSFontBankOffset(e){return V(this._messaging,"getSFontBankOffset",[e])}setSFontBankOffset(e,t){x(this._messaging,"setSFontBankOffset",[e,t])}render(){throw new Error("Unexpected call")}midiNoteOn(e,t,s){x(this._messaging,"midiNoteOn",[e,t,s])}midiNoteOff(e,t){x(this._messaging,"midiNoteOff",[e,t])}midiKeyPressure(e,t,s){x(this._messaging,"midiKeyPressure",[e,t,s])}midiControl(e,t,s){x(this._messaging,"midiControl",[e,t,s])}midiProgramChange(e,t){x(this._messaging,"midiProgramChange",[e,t])}midiChannelPressure(e,t){x(this._messaging,"midiChannelPressure",[e,t])}midiPitchBend(e,t){x(this._messaging,"midiPitchBend",[e,t])}midiSysEx(e){x(this._messaging,"midiSysEx",[e])}midiPitchWheelSensitivity(e,t){x(this._messaging,"midiPitchWheelSensitivity",[e,t])}midiBankSelect(e,t){x(this._messaging,"midiBankSelect",[e,t])}midiSFontSelect(e,t){x(this._messaging,"midiSFontSelect",[e,t])}midiProgramSelect(e,t,s,n){x(this._messaging,"midiProgramSelect",[e,t,s,n])}midiUnsetProgram(e){x(this._messaging,"midiUnsetProgram",[e])}midiProgramReset(){x(this._messaging,"midiProgramReset",[])}midiSystemReset(){x(this._messaging,"midiSystemReset",[])}midiAllNotesOff(e){x(this._messaging,"midiAllNotesOff",[e])}midiAllSoundsOff(e){x(this._messaging,"midiAllSoundsOff",[e])}midiSetChannelType(e,t){x(this._messaging,"midiSetChannelType",[e,t])}resetPlayer(){return V(this._messaging,"resetPlayer",[])}closePlayer(){x(this._messaging,"closePlayer",[])}isPlayerPlaying(){return this._status.playerPlaying}addSMFDataToPlayer(e){return V(this._messaging,"addSMFDataToPlayer",[e])}playPlayer(){return V(this._messaging,"playPlayer",[])}stopPlayer(){x(this._messaging,"stopPlayer",[])}retrievePlayerCurrentTick(){return V(this._messaging,"retrievePlayerCurrentTick",[])}retrievePlayerTotalTicks(){return V(this._messaging,"retrievePlayerTotalTicks",[])}retrievePlayerBpm(){return V(this._messaging,"retrievePlayerBpm",[])}retrievePlayerMIDITempo(){return V(this._messaging,"retrievePlayerMIDITempo",[])}seekPlayer(e){x(this._messaging,"seekPlayer",[e])}setPlayerLoop(e){x(this._messaging,"setPlayerLoop",[e])}setPlayerTempo(e,t){x(this._messaging,"setPlayerTempo",[e,t])}waitForPlayerStopped(){return V(this._messaging,"waitForPlayerStopped",[])}createSequencer(){const e=new MessageChannel;return V(this._messaging,"createSequencer",[e.port2]).then((()=>new $(e.port1)))}hookPlayerMIDIEventsByName(e,t){return V(this._messaging,"hookPlayerMIDIEventsByName",[e,t])}registerSequencerClientByName(e,t,s,n){return e instanceof $?e.registerSequencerClientByName(t,s,n):Promise.reject(new TypeError("Invalid sequencer object"))}callFunction(e,t){return V(this._messaging,"callFunction",[e,t])}_getRawSynthesizer(){return V(this._messaging,"getRawSynthesizer",[])}_handleLoggingChanged(e){null!=this._messaging&&x(this._messaging,"loggingChanged",[e])}}return t})()));
//# sourceMappingURL=js-synthesizer.min.js.map