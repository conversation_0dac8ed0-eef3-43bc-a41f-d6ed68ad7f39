{"version": 3, "file": "js-synthesizer.min.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAQ,kBAAoBD,IAE5BD,EAAc,QAAIC,GACnB,CATD,CASGK,MAAM,IACT,M,aCTA,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,6SCiH9D,MAAMC,EAAqB,CAC1BC,SAAU,EACVC,YAAa,EACbC,aAAc,GC1GFC,EAAmC,ECTjC,MAAMC,EAEpB,WAAAC,CAAoBC,EAA2BC,GAA3B,KAAAD,KAAAA,EAA2B,KAAAC,QAAAA,CAC/C,CAGO,MAAAC,GACN,OAAO1B,KAAKwB,IACb,CAGO,OAAAG,GACN3B,KAAKwB,KAAOH,CACb,CAEO,OAAAO,GACN,OAAI5B,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQI,sBAAsB7B,KAAKwB,KAChD,CACO,SAAAM,GACN,OAAI9B,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQM,wBAAwB/B,KAAKwB,KAClD,CACO,OAAAQ,GACN,OAAIhC,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQQ,sBAAsBjC,KAAKwB,KAChD,CACO,UAAAU,GACN,OAAIlC,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQU,yBAAyBnC,KAAKwB,KACnD,CACO,MAAAY,GACN,OAAIpC,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQY,qBAAqBrC,KAAKwB,KAC/C,CACO,WAAAc,GACN,OAAItC,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQc,0BAA0BvC,KAAKwB,KACpD,CACO,UAAAgB,GACN,OAAIxC,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQgB,yBAAyBzC,KAAKwB,KACnD,CACO,QAAAkB,GACN,OAAI1C,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQkB,uBAAuB3C,KAAKwB,KACjD,CACO,UAAAoB,GACN,OAAI5C,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQoB,yBAAyB7C,KAAKwB,KACnD,CACO,OAAAsB,GACN,OAAI9C,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQsB,sBAAsB/C,KAAKwB,KAChD,CACO,WAAAwB,GACN,OAAIhD,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQwB,0BAA0BjD,KAAKwB,KACpD,CACO,OAAA0B,GACN,OAAIlD,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQ0B,sBAAsBnD,KAAKwB,KAChD,CACO,QAAA4B,GACN,OAAIpD,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQ4B,uBAAuBrD,KAAKwB,KACjD,CACO,UAAA8B,GACN,OAAItD,KAAKwB,OAASH,GAAyB,EACpCrB,KAAKyB,QAAQ8B,0BAA0BvD,KAAKwB,KACpD,ECpED,MAAMC,EAAkD,oBAA5B+B,wBAC3BA,wBAAwBC,WAAaC,OAwB/B,SAASC,EAAqBC,EAAiBC,GACrD,OAAQA,EAAMC,MACb,KAAK,EACL,IAAK,OACJrC,EAAQsC,kBAAkBH,EAAIC,EAAMG,QAASH,EAAM1D,IAAK0D,EAAMI,IAAKJ,EAAMK,UACzE,MACD,KAAK,EACL,IAAK,SACL,IAAK,UACJzC,EAAQ0C,oBAAoBP,EAAIC,EAAMG,QAASH,EAAM1D,IAAK0D,EAAMI,KAChE,MACD,KAAK,EACL,IAAK,UACL,IAAK,WACJxC,EAAQ2C,qBAAqBR,EAAIC,EAAMG,QAASH,EAAM1D,KACtD,MACD,KAAK,EACL,IAAK,eACL,IAAK,iBACJsB,EAAQ4C,4BAA4BT,EAAIC,EAAMG,SAC9C,MACD,KAAK,EACL,IAAK,cACL,IAAK,gBACJvC,EAAQ6C,2BAA2BV,EAAIC,EAAMG,SAC7C,MACD,KAAK,EACL,IAAK,aACL,IAAK,cACJvC,EAAQ8C,yBAAyBX,EAAIC,EAAMG,QAASH,EAAMW,MAC1D,MACD,KAAK,EACL,IAAK,gBACL,IAAK,iBACJ/C,EAAQgD,4BAA4Bb,EAAIC,EAAMG,QAASH,EAAMa,QAC7D,MACD,KAAK,EACL,IAAK,gBACL,IAAK,iBACJjD,EAAQkD,4BAA4Bf,EAAIC,EAAMG,QAASH,EAAMe,QAASf,EAAMW,KAAMX,EAAMa,QACxF,MACD,KAAK,GACL,IAAK,gBACL,IAAK,iBACJjD,EAAQoD,4BAA4BjB,EAAIC,EAAMG,QAASH,EAAMiB,QAASjB,EAAM7C,OAC5E,MACD,KAAK,EACL,IAAK,YACL,IAAK,aACJS,EAAQsD,wBAAwBnB,EAAIC,EAAMG,QAASH,EAAM7C,OACzD,MACD,KAAK,EACL,IAAK,iBACL,IAAK,wBACL,IAAK,mBACL,IAAK,0BACJS,EAAQuD,6BAA6BpB,EAAIC,EAAMG,QAASH,EAAM7C,OAC9D,MACD,KAAK,GACL,IAAK,aACJS,EAAQwD,wBAAwBrB,EAAIC,EAAMG,QAASH,EAAM7C,OACzD,MACD,KAAK,GACL,IAAK,UACJS,EAAQyD,qBAAqBtB,EAAIC,EAAMG,QAASH,EAAM7C,OACtD,MACD,KAAK,GACL,IAAK,MACJS,EAAQ0D,iBAAiBvB,EAAIC,EAAMG,QAASH,EAAM7C,OAClD,MACD,KAAK,GACL,IAAK,SACJS,EAAQ2D,oBAAoBxB,EAAIC,EAAMG,QAASH,EAAM7C,OACrD,MACD,KAAK,GACL,IAAK,SACL,IAAK,aACL,IAAK,cACJS,EAAQ4D,yBAAyBzB,EAAIC,EAAMG,QAASH,EAAM7C,OAC1D,MACD,KAAK,GACL,IAAK,SACL,IAAK,aACL,IAAK,cACJS,EAAQ6D,yBAAyB1B,EAAIC,EAAMG,QAASH,EAAM7C,OAC1D,MACD,KAAK,GACL,IAAK,cACL,IAAK,eACL,IAAK,aACJS,EAAQ8D,0BAA0B3B,EAAIC,EAAMG,QAASH,EAAM1D,IAAK0D,EAAM7C,OACtE,MACD,KAAK,GACL,IAAK,kBACL,IAAK,mBACL,IAAK,qBACJS,EAAQ+D,8BAA8B5B,EAAIC,EAAMG,QAASH,EAAM7C,OAC/D,MACD,KAAK,GACL,IAAK,cACL,IAAK,eACJS,EAAQgE,0BAA0B7B,GAClC,MACD,KAAK,GACL,IAAK,QACJnC,EAAQiE,mBAAmB9B,EAAIC,EAAM8B,MACrC,MACD,QAEC,OAAO,EAET,OAAO,CACR,CAQO,SAASC,EAAiBD,EAA2B9B,GAC3D,KAAK8B,GAAUA,aAAgBrE,GAC9B,OAAO,EAER,MAAMsC,EAAK+B,EAAKjE,SAChB,OAAIkC,IAAOvC,GAGJsC,EAAqBC,EAAIC,EACjC,CChKe,MAAMgC,UAAqBC,MAMzC,WAAAvE,CAAYwE,EAAkBC,EAAiBC,GAC9CC,MAAMF,GACNhG,KAAK+F,SAAWA,EAChB/F,KAAKiG,OAASA,EACVA,GAAUA,EAAOE,QACpBnG,KAAKmG,MAAQF,EAAOE,MAEtB,ECPc,MAAMC,EAGpB,WAAA7E,CAAoBC,EAA6BC,GAA7B,KAAAD,KAAAA,EAA6B,KAAAC,QAAAA,CACjD,CAEO,OAAAG,GACN,OAAO5B,KAAKyB,QAAQ4E,2BAA2BrG,KAAKwB,KACrD,CACO,OAAA8E,CAAQtF,GACdhB,KAAKyB,QAAQ8E,2BAA2BvG,KAAKwB,KAAMR,EACpD,CACO,UAAAkB,GACN,OAAOlC,KAAKyB,QAAQ+E,8BAA8BxG,KAAKwB,KACxD,CACO,UAAAiF,CAAWzF,GACjBhB,KAAKyB,QAAQiF,8BAA8B1G,KAAKwB,KAAMR,EACvD,CACO,MAAAoB,GACN,OAAOpC,KAAKyB,QAAQkF,0BAA0B3G,KAAKwB,KACpD,CACO,MAAAoF,CAAO5F,GACbhB,KAAKyB,QAAQoF,0BAA0B7G,KAAKwB,KAAMR,EACnD,CACO,WAAAsB,GACN,OAAOtC,KAAKyB,QAAQqF,+BAA+B9G,KAAKwB,KACzD,CACO,WAAAuF,CAAY/F,GAClBhB,KAAKyB,QAAQuF,+BAA+BhH,KAAKwB,KAAMR,EACxD,CACO,UAAAwB,GACN,OAAOxC,KAAKyB,QAAQwF,8BAA8BjH,KAAKwB,KACxD,CACO,UAAA0F,CAAWlG,GACjBhB,KAAKyB,QAAQ0F,8BAA8BnH,KAAKwB,KAAMR,EACvD,CACO,QAAA0B,GACN,OAAO1C,KAAKyB,QAAQ2F,4BAA4BpH,KAAKwB,KACtD,CACO,QAAA6F,CAASrG,GACfhB,KAAKyB,QAAQ6F,4BAA4BtH,KAAKwB,KAAMR,EACrD,CACO,UAAA4B,GACN,OAAO5C,KAAKyB,QAAQ8F,8BAA8BvH,KAAKwB,KACxD,CACO,UAAAgG,CAAWxG,GACjBhB,KAAKyB,QAAQgG,8BAA8BzH,KAAKwB,KAAMR,EACvD,CACO,QAAAoC,GACN,OAAOpD,KAAKyB,QAAQiG,4BAA4B1H,KAAKwB,KACtD,CACO,QAAAmG,CAAS3G,GACfhB,KAAKyB,QAAQmG,4BAA4B5H,KAAKwB,KAAMR,EACrD,CAEO,QAAA6G,CAASlC,GACf,MAAMmC,EAAOnC,EAAKoC,WACZC,EAAmBhI,KAAKyB,QAAQwG,QAAQH,GAC9B,IAAII,WAAWlI,KAAKyB,QAAQ0G,OAAOC,OAAQJ,EAAKF,GACxDO,IAAI1C,GACZ3F,KAAKyB,QAAQ6G,4BAA4BtI,KAAKwB,KAAMwG,EAAKF,EAAM,EAChE,CACO,OAAAS,CAAQ5C,GACd,MAAMmC,EAAOnC,EAAKoC,WACZC,EAAmBhI,KAAKyB,QAAQwG,QAAQH,GAC9B,IAAII,WAAWlI,KAAKyB,QAAQ0G,OAAOC,OAAQJ,EAAKF,GACxDO,IAAI1C,GACZ3F,KAAKyB,QAAQ+G,2BAA2BxI,KAAKwB,KAAMwG,EAAKF,EAAM,EAC/D,CACO,SAAAW,CAAU9C,GAChB,MAAMmC,EAAOnC,EAAKoC,WACZC,EAAmBhI,KAAKyB,QAAQwG,QAAQH,GAC9B,IAAII,WAAWlI,KAAKyB,QAAQ0G,OAAOC,OAAQJ,EAAKF,GACxDO,IAAI1C,GACZ3F,KAAKyB,QAAQiH,6BAA6B1I,KAAKwB,KAAMwG,EAAKF,EAAM,EACjE,EChED,IAAI,EACAa,EAEAC,ECVA,EAEAC,EACAC,ECoBA,EACAC,EACA,EACAC,EAGAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEAC,EACAC,EAEAC,EAyCAC,EC5FA,EACAC,EHwCJ,SAASC,EAAU/F,GAClB,MAAMD,EAAK,EAAQiG,mBACnB,OAAKlG,EAAqBC,EAAIC,GAIvBD,GAHN,EAAQkG,oBAAoBlG,GACrB,KAGT,CAGe,MAAMmG,EAQpB,WAAAxI,GAlCI,IAImC,oBAA5BiC,yBACV,EAAUA,wBAAwBC,WAClCkF,EAAkBnF,wBAAwBwG,qBAE1C,EAAUtG,OACViF,EAAkBsB,gBAGnBrB,EACC,EAAQsB,MAAM,kCAAmC,SAAU,CAAC,SAAU,YAwBtElK,KAAKmK,KAAO9I,EACZrB,KAAKoK,QAAU,EACfpK,KAAKqK,eAAiB,CAAC,CACxB,CAGO,WAAAC,GAIN,OAHAtK,KAAKuK,QACLvK,KAAKmK,KAAO,EAAQK,sBAAsB,GAC1CxK,KAAKoK,QAAU,EACRK,QAAQC,SAChB,CAGO,MAAAhJ,GACN,OAAO1B,KAAKmK,IACb,CAEO,KAAAI,GACFvK,KAAKmK,OAAS9I,IACjBhB,OAAOsK,KAAK3K,KAAKqK,gBAAgBO,SAASC,IACzC7K,KAAK8K,iBAAiBC,OAAOF,GAAa,IAE3C7K,KAAK8K,kBAAkB,GACvB,EAAQE,wBAAwBhL,KAAKmK,MACrCnK,KAAKmK,KAAO9I,EAEd,CAEO,mBAAA4J,CAAoBC,GAK1B,IAAIC,EACJ,IALqB,IAAjBnL,KAAKoK,SACR,EAAQgB,mCAAmCpL,KAAKmK,KAAMnK,KAAKoK,QAC3DpK,KAAKoK,QAAU,GAGK,iBAAVc,EACVC,EAAMD,MACA,MAAIA,aAAiBG,GAG3B,OAAOZ,QAAQa,OAAO,IAAIC,UAAU,8CAFpCJ,EAAMD,EAAMM,mB,CAMb,OADAxL,KAAKoK,OAAS,EAAQqB,qCAAqCzL,KAAKmK,KAAMgB,GAC/DV,QAAQC,QAAQ1K,KAAKoK,OAC7B,CAEO,gBAAAU,CAAiBY,GACvB,IAAkB,IAAdA,IAEe,KADlBA,EAAW1L,KAAKoK,QAEf,OAKF,MAAMxG,EAAK,EAAQiG,mBAQnB,GAPA,EAAQ8B,wBAAwB/H,GAAK,GACrC,EAAQgI,sBAAsBhI,EAAI8H,GAClC,EAAQG,2BAA2BjI,GACnC,EAAQkI,0BAA0B9L,KAAKmK,KAAMvG,GAC7C,EAAQkG,oBAAoBlG,GAE5B,EAAQwH,mCAAmCpL,KAAKmK,KAAMuB,GAClD1L,KAAKoK,SAAWsB,EACnB1L,KAAKoK,QAAU,MACT,CACN,MAAM2B,EAAM/L,KAAKqK,eACb0B,EAAIL,KACP/C,EAAgBoD,EAAIL,WACbK,EAAIL,G,CAGd,CAEO,uBAAAM,GACN,MAAMC,EAAI,EAAQC,+BAA+BlM,KAAKmK,MAChDgC,EAAkB,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIH,IAAKG,EAAG,CAC3B,MAAMC,EAAK,EAAQC,+BAA+BtM,KAAKmK,KAAMiC,GACvDG,EAAO3D,EAAgC5I,KAAKmK,KAAMkC,GACxDF,EAAEK,KAAK,CAAEd,SAAUW,EAAIE,KAAMA,G,CAE9B,OAAO9B,QAAQC,QAAQyB,EACxB,CAEO,cAAAM,GACN,OAAOhC,QAAQC,QAAgB,EAAQwB,+BAA+BlM,KAAKmK,MAC5E,CAEO,aAAAuC,CAAcC,GACpB,MAAMN,EAAK,EAAQC,+BAA+BtM,KAAKmK,KAAMwC,GACvDJ,EAAO3D,EAAgC5I,KAAKmK,KAAMkC,GACxD,OAAO5B,QAAQC,QAAoB,CAAEgB,SAAUW,EAAIE,KAAMA,GAC1D,CAEO,YAAAK,CAAaC,GACnB,EAAQC,gCAAgC9M,KAAKmK,KAAM0C,EACpD,CAEO,YAAAE,GACN,OAAOtC,QAAQC,QAAQ,EAAQsC,gCAAgChN,KAAKmK,MACrE,CAEO,OAAA8C,GACN,OAAOxC,QAAQC,QAAQ,EAAQwC,0BAA0BlN,KAAKmK,MAC/D,CAEO,WAAAgD,CAAYtJ,EAAuBuJ,EAAcC,GACvD,MAAMzJ,EAAKgG,EAAU/F,GACrB,GAAW,OAAPD,EAAa,CAEhB,MAAM0J,EAAQ,EAAQpB,+BAA+BlM,KAAKmK,MAC1D,IAAK,IAAIiC,EAAI,EAAGA,EAAIkB,IAASlB,EAAG,CAC/B,MAAMC,EAAa,EAAQC,+BAA+BtM,KAAKmK,KAAMiC,GACrE,EAAQR,sBAAsBhI,EAAIyI,GAClC,EAAQkB,yBAAyBvN,KAAKmK,KAAMvG,EAAIwJ,EAAMC,EAAa,EAAI,E,CAExE,EAAQvD,oBAAoBlG,E,CAE9B,CAEO,mBAAA4J,CAAoB9B,EAAkB7H,EAAuBuJ,EAAcC,GACjF,MAAMzJ,EAAKgG,EAAU/F,GACV,OAAPD,IACH,EAAQgI,sBAAsBhI,GAAkB,IAAd8H,EAAkB1L,KAAKoK,OAASsB,GAClE,EAAQ6B,yBAAyBvN,KAAKmK,KAAMvG,EAAIwJ,EAAMC,EAAa,EAAI,GACvE,EAAQvD,oBAAoBlG,GAE9B,CAGO,oBAAA6J,CAAqB/B,EAAkB7H,GAC7C,MAAMD,EAAKgG,EAAU/F,GACV,OAAPD,IACH,EAAQgI,sBAAsBhI,GAAkB,IAAd8H,EAAkB1L,KAAKoK,OAASsB,GAClE,EAAQI,0BAA0B9L,KAAKmK,KAAMvG,GAC7C,EAAQkG,oBAAoBlG,GAE9B,CAGO,YAAA8J,CAAahC,EAAkBiC,GACrC,KAAMA,aAAqBrM,GAC1B,OAED,MAAMsC,EAAK+J,EAAUjM,SACjBkC,IAAOvC,IACV,EAAQuK,sBAAsBhI,GAAkB,IAAd8H,EAAkB1L,KAAKoK,OAASsB,GAClE,EAAQI,0BAA0B9L,KAAKmK,KAAMvG,GAE/C,CAEO,eAAAgK,GACN,EAAQC,+BAA+B7N,KAAKmK,MAAO,GAAI,GAAI,EAC5D,CAEO,yBAAA2D,CAA0BpC,GAChC,EAAQmC,+BAA+B7N,KAAKmK,MAAO,GAAiB,IAAduB,EAAkB1L,KAAKoK,OAASsB,GAAW,EAClG,CAEO,gBAAAqC,CAAiBC,GACnBhO,KAAKmK,OAAS9I,GACjB,EAAQ4M,yBAAyBjO,KAAKmK,KAAM6D,EAE9C,CAGO,uBAAAE,CAAwBC,GAC9B,OAAOC,aAAY,IAAMpO,KAAK+N,iBAAiBI,IAAOA,EACvD,ECtMc,MAAME,EAIpB,YAAmBC,GAClBtO,KAAKwB,KAAO8M,CACb,CAEO,uBAAOC,CAAiBrD,EAAoBmB,GAxB/C,IAKH,EADsC,oBAA5B7I,wBACAA,wBAAwBC,WAExBC,OAGXmF,EACC,EAAQqB,MAAM,uBAAwB,SAAU,CAAC,WAClDpB,EACC,EAAQoB,MAAM,wBAAyB,SAAU,CAAC,YAclD,MAAMsE,EAAQ,EAAQC,6BAA6BvD,EAAMM,oBAAqBa,GAC9E,OAAImC,IAAUnN,EACN,KAED,IAAIgN,EAAUG,EACtB,CAEO,OAAAE,GACN,OAAO7F,EAAqB7I,KAAKwB,KAClC,CAEO,SAAAmN,CAAUnK,EAAcoK,GAC9B,MAAMC,EAA2B,EAAQC,wBAAwB9O,KAAKwB,KAAMgD,EAAMoK,GAClF,GAAIC,IAAcxN,EACjB,OAAO,KAKR,MAAO,CACN0N,UAAW/O,KACXuM,KALYzD,EAAsB+F,GAMlCG,QALe,EAAQC,0BAA0BJ,GAMjDK,IALW,EAAQC,sBAAsBN,GAO3C,CAEO,iBAAAO,GACN,MAAMC,EAAQ,KACb,EAAQC,6BAA6BtP,KAAKwB,KAAK,EAE1C+N,EAAO,KACZ,MAAMV,EAAY,EAAQW,4BAA4BxP,KAAKwB,MAC3D,GAAkB,IAAdqN,EACH,MAAO,CACNY,MAAM,EACNzO,WAAO0O,GAMR,MAAO,CACND,MAAM,EACNzO,MAAO,CACN+N,UAAW/O,KACXuM,KAPWzD,EAAsB+F,GAQjCG,QAPc,EAAQC,0BAA0BJ,GAQhDK,IAPU,EAAQC,sBAAsBN,I,EAkB5C,MAAO,CACN,CAAC/N,OAAO6O,UAPQ,KAChBN,IACO,CACNE,SAMH,ECtDD,SAAS,IACR,IAAInG,EAAJ,CAKA,GAAuC,oBAA5B5F,wBACV,EAAUA,wBAAwBC,WAClCsF,EAAevF,wBAAwBoM,gBACvC,EAAkBpM,wBAAwBwG,uBACpC,IAAsB,oBAAXtG,OAKjB,MAAM,IAAIoC,MAAM,oEAJhB,EAAUpC,OACVqF,EAAe8G,YACf,EAAkB5F,c,CAInBjB,EAAM,EAAQ8G,GAGd7G,EACC,EAAQiB,MAAM,wBAAyB,SAAU,CAAC,SAAU,SAAU,WACvEhB,EACC,EAAQgB,MAAM,wBAAyB,SAAU,CAAC,SAAU,SAAU,WACvEf,EACC,EAAQe,MAAM,wBAAyB,SAAU,CAAC,SAAU,SAAU,WACvEd,EACC,EAAQc,MAAM,oBAAqB,SAAU,CAAC,WAC/Cb,EACC,EAAQa,MAAM,qBAAsB,SAAU,CAAC,SAAU,SAAU,WACpEZ,EACC,EAAQY,MAAM,kCAAmC,SAAU,CAAC,SAAU,SAAU,SAAU,WAE3FX,EAAS,EAAQtB,QAAQ8H,KAAK,GAC9BvG,EAAO,EAAQwG,MAAMD,KAAK,GAE1BtG,EAA2B,EAAQwG,+BAA+BF,KAAK,E,CACxE,CAyCA,SAASG,EAAwBC,EAAsB5D,EAAcvL,QAC/C,IAAVA,GACViI,EAAsBkH,EAAU5D,EAAMvL,EAAQ,EAAI,EAEpD,CACA,SAASoP,EAAuBD,EAAsB5D,EAAcvL,QAC9C,IAAVA,GACViI,EAAsBkH,EAAU5D,EAAMvL,EAExC,CACA,SAASqP,EAAuBF,EAAsB5D,EAAcvL,QAC9C,IAAVA,GACVkI,EAAsBiH,EAAU5D,EAAMvL,EAExC,CA8He,MAAMqK,EA+BpB,WAAA9J,GACC,IAEAvB,KAAKsQ,UAAYjP,EACjBrB,KAAKuQ,OAASlP,EACdrB,KAAKwQ,QAAUnP,EACfrB,KAAKyQ,gBAAiB,EACtBzQ,KAAK0Q,mBAAqB,KAC1B1Q,KAAK2Q,oBAAsB,KAE3B3Q,KAAK4Q,QAAUvP,EACfrB,KAAK6Q,YAAc,EACnB7Q,KAAK8Q,QAAUzP,EAEfrB,KAAK+Q,MAAQ,EACd,CAGO,6BAAOC,GACb,OApOF,WACC,GAAItH,EACH,OAAOA,EAGR,IAAIuH,EACAC,EACJ,GAAuC,oBAA5B1N,wBACVyN,EAAMzN,wBAAwBC,WAC9ByN,EAAiB1N,wBAAwB2N,iBACnC,IAAsB,oBAAXzN,OAIjB,OAAO+G,QAAQa,OAAO,IAAIxF,MAAM,qEAHhCmL,EAAMvN,OACNwN,EAAyC,oBAAjBC,aAA+BA,kBAAezB,C,CAIvE,OAAIuB,EAAIG,WACP1H,EAA4Be,QAAQC,UAC7BhB,IAGPA,EAA4B,IAAIe,aADH,IAAnByG,EAC+BxG,IACxC,MAAM2G,EAA+B,EAAQC,qBAC7C,EAAQA,qBAAuB,KAC9B5G,IACI2G,GACHA,G,CAED,EAGuC3G,IACxCwG,EAAgBxG,EAAQ,GAGnBhB,EACR,CAgMS6H,EACR,CAEO,aAAAC,GACN,OAAOxR,KAAKuQ,SAAWlP,CACxB,CAGO,iBAAAmK,GACN,OAAOxL,KAAKuQ,MACb,CAEO,eAAAkB,CACNC,EACAC,GAEA,MAAMC,EAAOF,EAAQG,sBAAsBF,EAAW,EAAG,GAIzD,OAHAC,EAAKE,iBAAiB,gBAAiBlO,IACtC5D,KAAK+R,OAAOnO,EAAGoO,aAAa,IAEtBJ,CACR,CAEO,IAAAK,CAAKC,EAAoB/B,GAC/BnQ,KAAKuK,QAEL,MAAMlC,EAAOrI,KAAKsQ,UAAY,EAAQ6B,sBACtCjJ,EAAsBb,EAAK,oBAAqB6J,GAC5C/B,SACiC,IAAzBA,EAASiC,cACnBpS,KAAK+Q,MAAQZ,EAASiC,aAEvBlC,EACC7H,EACA,sBACA8H,EAASkC,cAEVhC,EACChI,EACA,qBACA8H,EAASmC,aAEVjC,EACChI,EACA,qBACA8H,EAASoC,aAEVnC,EAAuB/H,EAAK,kBAAmB8H,EAASqC,UACxDnC,EACChI,EACA,qBACA8H,EAASsC,aAEVrC,EACC/H,EACA,sBACA8H,EAASuC,kBAvOb,SAAgCvC,EAAsB5D,EAAcvL,QAC9C,IAAVA,GACVmI,EAAsBgH,EAAU5D,EAAMvL,EAExC,CAqOG2R,CACCtK,EACA,yBACA8H,EAASyC,gBAEVxC,EACC/H,EACA,wBACA8H,EAAS0C,eAEVxC,EACChI,EACA,qBACA8H,EAAS2C,aAEVzC,EACChI,EACA,2BACA8H,EAAS4C,6BAEwC,IAAvC5C,EAAS6C,2BACnB7J,EACCd,EACA,oCACA8H,EAAS6C,0BAA0BC,KAAK,MAG1C5C,EACChI,EACA,4BACA8H,EAAS+C,oBAEV7C,EACChI,EACA,0BACA8H,EAASgD,kBAEV9C,EACChI,EACA,2BACA8H,EAASiD,mBAEV/C,EACChI,EACA,wBACA8H,EAASkD,gBAEVjD,EAAuB/H,EAAK,kBAAmB8H,EAASmD,WACxDpD,EACC7H,EACA,sBACA8H,EAASoD,cAEVlD,EACChI,EACA,oBACA8H,EAASqD,YAEVnD,EACChI,EACA,qBACA8H,EAASsD,aAEVpD,EACChI,EACA,yBACA8H,EAASuD,gBAEVrD,EACChI,EACA,qBACA8H,EAASwD,cAGXzK,EAAsBb,EAAK,aAAcrI,KAAK+Q,OAE9C/Q,KAAKuQ,OAAS,EAAQqD,iBAAiB5T,KAAKsQ,WAE5CtQ,KAAK8Q,QAAUvH,EAAO,EACvB,CAEO,KAAAgB,GACFvK,KAAKuQ,SAAWlP,IAGpBrB,KAAK6T,eACL,EAAQC,oBAAoB9T,KAAKuQ,QACjCvQ,KAAKuQ,OAASlP,EACd,EAAQ0S,uBAAuB/T,KAAKsQ,WACpCtQ,KAAKsQ,UAAYjP,EACjBmI,EAAKxJ,KAAK8Q,SACV9Q,KAAK8Q,QAAUzP,EAChB,CAEO,SAAA2S,GACN,OACChU,KAAKuQ,SAAWlP,GAnUnB,SAA6B6J,GAC5B,MAAM+I,EAAc,EAAQC,oCAAoChJ,GAChE,IAAK+I,EACJ,OAAO,EAcR,IAAIE,EAAoB,IACpBC,EAA4BlJ,EAAQiJ,EAAoB,GAAM,EAC9DE,EAAyB,EAAQC,QAAQF,GAC7C,GAAIC,IAA2BJ,IAE9BE,GAAqB,EACrBC,EAA4BlJ,EAAQiJ,EAAoB,GAAM,EAC9DE,EAAyB,EAAQC,QAAQF,GACrCC,IAA2BJ,GAM9B,OAJUM,QACRC,KACD,2EAEMP,EAIT,MAAMQ,EAAY,EAAQH,QAASpJ,EAAQiJ,GAAsB,GAEjE,IAAKM,GAAaA,GAAa,EAAQH,QAAQvM,WAM9C,OAJUwM,QACRC,KACD,2EAEMP,EAIR,MAAMS,EAAa,EAAQC,2BAA2BzJ,GACtD,IAAI0J,GAAY,EAChB,IAAK,IAAIxI,EAAI,EAAGA,EAAIsI,IAActI,EAAG,CAEpC,MAAMyI,EAAQ,EAAQP,SAASG,GAAa,GAAKrI,GACjD,GAAKyI,GAMU,IAFA,EAAQ1M,OAAO0M,EAAQ,GAEpB,CACjBD,GAAY,EACZ,K,EAGF,IAAKA,EASJ,OAR+B,IAA3BP,GACOE,QACRC,KACD,0EACAH,GAGF,EAAQC,QAAQF,GAA4B,EACrC,EAGR,OAAOH,CACR,CAwPGa,CAAoB9U,KAAKuQ,QAAU,CAErC,CAEO,gBAAAwE,CAAiB/T,EAA4BgD,GACnDhE,KAAKgV,yBACkB,IAAZhR,IACVA,GAAW,GAEZ,EAAQiR,+BAA+BjV,KAAKuQ,OAAQvM,EAAShD,EAC9D,CAEO,OAAAkU,GACN,OAAOlV,KAAK+Q,KACb,CAEO,OAAAoE,CAAQC,GACdpV,KAAKgV,oBACL,EAAQK,sBAAsBrV,KAAKuQ,OAAQ6E,GAC3CpV,KAAK+Q,MAAQ,EAAQuE,sBAAsBtV,KAAKuQ,OACjD,CAEO,cAAAgF,CAAevR,EAAiBwR,GACtCxV,KAAKgV,oBAEL,EAAQS,8BACPzV,KAAKuQ,OACLvM,EACAwR,EAAS,EAAI,EAEf,CAEO,oBAAAE,GACN,OAAO1V,KAAK2V,kBACb,CAEO,SAAAC,CAAUC,GAChB7V,KAAKgV,oBAEL,MAAMzI,GA7RkCuJ,EA6RC,OA5RnC,IA4R0B,YA5RG,MAAhBC,KAAKC,YAAoC,MAAhBD,KAAKC,WAAmBF,KADtE,IAA0CA,EA8RxC,MAAMG,EAAK,IAAI/N,WAAW2N,GAE1B7M,EAAIkN,UAAU3J,EAAM0J,GACpB,MAAMzH,EAAQnF,EAAmBrJ,KAAKuQ,OAAQhE,EAAM,GAEpD,OADAvD,EAAImN,OAAO5J,IACO,IAAXiC,EACJ/D,QAAQa,OAAO,IAAIxF,MAAMsD,EAAmBpJ,KAAKuQ,UACjD9F,QAAQC,QAAQ8D,EACpB,CAEO,WAAA4H,CAAY/J,GAClBrM,KAAKgV,oBACLhV,KAAKqW,aACLrW,KAAKsW,kBAEL,EAAQC,sBAAsBvW,KAAKuQ,OAAQlE,EAAI,EAChD,CAEO,gBAAAmK,CAAiBnK,GAIvB,OAFArM,KAAKgV,oBACLhV,KAAKqW,aACErW,KAAK2V,mBAAmBc,MAAK,KACnC,EAAQF,sBAAsBvW,KAAKuQ,OAAQlE,EAAI,EAAE,GAEnD,CAOO,cAAAqK,CAAe9R,GACrB,OAAOyJ,EAAUE,iBAAiBvO,KAAM4E,EACzC,CAEO,kBAAA+R,CAAmBtK,GAEzB,OADArM,KAAKgV,oBACEvK,QAAQC,QACd,EAAQkM,6BAA6B5W,KAAKuQ,OAAQlE,GAEpD,CACO,kBAAAwK,CAAmBxK,EAAYyK,GACrC9W,KAAKgV,oBACL,EAAQ+B,6BAA6B/W,KAAKuQ,OAAQlE,EAAIyK,EACvD,CAEO,MAAA/E,CAAOiF,GACb,MAAMC,EACL,qBAAsBD,EACnBA,EAAUE,OACVF,EAAU,GAAGE,OACXC,EACL,qBAAsBH,EACnBA,EAAUI,iBACVJ,EAAUE,OACRG,EAAiB,EAAIJ,EACrBK,EAA6B,EAAjBD,EACdrX,KAAK6Q,YAAcyG,IAClBtX,KAAK4Q,UAAYvP,GACpBmI,EAAKxJ,KAAK4Q,SAEX5Q,KAAK4Q,QAAUrH,EAAO+N,GACtBtX,KAAK6Q,YAAcyG,GAGpB,MAAMC,EAAUvX,KAAK4Q,QACf4G,EAAaxX,KAAK4Q,QACvByG,EACDrX,KAAKyX,UAAUF,EAASC,EAAUP,GAElC,MAAMS,EAAQ,IAAIC,aACjB,EAAQxP,OAAOC,OACfmP,EACAN,GAEKW,EACLT,GAAY,EACT,IAAIQ,aAAa,EAAQxP,OAAOC,OAAQoP,EAAUP,GAClD,KACJ,GAAI,qBAAsBD,EACzB,GAAIA,EAAUa,cACbb,EAAUa,cAAcH,EAAO,EAAG,GAC9BE,GACHZ,EAAUa,cAAcD,EAAQ,EAAG,OAE9B,CAEN,MAAME,EAAWd,EAAUe,eAAe,GAE1C,GADAL,EAAM9M,SAAQ,CAACO,EAAKiB,IAAO0L,EAAS1L,GAAKjB,IACrCyM,EAAQ,CACX,MAAMI,EAAYhB,EAAUe,eAAe,GAC3CH,EAAOhN,SAAQ,CAACO,EAAKiB,IAAO4L,EAAU5L,GAAKjB,G,OAI7C6L,EAAU,GAAG3O,IAAIqP,GACbE,GACHZ,EAAU,GAAG3O,IAAIuP,GAKnB5X,KAAKiY,iBACN,CAEO,UAAAC,CAAWC,EAAchY,EAAa8D,GAC5C,EAAQmU,oBAAoBpY,KAAKuQ,OAAQ4H,EAAMhY,EAAK8D,EACrD,CACO,WAAAoU,CAAYF,EAAchY,GAChC,EAAQmY,qBAAqBtY,KAAKuQ,OAAQ4H,EAAMhY,EACjD,CACO,eAAAoY,CAAgBJ,EAAchY,EAAagL,GACjD,EAAQqN,0BAA0BxY,KAAKuQ,OAAQ4H,EAAMhY,EAAKgL,EAC3D,CACO,WAAAsN,CAAYN,EAAcO,EAAcvN,GAC9C,EAAQwN,gBAAgB3Y,KAAKuQ,OAAQ4H,EAAMO,EAAMvN,EAClD,CACO,iBAAAyN,CAAkBT,EAAcU,GACtC,EAAQC,4BAA4B9Y,KAAKuQ,OAAQ4H,EAAMU,EACxD,CACO,mBAAAE,CAAoBZ,EAAchN,GACxC,EAAQ6N,8BAA8BhZ,KAAKuQ,OAAQ4H,EAAMhN,EAC1D,CACO,aAAA8N,CAAcd,EAAchN,GAClC,EAAQ+N,wBAAwBlZ,KAAKuQ,OAAQ4H,EAAMhN,EACpD,CACO,SAAAgO,CAAUxT,GAChB,MAAMyT,EAAMzT,EAAKoC,WACXsR,EAAM9P,EAAO6P,GACnB,EAAQjR,OAAOE,IAAI1C,EAAM0T,GACzB,EAAQC,mBACPtZ,KAAKuQ,OACL8I,EACAD,EACA/X,EACAA,EACAA,EACA,GAEDmI,EAAK6P,EACN,CAEO,yBAAAE,CAA0BpB,EAAchN,GAC9C,EAAQqO,8BAA8BxZ,KAAKuQ,OAAQ4H,EAAMhN,EAC1D,CACO,cAAAyH,CAAeuF,EAAc3T,GACnC,EAAQiV,yBAAyBzZ,KAAKuQ,OAAQ4H,EAAM3T,EACrD,CACO,eAAAkV,CAAgBvB,EAAcvT,GACpC,EAAQ+U,0BAA0B3Z,KAAKuQ,OAAQ4H,EAAMvT,EACtD,CACO,iBAAAgV,CACNzB,EACAvT,EACAJ,EACAoK,GAEA,EAAQiL,4BACP7Z,KAAKuQ,OACL4H,EACAvT,EACAJ,EACAoK,EAEF,CACO,gBAAAkL,CAAiB3B,GACvB,EAAQ4B,2BAA2B/Z,KAAKuQ,OAAQ4H,EACjD,CACO,gBAAA6B,GACN,EAAQC,2BAA2Bja,KAAKuQ,OACzC,CACO,eAAA2J,GACN,EAAQC,0BAA0Bna,KAAKuQ,OACxC,CACO,eAAA6J,CAAgBjC,GACtB,EAAQkC,2BACPra,KAAKuQ,YACW,IAAT4H,GAAwB,EAAIA,EAErC,CACO,gBAAAmC,CAAiBnC,GACvB,EAAQoC,4BACPva,KAAKuQ,YACW,IAAT4H,GAAwB,EAAIA,EAErC,CACO,kBAAAqC,CAAmBrC,EAAc3C,GAGvC,EAAQC,8BACPzV,KAAKuQ,OACL4H,EACA3C,EAAS,EAAI,EAEf,CAKO,SAAAiF,CACNC,EACAC,EACAC,EACAC,GAEA,EAAQC,wBACP9a,KAAKuQ,OACLmK,EACAC,EACAC,EACAC,EAEF,CAIO,iBAAAE,CAAkBL,GACxB,EAAQM,iCAAiChb,KAAKuQ,OAAQmK,EACvD,CAIO,aAAAO,CAAcN,GACpB,EAAQO,6BAA6Blb,KAAKuQ,OAAQoK,EACnD,CAIO,cAAAQ,CAAeP,GACrB,EAAQQ,8BAA8Bpb,KAAKuQ,OAAQqK,EACpD,CAIO,cAAAS,CAAeR,GACrB,EAAQS,8BAA8Btb,KAAKuQ,OAAQsK,EACpD,CAIO,WAAAU,CAAYC,GAClB,EAAQC,2BAA2Bzb,KAAKuQ,OAAQiL,EAAK,EAAI,EAC1D,CAIO,iBAAAE,GACN,OAAO,EAAQC,iCAAiC3b,KAAKuQ,OACtD,CAIO,aAAAqL,GACN,OAAO,EAAQC,6BAA6B7b,KAAKuQ,OAClD,CAIO,cAAAuL,GACN,OAAO,EAAQC,8BAA8B/b,KAAKuQ,OACnD,CAIO,cAAAyL,GACN,OAAO,EAAQC,8BAA8Bjc,KAAKuQ,OACnD,CAKO,SAAA2L,CACNxH,EACAmG,EACAsB,EACAC,EACAtY,GAEA,EAAQuY,wBACPrc,KAAKuQ,OACLmE,EACAmG,EACAsB,EACAC,EACAtY,EAEF,CAIO,mBAAAwY,CAAoB5H,GAC1B,EAAQ6H,2BAA2Bvc,KAAKuQ,OAAQmE,EACjD,CAIO,cAAA8H,CAAe3B,GACrB,EAAQ4B,8BAA8Bzc,KAAKuQ,OAAQsK,EACpD,CAIO,cAAA6B,CAAeP,GACrB,EAAQQ,8BAA8B3c,KAAKuQ,OAAQ4L,EACpD,CAIO,cAAAS,CAAeR,GACrB,EAAQS,8BAA8B7c,KAAKuQ,OAAQ6L,EACpD,CAIO,aAAAU,CAAchZ,GACpB,EAAQiZ,6BAA6B/c,KAAKuQ,OAAQzM,EACnD,CAIO,WAAAkZ,CAAYxB,GAClB,EAAQyB,2BAA2Bjd,KAAKuQ,OAAQiL,EAAK,EAAI,EAC1D,CAIO,mBAAA0B,GACN,OAAO,EAAQC,2BAA2Bnd,KAAKuQ,OAChD,CAIO,cAAA6M,GACN,OAAO,EAAQC,8BAA8Brd,KAAKuQ,OACnD,CAIO,cAAA+M,GACN,OAAO,EAAQC,8BAA8Bvd,KAAKuQ,OACnD,CAIO,cAAAiN,GACN,OAAO,EAAQC,8BAA8Bzd,KAAKuQ,OACnD,CAIO,aAAAmN,GACN,OAAO,EAAQC,6BAA6B3d,KAAKuQ,OAClD,CAQO,YAAAqN,CAAa5Z,EAAiB6Z,GACpC,OAAO,EAAQC,qBAAqB9d,KAAKuQ,OAAQvM,EAAS6Z,EAC3D,CAOO,YAAAE,CAAa/Z,EAAiB6Z,EAAuB7c,GAC3D,EAAQgd,qBAAqBhe,KAAKuQ,OAAQvM,EAAS6Z,EAAO7c,EAC3D,CAMO,aAAAid,CAAcja,GAMpB,OALA,EAAQka,6BACPle,KAAKuQ,OACLvM,EACAhE,KAAK8Q,SAEC,EAAQqN,OAAQne,KAAK8Q,SAAsB,EACnD,CAMO,aAAAsN,CAAcpa,EAAiBqa,GACrC,EAAQC,6BAA6Bte,KAAKuQ,OAAQvM,EAASqa,EAC5D,CAMO,iBAAAE,CAAkBva,GAMxB,OALA,EAAQwa,iCACPxe,KAAKuQ,OACLvM,EACAhE,KAAK8Q,SAEC,EAAQqN,OAAQne,KAAK8Q,SAAsB,EACnD,CAMO,iBAAA2N,CAAkBza,EAAiBqa,GACzC,EAAQK,iCAAiC1e,KAAKuQ,OAAQvM,EAASqa,EAChE,CAMO,aAAAM,CAAc3a,GAMpB,OALA,EAAQ4a,6BACP5e,KAAKuQ,OACLvM,EACAhE,KAAK8Q,SAEC,EAAQqN,OAAQne,KAAK8Q,SAAsB,EACnD,CAMO,aAAA+N,CAAc7a,EAAiB8a,GACrC,EAAQC,6BAA6B/e,KAAKuQ,OAAQvM,EAAS8a,EAC5D,CAIO,WAAAE,GACN,OAAO,IAAIvU,SAAeC,IACzB1K,KAAKif,cACLvU,GAAS,GAEX,CAEO,WAAAwU,GACNlf,KAAK6T,cACN,CAGQ,WAAAoL,GACPjf,KAAK6T,eAEL,MAAMsL,EAAS,EAAQC,kBAAkBpf,KAAKuQ,QAE9C,GADAvQ,KAAKwQ,QAAU2O,EACXA,IAAW9d,EAcd,MAAM,IAAIyE,MAAM,iBAbhB,GAAiC,OAA7B9F,KAAK2Q,oBAA8B,CAItC,MAAM0O,EACL,EAAQ/K,QAAU6K,EAAoB,KAAQ,GAE9C,EAAQ7K,QAAU6K,EAAoB,KAAQ,KAC9Bnf,KAAKuQ,SACrBvQ,KAAK2Q,oBAAsB0O,E,CAM/B,CAGQ,YAAAxL,GACP,MAAMyL,EAAItf,KAAKwQ,QACX8O,IAAMje,IAGVrB,KAAKqW,aACL,EAAQkJ,qBAAqBD,GAC7Btf,KAAKwQ,QAAUnP,EACfrB,KAAK0Q,mBAAqB,KAC3B,CAEO,eAAAuH,GACN,GAAIjY,KAAKyQ,eAAgB,CAExB,GAAe,IADA,EAAQ+O,yBAAyBxf,KAAKwQ,SAEpD,OAAO,EAERxQ,KAAKqW,Y,CAEN,OAAO,CACR,CAEO,kBAAAoJ,CAAmB5J,GACzB7V,KAAK0f,0BACL,MAAMtG,EAAMvD,EAAI9N,WACVsR,EAAM9P,EAAO6P,GACnB,EAAQjR,OAAOE,IAAI,IAAIH,WAAW2N,GAAMwD,GACxC,MAAMlN,EAAY,EAAQwT,sBAAsB3f,KAAKwQ,QAAS6I,EAAKD,GAEnE,OADA5P,EAAK6P,IACS,IAAPlN,EACJ1B,QAAQC,UACRD,QAAQa,OAAO,IAAIxF,MAAMsD,EAAmBpJ,KAAKuQ,SACrD,CAEO,UAAAqP,GAMN,GALA5f,KAAK0f,0BACD1f,KAAKyQ,gBACRzQ,KAAKqW,cAG4C,IAA9C,EAAQwJ,mBAAmB7f,KAAKwQ,SACnC,OAAO/F,QAAQa,OAAO,IAAIxF,MAAMsD,EAAmBpJ,KAAKuQ,UAEzDvQ,KAAKyQ,gBAAiB,EACtB,IAAIqP,EAAW,OACf,MAAMR,EAAI,IAAI7U,SAAeC,IAC5BoV,EAAWpV,CAAO,IAMnB,OAJA1K,KAAK+f,aAAe,CACnBC,QAASV,EACT5U,QAASoV,GAEHrV,QAAQC,SAChB,CAEO,UAAA2L,GACN,MAAMiJ,EAAItf,KAAKwQ,QACX8O,IAAMje,GAAoBrB,KAAKyQ,iBAGnC,EAAQwP,mBAAmBX,GAC3B,EAAQY,mBAAmBZ,GAC3B,EAAQ/E,4BAA4Bva,KAAKuQ,QAAS,GAC9CvQ,KAAK+f,eACR/f,KAAK+f,aAAarV,UAClB1K,KAAK+f,kBAAe,GAErB/f,KAAKyQ,gBAAiB,EACvB,CAEO,yBAAA0P,GAEN,OADAngB,KAAK0f,0BACEjV,QAAQC,QACd,EAAQ0V,+BAA+BpgB,KAAKwQ,SAE9C,CACO,wBAAA6P,GAEN,OADArgB,KAAK0f,0BACEjV,QAAQC,QACd,EAAQ4V,8BAA8BtgB,KAAKwQ,SAE7C,CACO,iBAAA+P,GAEN,OADAvgB,KAAK0f,0BACEjV,QAAQC,QAAQ,EAAQ8V,sBAAsBxgB,KAAKwQ,SAC3D,CACO,uBAAAiQ,GAEN,OADAzgB,KAAK0f,0BACEjV,QAAQC,QACd,EAAQgW,6BAA6B1gB,KAAKwQ,SAE5C,CACO,UAAAmQ,CAAWC,GACjB5gB,KAAK0f,0BACL,EAAQmB,mBAAmB7gB,KAAKwQ,QAASoQ,EAC1C,CACO,aAAAE,CAAcC,GACpB/gB,KAAK0f,0BACL,EAAQsB,uBAAuBhhB,KAAKwQ,QAASuQ,EAC9C,CACO,cAAAE,CAAeC,EAA+BC,GACpDnhB,KAAK0f,0BACL,EAAQ0B,wBAAwBphB,KAAKwQ,QAAS0Q,EAAWC,EAC1D,CAQO,oBAAAE,CACNC,EACAzD,GAEA7d,KAAK0f,0BAEL,MAAM6B,EAASvhB,KAAK0Q,mBACpB,GAAe,OAAX6Q,GAAgC,OAAbD,EACtB,OAED,MAAME,EAEQ,OAAbF,EACGvY,EAv1BN,SAA+BmC,EAAoBuW,EAA2B5D,GAC7E,MAAO,CAAClY,EAAmB9B,KAC1B,MAAM6d,EAAI,EAAQrb,2BAA2BxC,GAC7C,OAAI4d,EAAGvW,EAAOwW,EAAG,IAAItb,EAAUvC,EAAO,GAAUga,GACxC,EAED,EAAQ5N,+BAA+BtK,EAAM9B,EAAM,CAE5D,CAg1BM8d,CAAsB3hB,KAAMshB,EAAUzD,GACtC,OAI2B,OAA7B7d,KAAK2Q,oBACH,KACA5H,EAAaU,EAA0B,OAE5B,OAAX8X,GAA8B,OAAXC,GAEtB,EAAQI,oCACP5hB,KAAKwQ,QACLgR,EACAxhB,KAAKuQ,QAEN,EAAgBgR,IAED,OAAXC,GAEH,EAAQI,oCACP5hB,KAAKwQ,QACLxQ,KAAK2Q,oBACL3Q,KAAKuQ,QAEN,EAAgBgR,IAEhB,EAAQK,oCACP5hB,KAAKwQ,QACLgR,EACAxhB,KAAKuQ,QAIRvQ,KAAK0Q,mBAAqB8Q,CAC3B,CAGQ,iBAAAxM,GACP,GAAIhV,KAAKuQ,SAAWlP,EACnB,MAAM,IAAIyE,MAAM,iCAElB,CAGQ,uBAAA4Z,GACP1f,KAAKgV,oBACDhV,KAAKwQ,UAAYnP,GACpBrB,KAAKif,aAEP,CAGQ,SAAAxH,CACPF,EACAC,EACAP,GAEA,EAAQ4K,yBACP7hB,KAAKuQ,OACL0G,EACAM,EACA,EACA,EACAC,EACA,EACA,EAEF,CAGQ,eAAAlB,GACP,MACMxO,EAAO,OACPuR,EAAM9P,EAAOzB,QACbyP,EAAU8B,EACV7B,EAAa6B,EAAiBvR,EACpC,KAAO9H,KAAKgU,aACXhU,KAAKyX,UAAUF,EAASC,EANN,OAQnBhO,EAAK6P,EACN,CAGQ,gBAAA1D,GACP,IAAK3V,KAAKgU,YACT,OAAOvJ,QAAQC,UAEhB,MACM5C,EAAO,OACPuR,EAAM9P,EAAOzB,QACbyP,EAAU8B,EACV7B,EAAa6B,EAAiBvR,EAC9Bga,EACiB,oBAAfC,WACJ,IACO,IAAItX,SAAeC,GACzBqX,WAAWrX,EAAS,KAGrB,IACOD,QAAQC,UAEnB,SAASsX,IACR,OAAOF,IAAYrL,KAAKwL,EACzB,CACA,MAAMC,EAAOliB,KACb,SAASiiB,IACR,OAAKC,EAAKlO,aAIVkO,EAAKzK,UAAUF,EAASC,EAxBN,OAyBXwK,MAJNxY,EAAK6P,GACE5O,QAAQC,UAIjB,CACA,OAAOsX,GACR,CAEO,oBAAAG,GACN,OAAOniB,KAAK+f,aACT/f,KAAK+f,aAAaC,QAClBvV,QAAQC,SACZ,CAKO,sBAAO0X,GACb,IACA,MAAMC,EAAM,IAAItY,EAChB,OAAOsY,EAAI/X,cAAcmM,MAAK,IAAM4L,GACrC,CAWO,8BAAOC,CACbD,EACA9V,EACA+U,EACAzD,GAEA,KAAMwE,aAAetY,GACpB,MAAM,IAAIwB,UAAU,8BAErB,MAAMvD,EAAMe,GACX,CAACwZ,EAAc3e,EAAiBuG,EAAcxE,KAC7C,MAAM6c,EAAI,IAAIlhB,EAAmBsC,EAAI,GAC/BE,EACL,EAAQjC,sBAAsB+B,GAC/B0d,EAASiB,EAAMze,EAAM0e,EAAGH,EAAK1c,EAAK,GAEnC,SAEKwG,EAAI7C,EACT+Y,EAAI3gB,SACJ6K,EACAvE,EACA6V,GAKD,OAHW,IAAP1R,IACHkW,EAAIhY,eAAe8B,GAAKnE,GAElBmE,CACR,CAQO,2BAAOsB,CACb4U,EACA3W,EACA7H,GAEA,KAAMwe,aAAetY,GACpB,MAAM,IAAIwB,UAAU,8BAErB8W,EAAI5U,qBAAqB/B,EAAU7H,EACpC,CAOO,mBAAO6J,CACb2U,EACA3W,EACAiC,GAEA,KAAM0U,aAAetY,GACpB,MAAM,IAAIwB,UAAU,8BAErB8W,EAAI3U,aAAahC,EAAUiC,EAC5B,CAQO,8BAAOO,CAAwBmU,EAAiBlU,GACtD,KAAMkU,aAAetY,GACpB,MAAM,IAAIwB,UAAU,8BAErB,OAAO8W,EAAInU,wBAAwBC,EACpC,EE/yCc,SAASsU,IACvB,OAAOpX,EAAY2F,wBACpB,CCqCO,SAAS0R,EACfC,EACAC,GAEA,MAAMC,EAAgC,CACrCF,KAAMA,EACNG,OAAQ,CAAC,EACTC,QAAS,GAIV,OAFAJ,EAAK7Q,iBAAiB,WAAY0Q,GAsCnC,SAA8BM,EAAkBE,EAA6CR,GAC5F,MAAM7c,EAA8B6c,EAAE7c,KACtC,IAAKA,EACJ,OAED,GAAIqd,GAAQA,EAAKrd,GAChB,OAED,MAAMsd,EAAQH,EAAOnd,EAAK0G,IAC1B,GAAI4W,SACIH,EAAOnd,EAAK0G,IACf1G,EAAKud,MACRD,EAAM3X,OAAO6X,EAAiBxd,EAAKud,QAEnCD,EAAMvY,QAAQ/E,EAAKwF,UAGpB,GAAIxF,EAAKud,MACR,MAAMC,EAAiBxd,EAAKud,MAG/B,CA3DyCE,CAAqBP,EAASC,OAAQF,EAAaJ,KAC3FG,EAAKU,QACER,CACR,CA+BA,SAASM,EAAiBD,GACzB,OAAO,IAAIrd,EAAaqd,EAAMnd,SAAUmd,EAAMld,QAASkd,EAAMjd,OAC9D,CA6BO,SAASqd,GAAS,KAAEX,GAA6BY,EAAgBC,GACvEb,EAAKc,YAAY,CAChBpX,IAAK,EAAGkX,SAAQC,QAElB,CAGO,SAASE,EAAuBb,EAA+BU,EAAgBC,GACrF,MAAMnX,EAAKwW,EAASE,WAChBF,EAASE,UAAYY,KAAYd,EAASE,QAAU,KACvDF,EAASE,QAAU,GAEpB,MAAM/C,EAAU,IAAIvV,SAAW,CAACC,EAASY,KACxCuX,EAASC,OAAOzW,GAAM,CAAE3B,UAASY,SAAQ,IAEpCsY,EAA4B,GAOlC,OANIJ,EAAK,aAAcK,aACtBD,EAAUpX,KAAKgX,EAAK,IAErBX,EAASF,KAAKc,YAAY,CACzBpX,KAAIkX,SAAQC,QACaI,GACnB5D,CACR,CCzIe,MAAM8D,EAKpB,YAAmBnB,EAAoCpW,GAAA,KAAAA,KAAAA,EACtDvM,KAAK+jB,WAAa,EAAmCpB,EACtD,CAEO,OAAAjU,GACN,OAAO1O,KAAKuM,IACb,CAEO,SAAAoC,CAAUnK,EAAcoK,GAC9B,OAAO,EAAoC5O,KAAK+jB,WAAY,YAAa,CAACvf,EAAMoK,GACjF,CAEO,iBAAAQ,GACN,OAAO,EAA8CpP,KAAK+jB,WAAY,oBAAqB,GAC5F,ECbc,MAAMC,EAIpB,WAAAziB,CAAYohB,GACX3iB,KAAK+jB,WAAa,EAAmCpB,EACtD,CAGO,MAAAjhB,GACN,OAAO,EAA4C1B,KAAK+jB,WAAa,SAAU,GAChF,CAEO,6BAAAE,CAA8BC,EAAoBC,EAAsBtG,GAC9E,OAAO7d,KAAK0B,SAAS+U,MAAM2N,GAAW,EACrCpkB,KAAK+jB,WACL,gCACA,CAACK,EAAQF,EAAYC,EAActG,KAErC,CAEO,KAAAtT,GACN,EAAyBvK,KAAK+jB,WAAa,QAAS,GACrD,CACO,mBAAA9Y,CAAoBC,GAC1B,IAAIC,EACJ,OAAID,aAAiBmZ,IACpBlZ,EAAMD,EAAMoZ,qBAINnZ,EAAIsL,MAAM8N,GAAM,EAA4CvkB,KAAK+jB,WAAa,sBAAuB,CAACQ,OAFrG9Z,QAAQa,OAAO,IAAIC,UAAU,6CAGtC,CACO,gBAAAT,CAAiBY,GACvB,EAAyB1L,KAAK+jB,WAAa,mBAAoB,CAACrY,GACjE,CACO,uBAAAM,GACN,OAAO,EAAkDhM,KAAK+jB,WAAa,0BAA2B,GACvG,CACO,cAAAtX,GACN,OAAO,EAA4CzM,KAAK+jB,WAAa,iBAAkB,GACxF,CACO,aAAArX,CAAcC,GACpB,OAAO,EAAgD3M,KAAK+jB,WAAa,gBAAiB,CAACpX,GAC5F,CACO,YAAAC,CAAaC,GACnB,EAAyB7M,KAAK+jB,WAAa,eAAgB,CAAClX,GAC7D,CACO,YAAAE,GACN,OAAO,EAA4C/M,KAAK+jB,WAAa,eAAgB,GACtF,CACO,OAAA9W,GACN,OAAO,EAA4CjN,KAAK+jB,WAAa,UAAW,GACjF,CACO,WAAA5W,CAAYtJ,EAAuBuJ,EAAcC,GACvD,EAAyBrN,KAAK+jB,WAAa,cAAe,CAAClgB,EAAOuJ,EAAMC,GACzE,CACO,mBAAAG,CAAoB9B,EAAkB7H,EAAuBuJ,EAAcC,GACjF,EAAyBrN,KAAK+jB,WAAa,sBAAuB,CAACrY,EAAU7H,EAAOuJ,EAAMC,GAC3F,CACO,eAAAO,GACN,EAAyB5N,KAAK+jB,WAAa,kBAAmB,GAC/D,CACO,yBAAAjW,CAA0BpC,GAChC,EAAyB1L,KAAK+jB,WAAa,4BAA6B,CAACrY,GAC1E,CAEO,gBAAAqC,CAAiBC,GACvB,EAAyBhO,KAAK+jB,WAAa,mBAAoB,CAAC/V,GACjE,EJ7ED,IAAIwW,EAAyC,KAC7C,MAAMC,EAAqD,GAErDC,EAAkB,EAElBC,EAAW,CAChBC,MAAO,EACP9e,MAAO,EACP+e,QAAS,EACTC,KAAM,EACNC,MAAO,GAuBD,SAASC,EAAenK,EAAyB8J,EAASC,OAChE,GAAIJ,IAA0B3J,EAA9B,CAIA,GAtBD,WACC,GAAuC,oBAA5BrX,wBACV,EAAUA,wBAAwBC,eAC5B,IAAsB,oBAAXC,OAGjB,MAAM,IAAIoC,MACT,oEAHD,EAAUpC,M,CAMZ,CAWC,GACa,MAATmX,EAC2B,MAA1BlR,IACH,EAAQsb,wBAAwB,EAAGtb,EAAwB,GAC3D,EAAQsb,wBAAwB,EAAGtb,EAAwB,GAC3D,EAAQsb,wBAAwB,EAAGtb,EAAwB,GAC3D,EAAQsb,wBAAwB,EAAGtb,EAAwB,IAE5D,EAAQsb,wBAAwB,EAAG,EAAG,OAChC,CACN,IAAIjd,EACJ,IAAK,IAAIkd,EAAIrK,EAAOqK,EAAIR,IAAmBQ,EAAG,CAC7C,MAAM5F,EAAI,EAAQ2F,wBAAwBC,EAAG,EAAG,GAC5CA,IAAMP,EAASI,QAClB/c,EAAMsX,E,CAGG,MAAPtX,GAAyC,MAA1B2B,IAClBA,EAAyB3B,E,CAG3Bwc,EAAwB3J,EACxB,IAAK,MAAMxJ,KAAMoT,EAChBpT,EAAGwJ,E,CAEL,CAKO,SAASsK,KACfH,EAAe,KAChB,CK5Ce,MAAMX,GAcpB,WAAA9iB,GLsCM,IAAwC8P,EKrC7CrR,KAAKolB,QAAU,CACdC,SAAS,EACTC,eAAe,GAEhBtlB,KAAK+jB,WAAa,KAClB/jB,KAAKulB,MAAQ,KACbvlB,KAAK+Q,MAAQ,GACb/Q,KAAKwlB,qBAAuBxlB,KAAKylB,sBAAsB1V,KAAK/P,ML8BfqR,EK7BdrR,KAAKwlB,qBL8BrCf,EAAUjY,KAAK6E,EK7Bf,CAGA,QAAWO,GACV,OAAO5R,KAAKulB,KACb,CAKO,eAAA9T,CAAgBC,EAAuBvB,GAC7C,MAIMyB,EAAO,IAAI8T,iBAAiBhU,EAAS,WAAyB,CACnEiU,eAAgB,EAChBC,gBAAiB,EACjBC,aAAc,EACdC,mBAAoB,CAAC,GACrBC,iBAT0C,CAC1C5V,SAAUA,EACV6V,qBLWKxB,KKON,OATAxkB,KAAKulB,MAAQ3T,EAEb5R,KAAK+jB,WAAa,EAAmCnS,EAAK+Q,MAAOhd,GAC5C,iBAAhBA,EAAK4d,SACRvjB,KAAKolB,QAAUzf,EAAKwF,KACb,KAIFyG,CACR,CAEO,aAAAJ,GACN,OAA2B,OAApBxR,KAAK+jB,UACb,CAEO,IAAA9R,CAAKgU,EAAqB3V,GACjC,CAEO,KAAA/F,GAEN,EAAyBvK,KAAK+jB,WAAa,OAAQ,CAAC,GACrD,CAEO,SAAA/P,GACN,OAAOhU,KAAKolB,QAAQC,OACrB,CAEO,gBAAAtQ,CAAiB/T,EAA4BgD,GACnD,EAAyBhE,KAAK+jB,WAAa,mBAAoB,CAAC/iB,EAAOgD,GACxE,CAEO,OAAAkR,GACN,OAAOlV,KAAK+Q,KACb,CAEO,OAAAoE,CAAQC,GACdpV,KAAK+Q,MAAQqE,EACb,EAA0CpV,KAAK+jB,WAAa,UAAW,CAAC3O,IAAOqB,MAAK,IAC5E,EAA4CzW,KAAK+jB,WAAa,UAAW,MAC9EtN,MAAMzV,IACRhB,KAAK+Q,MAAQ/P,CAAK,GAEpB,CAEO,cAAAuU,CAAevR,EAAiBwR,GACtC,EAAyBxV,KAAK+jB,WAAa,iBAAkB,CAAC/f,EAASwR,GACxE,CAEO,oBAAAE,GACN,OAAO,EAA0C1V,KAAK+jB,WAAa,uBAAwB,GAC5F,CAEO,SAAAnO,CAAUC,GAChB,OAAO,EAA4C7V,KAAK+jB,WAAa,YAAa,CAAClO,GACpF,CAEO,WAAAO,CAAY/J,GAClB,EAAyBrM,KAAK+jB,WAAa,cAAe,CAAC1X,GAC5D,CAEO,gBAAAmK,CAAiBnK,GACvB,OAAO,EAA0CrM,KAAK+jB,WAAa,cAAe,CAAC1X,GACpF,CAOO,cAAAqK,CAAe9R,GACrB,MAAMZ,EAAU,IAAIkiB,eACpB,OAAO,EAA4ClmB,KAAK+jB,WAAa,iBAAkB,CAAC/f,EAAQmiB,MAAOvhB,IAAU6R,MAAMlK,GAC/G,IAAIuX,EAAiB9f,EAAQoiB,MAAO7Z,IAE7C,CAEO,kBAAAoK,CAAmBtK,GACzB,OAAO,EAA4CrM,KAAK+jB,WAAa,qBAAsB,CAAC1X,GAC7F,CACO,kBAAAwK,CAAmBxK,EAAYyK,GACrC,EAAyB9W,KAAK+jB,WAAa,qBAAsB,CAAC1X,EAAIyK,GACvE,CAEO,MAAA/E,GACN,MAAM,IAAIjM,MAAM,kBACjB,CAEO,UAAAoS,CAAWC,EAAchY,EAAa8D,GAC5C,EAAyBjE,KAAK+jB,WAAa,aAAc,CAAC5L,EAAMhY,EAAK8D,GACtE,CACO,WAAAoU,CAAYF,EAAchY,GAChC,EAAyBH,KAAK+jB,WAAa,cAAe,CAAC5L,EAAMhY,GAClE,CACO,eAAAoY,CAAgBJ,EAAchY,EAAagL,GACjD,EAAyBnL,KAAK+jB,WAAa,kBAAmB,CAAC5L,EAAMhY,EAAKgL,GAC3E,CACO,WAAAsN,CAAYN,EAAcO,EAAcvN,GAC9C,EAAyBnL,KAAK+jB,WAAa,cAAe,CAAC5L,EAAMO,EAAMvN,GACxE,CACO,iBAAAyN,CAAkBT,EAAcU,GACtC,EAAyB7Y,KAAK+jB,WAAa,oBAAqB,CAAC5L,EAAMU,GACxE,CACO,mBAAAE,CAAoBZ,EAAchN,GACxC,EAAyBnL,KAAK+jB,WAAa,sBAAuB,CAAC5L,EAAMhN,GAC1E,CACO,aAAA8N,CAAcd,EAAchN,GAClC,EAAyBnL,KAAK+jB,WAAa,gBAAiB,CAAC5L,EAAMhN,GACpE,CACO,SAAAgO,CAAUxT,GAChB,EAAyB3F,KAAK+jB,WAAa,YAAa,CAACpe,GAC1D,CAEO,yBAAA4T,CAA0BpB,EAAchN,GAC9C,EAAyBnL,KAAK+jB,WAAa,4BAA6B,CAAC5L,EAAMhN,GAChF,CACO,cAAAyH,CAAeuF,EAAc3T,GACnC,EAAyBxE,KAAK+jB,WAAa,iBAAkB,CAAC5L,EAAM3T,GACrE,CACO,eAAAkV,CAAgBvB,EAAcvT,GACpC,EAAyB5E,KAAK+jB,WAAa,kBAAmB,CAAC5L,EAAMvT,GACtE,CACO,iBAAAgV,CAAkBzB,EAAcvT,EAAiBJ,EAAcoK,GACrE,EAAyB5O,KAAK+jB,WAAa,oBAAqB,CAAC5L,EAAMvT,EAASJ,EAAMoK,GACvF,CACO,gBAAAkL,CAAiB3B,GACvB,EAAyBnY,KAAK+jB,WAAa,mBAAoB,CAAC5L,GACjE,CACO,gBAAA6B,GACN,EAAyBha,KAAK+jB,WAAa,mBAAoB,GAChE,CACO,eAAA7J,GACN,EAAyBla,KAAK+jB,WAAa,kBAAmB,GAC/D,CACO,eAAA3J,CAAgBjC,GACtB,EAAyBnY,KAAK+jB,WAAa,kBAAmB,CAAC5L,GAChE,CACO,gBAAAmC,CAAiBnC,GACvB,EAAyBnY,KAAK+jB,WAAa,mBAAoB,CAAC5L,GACjE,CACO,kBAAAqC,CAAmBrC,EAAc3C,GACvC,EAAyBxV,KAAK+jB,WAAa,qBAAsB,CAAC5L,EAAM3C,GACzE,CAEO,WAAAwJ,GACN,OAAO,EAA0Chf,KAAK+jB,WAAa,cAAe,GACnF,CAEO,WAAA7E,GACN,EAAyBlf,KAAK+jB,WAAa,cAAe,GAC3D,CAEO,eAAA9L,GACN,OAAOjY,KAAKolB,QAAQE,aACrB,CAEO,kBAAA7F,CAAmB5J,GACzB,OAAO,EAA0C7V,KAAK+jB,WAAa,qBAAsB,CAAClO,GAC3F,CAEO,UAAA+J,GACN,OAAO,EAA0C5f,KAAK+jB,WAAa,aAAc,GAClF,CAEO,UAAA1N,GACN,EAAyBrW,KAAK+jB,WAAa,aAAc,GAC1D,CAEO,yBAAA5D,GACN,OAAO,EAA4CngB,KAAK+jB,WAAa,4BAA6B,GACnG,CACO,wBAAA1D,GACN,OAAO,EAA4CrgB,KAAK+jB,WAAa,2BAA4B,GAClG,CACO,iBAAAxD,GACN,OAAO,EAA4CvgB,KAAK+jB,WAAa,oBAAqB,GAC3F,CACO,uBAAAtD,GACN,OAAO,EAA4CzgB,KAAK+jB,WAAa,0BAA2B,GACjG,CACO,UAAApD,CAAWC,GACjB,EAAyB5gB,KAAK+jB,WAAa,aAAc,CAACnD,GAC3D,CACO,aAAAE,CAAcC,GACpB,EAAyB/gB,KAAK+jB,WAAa,gBAAiB,CAAChD,GAC9D,CACO,cAAAE,CAAeC,EAA+BC,GACpD,EAAyBnhB,KAAK+jB,WAAa,iBAAkB,CAAC7C,EAAWC,GAC1E,CAEO,oBAAAgB,GACN,OAAO,EAA0CniB,KAAK+jB,WAAa,uBAAwB,GAC5F,CAKO,eAAA3B,GACN,MAAMpe,EAAU,IAAIkiB,eACpB,OAAO,EAA0ClmB,KAAK+jB,WAAa,kBAAmB,CAAC/f,EAAQmiB,QAAQ1P,MAAK,IACpG,IAAIuN,EAAiBhgB,EAAQoiB,QAEtC,CAYO,0BAAAC,CAA2BlC,EAAyCtG,GAC1E,OAAO,EAA0C7d,KAAK+jB,WAAa,6BAA8B,CAACI,EAActG,GACjH,CAeO,6BAAAoG,CAA8B5B,EAAiB6B,EAAoBC,EAAsBtG,GAC/F,OAAMwE,aAAe2B,EAGd3B,EAAI4B,8BAA8BC,EAAYC,EAActG,GAF3DpT,QAAQa,OAAO,IAAIC,UAAU,4BAGtC,CAcO,YAAA+a,CAAa/Z,EAAcsR,GACjC,OAAO,EAA0C7d,KAAK+jB,WAAa,eAAgB,CAACxX,EAAMsR,GAC3F,CAGO,kBAAAyG,GACN,OAAO,EAA4CtkB,KAAK+jB,WAAa,oBAAqB,GAC3F,CAGQ,qBAAA0B,CAAsB5K,GACN,MAAnB7a,KAAK+jB,YAGT,EAAyB/jB,KAAK+jB,WAAY,iBAAkB,CAAClJ,GAC9D,E,UnBxUD", "sources": ["webpack://JSSynth/webpack/universalModuleDefinition", "webpack://JSSynth/webpack/bootstrap", "webpack://JSSynth/webpack/runtime/define property getters", "webpack://JSSynth/webpack/runtime/hasOwnProperty shorthand", "webpack://JSSynth/webpack/runtime/make namespace object", "webpack://JSSynth/./src/main/Constants.ts", "webpack://JSSynth/./src/main/PointerType.ts", "webpack://JSSynth/./src/main/SequencerEventData.ts", "webpack://JSSynth/./src/main/ISequencerEventData.ts", "webpack://JSSynth/./src/main/MessageError.ts", "webpack://JSSynth/./src/main/MIDIEvent.ts", "webpack://JSSynth/./src/main/Sequencer.ts", "webpack://JSSynth/./src/main/Soundfont.ts", "webpack://JSSynth/./src/main/Synthesizer.ts", "webpack://JSSynth/./src/main/logging.ts", "webpack://JSSynth/./src/main/waitForReady.ts", "webpack://JSSynth/./src/main/MethodMessaging.ts", "webpack://JSSynth/./src/main/WorkletSoundfont.ts", "webpack://JSSynth/./src/main/WorkletSequencer.ts", "webpack://JSSynth/./src/main/AudioWorkletNodeSynthesizer.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"js-synthesizer\"] = factory();\n\telse\n\t\troot[\"JSSynth\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "\n/** Default values for synthesizer instances */\nexport const enum SynthesizerDefaultValues {\n\tGain = 0.5\n}\n\n/** Interpolation values used by ISynthesizer.setInterpolation */\nexport const enum InterpolationValues {\n\t/** No interpolation: Fastest, but questionable audio quality */\n\tNone = 0,\n\t/** Straight-line interpolation: A bit slower, reasonable audio quality */\n\tLinear = 1,\n\t/** Fourth-order interpolation, good quality, the default */\n\tFourthOrder = 4,\n\t/** Seventh-order interpolation */\n\tSeventhOrder = 7,\n\t/** Default interpolation method */\n\tDefault = FourthOrder,\n\t/** Highest interpolation method */\n\tHighest = SeventhOrder,\n}\n\n/** Chorus modulation waveform type, used by Synthesizer.setChorus etc. */\nexport const enum ChorusModulation {\n\t/** Sine wave chorus modulation */\n\tSine = 0,\n\t/** Triangle wave chorus modulation */\n\tTriangle = 1\n}\n\n/** Generator type ID (specified in SoundFont format) */\nexport const enum GeneratorTypes {\n\tStartAddrsOffset = 0,\n\tEndAddrsOffset = 1,\n\tStartLoopAddrsOffset = 2,\n\tEndLoopAddrsOffset = 3,\n\tStartAddrsCoarseOffset = 4,\n\tModLfoToPitch = 5,\n\tVibLfoToPitch = 6,\n\tModEnvToPitch = 7,\n\tInitialFilterFc = 8,\n\tInitialFilterQ = 9,\n\tModLfoToFilterFc = 10,\n\tModEnvToFilterFc = 11,\n\tEndAddrsCoarseOffset = 12,\n\tModLfoToVolume = 13,\n\t// 14: unused\n\tChorusEffectsSend = 15,\n\tReverbEffectsSend = 16,\n\tPan = 17,\n\t// 18-20: unused\n\tDelayModLFO = 21,\n\tFreqModLFO = 22,\n\tDelayVibLFO = 23,\n\tFreqVibLFO = 24,\n\tDelayModEnv = 25,\n\tAttackModEnv = 26,\n\tHoldModEnv = 27,\n\tDecayModEnv = 28,\n\tSustainModEnv = 29,\n\tReleaseModEnv = 30,\n\tKeynumToModEnvHold = 31,\n\tKeynumToModEnvDecay = 32,\n\tDelayVolEnv = 33,\n\tAttackVolEnv = 34,\n\tHoldVolEnv = 35,\n\tDecayVolEnv = 36,\n\tSustainVolEnv = 37,\n\tReleaseVolEnv = 38,\n\tKeynumToVolEnvHold = 39,\n\tKeynumToVolEnvDecay = 40,\n\tInstrument = 41,\n\t// 42: reserved\n\tKeyRange = 43,\n\tVelRange = 44,\n\tStartloopAddrsCoarseOffset = 45,\n\tKeynum = 46,\n\tVelocity = 47,\n\tInitialAttenuation = 48,\n\t// 49: reserved2\n\tEndloopAddrsCoarseOffset = 50,\n\tCoarseTune = 51,\n\tFineTune = 52,\n\tSampleID = 53,\n\tSampleModes = 54,\n\t// 55: reserved3\n\tScaleTuning = 56,\n\tExclusiveClass = 57,\n\tOverridingRootKey = 58,\n\n\t// Not in SoundFont specification\n\t_Pitch = 59,\n\t_CustomBalance = 60,\n\t_CustomFilterFc = 61,\n\t_CustomFilterQ = 62\n}\n\n/** Mono legato mode */\nexport const enum LegatoMode {\n\tRetrigger = 0,\n\tMultiRetrigger = 1\n}\n\n/** Portamento mode */\nexport const enum PortamentoMode {\n\tEachNote = 0,\n\tLegatoOnly = 1,\n\tStaccatoOnly = 2\n}\n\n/** Breath mode flags */\nexport const enum BreathFlags {\n\tPoly = 0x10,\n\tMono = 0x20,\n\tSync = 0x40\n}\n\n/** Tempo type for `Synthesizer.setPlayerTempo` */\nconst PlayerSetTempoType = {\n\tInternal: 0,\n\tExternalBpm: 1,\n\tExternalMidi: 2,\n} as const;\n/** Tempo type for `Synthesizer.setPlayerTempo` */\ntype PlayerSetTempoType = (typeof PlayerSetTempoType)[keyof typeof PlayerSetTempoType];\nexport { PlayerSetTempoType };\n", "\ntype NullPointerType = number & { _null_pointer_marker: never; };\n\n/** @internal */\ntype PointerType = NullPointerType | (number & { _pointer_marker: never; });\n\nexport default PointerType;\n\ntype UniquePointerType<TMarker extends string> = NullPointerType | (number & {\n\t_pointer_marker: never;\n} & {\n\t[P in TMarker]: never;\n});\nexport { UniquePointerType };\n\nexport const INVALID_POINTER: NullPointerType = 0 as any as NullPointerType;\n", "\nimport { EventType } from './SequencerEvent';\nimport ISequencerEventData from './ISequencerEventData';\nimport PointerType, { INVALID_POINTER } from './PointerType';\n\n/** @internal */\nexport default class SequencerEventData implements ISequencerEventData {\n\t/** @internal */\n\tconstructor(private _ptr: PointerType, private _module: any) {\n\t}\n\n\t/** @internal */\n\tpublic getRaw() {\n\t\treturn this._ptr;\n\t}\n\n\t/** @internal */\n\tpublic dispose() {\n\t\tthis._ptr = INVALID_POINTER;\n\t}\n\n\tpublic getType(): EventType {\n\t\tif (this._ptr === INVALID_POINTER) return -1 as any as EventType;\n\t\treturn this._module._fluid_event_get_type(this._ptr);\n\t}\n\tpublic getSource(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_source(this._ptr);\n\t}\n\tpublic getDest(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_dest(this._ptr);\n\t}\n\tpublic getChannel(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_channel(this._ptr);\n\t}\n\tpublic getKey(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_key(this._ptr);\n\t}\n\tpublic getVelocity(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_velocity(this._ptr);\n\t}\n\tpublic getControl(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_control(this._ptr);\n\t}\n\tpublic getValue(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_value(this._ptr);\n\t}\n\tpublic getProgram(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_program(this._ptr);\n\t}\n\tpublic getData(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_data(this._ptr);\n\t}\n\tpublic getDuration(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_duration(this._ptr);\n\t}\n\tpublic getBank(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_bank(this._ptr);\n\t}\n\tpublic getPitch(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_pitch(this._ptr);\n\t}\n\tpublic getSFontId(): number {\n\t\tif (this._ptr === INVALID_POINTER) return -1;\n\t\treturn this._module._fluid_event_get_sfont_id(this._ptr);\n\t}\n}\n", "\nimport SequencerEvent, { EventType } from './SequencerEvent';\n\n/** @internal */\nimport PointerType, { INVALID_POINTER } from './PointerType';\n/** @internal */\nimport SequencerEventData from './SequencerEventData';\n\nconst _module: any = typeof AudioWorkletGlobalScope !== 'undefined' ?\n\tAudioWorkletGlobalScope.wasmModule : Module;\n\n/** Event data for sequencer callback. Only available in the callback function due to the instance lifetime. */\nexport default interface ISequencerEventData {\n\t/** Returns the event type */\n\tgetType(): EventType;\n\t/** Returns the source client id of event */\n\tgetSource(): number;\n\t/** Returns the destination client id of event */\n\tgetDest(): number;\n\tgetChannel(): number;\n\tgetKey(): number;\n\tgetVelocity(): number;\n\tgetControl(): number;\n\tgetValue(): number;\n\tgetProgram(): number;\n\tgetData(): number;\n\tgetDuration(): number;\n\tgetBank(): number;\n\tgetPitch(): number;\n\tgetSFontId(): number;\n}\n\n/** @internal */\nexport function rewriteEventDataImpl(ev: PointerType, event: SequencerEvent): boolean {\n\tswitch (event.type) {\n\t\tcase EventType.Note:\n\t\tcase 'note':\n\t\t\t_module._fluid_event_note(ev, event.channel, event.key, event.vel, event.duration);\n\t\t\tbreak;\n\t\tcase EventType.NoteOn:\n\t\tcase 'noteon':\n\t\tcase 'note-on':\n\t\t\t_module._fluid_event_noteon(ev, event.channel, event.key, event.vel);\n\t\t\tbreak;\n\t\tcase EventType.NoteOff:\n\t\tcase 'noteoff':\n\t\tcase 'note-off':\n\t\t\t_module._fluid_event_noteoff(ev, event.channel, event.key);\n\t\t\tbreak;\n\t\tcase EventType.AllSoundsOff:\n\t\tcase 'allsoundsoff':\n\t\tcase 'all-sounds-off':\n\t\t\t_module._fluid_event_all_sounds_off(ev, event.channel);\n\t\t\tbreak;\n\t\tcase EventType.AllNotesOff:\n\t\tcase 'allnotesoff':\n\t\tcase 'all-notes-off':\n\t\t\t_module._fluid_event_all_notes_off(ev, event.channel);\n\t\t\tbreak;\n\t\tcase EventType.BankSelect:\n\t\tcase 'bankselect':\n\t\tcase 'bank-select':\n\t\t\t_module._fluid_event_bank_select(ev, event.channel, event.bank);\n\t\t\tbreak;\n\t\tcase EventType.ProgramChange:\n\t\tcase 'programchange':\n\t\tcase 'program-change':\n\t\t\t_module._fluid_event_program_change(ev, event.channel, event.preset);\n\t\t\tbreak;\n\t\tcase EventType.ProgramSelect:\n\t\tcase 'programselect':\n\t\tcase 'program-select':\n\t\t\t_module._fluid_event_program_select(ev, event.channel, event.sfontId, event.bank, event.preset);\n\t\t\tbreak;\n\t\tcase EventType.ControlChange:\n\t\tcase 'controlchange':\n\t\tcase 'control-change':\n\t\t\t_module._fluid_event_control_change(ev, event.channel, event.control, event.value);\n\t\t\tbreak;\n\t\tcase EventType.PitchBend:\n\t\tcase 'pitchbend':\n\t\tcase 'pitch-bend':\n\t\t\t_module._fluid_event_pitch_bend(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.PitchWheelSensitivity:\n\t\tcase 'pitchwheelsens':\n\t\tcase 'pitchwheelsensitivity':\n\t\tcase 'pitch-wheel-sens':\n\t\tcase 'pitch-wheel-sensitivity':\n\t\t\t_module._fluid_event_pitch_wheelsens(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Modulation:\n\t\tcase 'modulation':\n\t\t\t_module._fluid_event_modulation(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Sustain:\n\t\tcase 'sustain':\n\t\t\t_module._fluid_event_sustain(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Pan:\n\t\tcase 'pan':\n\t\t\t_module._fluid_event_pan(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.Volume:\n\t\tcase 'volume':\n\t\t\t_module._fluid_event_volume(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ReverbSend:\n\t\tcase 'reverb':\n\t\tcase 'reverbsend':\n\t\tcase 'reverb-send':\n\t\t\t_module._fluid_event_reverb_send(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ChorusSend:\n\t\tcase 'chorus':\n\t\tcase 'chorussend':\n\t\tcase 'chorus-send':\n\t\t\t_module._fluid_event_chorus_send(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.KeyPressure:\n\t\tcase 'keypressure':\n\t\tcase 'key-pressure':\n\t\tcase 'aftertouch':\n\t\t\t_module._fluid_event_key_pressure(ev, event.channel, event.key, event.value);\n\t\t\tbreak;\n\t\tcase EventType.ChannelPressure:\n\t\tcase 'channelpressure':\n\t\tcase 'channel-pressure':\n\t\tcase 'channel-aftertouch':\n\t\t\t_module._fluid_event_channel_pressure(ev, event.channel, event.value);\n\t\t\tbreak;\n\t\tcase EventType.SystemReset:\n\t\tcase 'systemreset':\n\t\tcase 'system-reset':\n\t\t\t_module._fluid_event_system_reset(ev);\n\t\t\tbreak;\n\t\tcase EventType.Timer:\n\t\tcase 'timer':\n\t\t\t_module._fluid_event_timer(ev, event.data);\n\t\t\tbreak;\n\t\tdefault:\n\t\t\t// 'typeof event' must be 'never' here\n\t\t\treturn false;\n\t}\n\treturn true;\n}\n\n/**\n * Rewrites event data with specified SequencerEvent object.\n * @param data destination instance\n * @param event source data\n * @return true if succeeded\n */\nexport function rewriteEventData(data: ISequencerEventData, event: SequencerEvent): boolean {\n\tif (!data || !(data instanceof SequencerEventData)) {\n\t\treturn false;\n\t}\n\tconst ev = data.getRaw();\n\tif (ev === INVALID_POINTER) {\n\t\treturn false;\n\t}\n\treturn rewriteEventDataImpl(ev, event);\n}\n", "\n/** Error object used for errors occurred in the message receiver (e.g. Worklet) */\nexport default class MessageError extends Error {\n\t/** The name of original error object if available */\n\tpublic baseName: any;\n\t/** Detailed properties of original error object if available */\n\tpublic detail: any;\n\n\tconstructor(baseName: string, message: string, detail?: any) {\n\t\tsuper(message);\n\t\tthis.baseName = baseName;\n\t\tthis.detail = detail;\n\t\tif (detail && detail.stack) {\n\t\t\tthis.stack = detail.stack;\n\t\t}\n\t}\n}\n", "\nimport IMIDIEvent from './IMIDIEvent';\nimport PointerType, { UniquePointerType } from './PointerType';\n\n/** @internal */\nexport type MIDIEventType = UniquePointerType<'midi_event'>;\n\n/** @internal */\nexport default class MIDIEvent implements IMIDIEvent {\n\n\t/** @internal */\n\tconstructor(private _ptr: MIDIEventType, private _module: any) {\n\t}\n\n\tpublic getType(): number {\n\t\treturn this._module._fluid_midi_event_get_type(this._ptr);\n\t}\n\tpublic setType(value: number): void {\n\t\tthis._module._fluid_midi_event_set_type(this._ptr, value);\n\t}\n\tpublic getChannel(): number {\n\t\treturn this._module._fluid_midi_event_get_channel(this._ptr);\n\t}\n\tpublic setChannel(value: number): void {\n\t\tthis._module._fluid_midi_event_set_channel(this._ptr, value);\n\t}\n\tpublic getKey(): number {\n\t\treturn this._module._fluid_midi_event_get_key(this._ptr);\n\t}\n\tpublic setKey(value: number): void {\n\t\tthis._module._fluid_midi_event_set_key(this._ptr, value);\n\t}\n\tpublic getVelocity(): number {\n\t\treturn this._module._fluid_midi_event_get_velocity(this._ptr);\n\t}\n\tpublic setVelocity(value: number): void {\n\t\tthis._module._fluid_midi_event_set_velocity(this._ptr, value);\n\t}\n\tpublic getControl(): number {\n\t\treturn this._module._fluid_midi_event_get_control(this._ptr);\n\t}\n\tpublic setControl(value: number): void {\n\t\tthis._module._fluid_midi_event_set_control(this._ptr, value);\n\t}\n\tpublic getValue(): number {\n\t\treturn this._module._fluid_midi_event_get_value(this._ptr);\n\t}\n\tpublic setValue(value: number): void {\n\t\tthis._module._fluid_midi_event_set_value(this._ptr, value);\n\t}\n\tpublic getProgram(): number {\n\t\treturn this._module._fluid_midi_event_get_program(this._ptr);\n\t}\n\tpublic setProgram(value: number): void {\n\t\tthis._module._fluid_midi_event_set_program(this._ptr, value);\n\t}\n\tpublic getPitch(): number {\n\t\treturn this._module._fluid_midi_event_get_pitch(this._ptr);\n\t}\n\tpublic setPitch(value: number): void {\n\t\tthis._module._fluid_midi_event_set_pitch(this._ptr, value);\n\t}\n\n\tpublic setSysEx(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_sysex(this._ptr, ptr, size, 1);\n\t}\n\tpublic setText(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_text(this._ptr, ptr, size, 1);\n\t}\n\tpublic setLyrics(data: Uint8Array): void {\n\t\tconst size = data.byteLength;\n\t\tconst ptr: PointerType = this._module._malloc(size);\n\t\tconst ptrView = new Uint8Array(this._module.HEAPU8.buffer, ptr, size);\n\t\tptrView.set(data);\n\t\tthis._module._fluid_midi_event_set_lyrics(this._ptr, ptr, size, 1);\n\t}\n}\n", "\nimport ISequencer, { ClientInfo } from './ISequencer';\nimport ISequencerEventData, { rewriteEventDataImpl } from './ISequencerEventData';\nimport ISynthesizer from './ISynthesizer';\nimport PointerType, { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport SequencerEvent from './SequencerEvent';\nimport SequencerEventData from './SequencerEventData';\n\nimport Synthesizer from './Synthesizer';\n\ntype SequencerPointer = UniquePointerType<'sequencer_ptr'>;\ntype SequencerId = number;\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n\tfunction removeFunction(funcPtr: number): void;\n}\n\nlet _module: any;\nlet _removeFunction: (funcPtr: number) => void;\n\nlet fluid_sequencer_get_client_name: (seq: number, id: number) => string;\n\nfunction bindFunctions() {\n\tif (_module) {\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t\t_removeFunction = AudioWorkletGlobalScope.wasmRemoveFunction;\n\t} else {\n\t\t_module = Module;\n\t\t_removeFunction = removeFunction;\n\t}\n\n\tfluid_sequencer_get_client_name =\n\t\t_module.cwrap('fluid_sequencer_get_client_name', 'string', ['number', 'number']);\n}\n\nfunction makeEvent(event: SequencerEvent): PointerType | null {\n\tconst ev = _module._new_fluid_event();\n\tif (!rewriteEventDataImpl(ev, event)) {\n\t\t_module._delete_fluid_event(ev);\n\t\treturn null;\n\t}\n\treturn ev;\n}\n\n/** @internal */\nexport default class Sequencer implements ISequencer {\n\n\tprivate _seq: SequencerPointer;\n\tprivate _seqId: SequencerId;\n\n\t/** @internal */\n\tpublic _clientFuncMap: { [id: number]: number };\n\n\tconstructor() {\n\t\tbindFunctions();\n\n\t\tthis._seq = INVALID_POINTER;\n\t\tthis._seqId = -1;\n\t\tthis._clientFuncMap = {};\n\t}\n\n\t/** @internal */\n\tpublic _initialize(): Promise<void> {\n\t\tthis.close();\n\t\tthis._seq = _module._new_fluid_sequencer2(0);\n\t\tthis._seqId = -1;\n\t\treturn Promise.resolve();\n\t}\n\n\t/** @internal */\n\tpublic getRaw() {\n\t\treturn this._seq;\n\t}\n\n\tpublic close() {\n\t\tif (this._seq !== INVALID_POINTER) {\n\t\t\tObject.keys(this._clientFuncMap).forEach((clientIdStr) => {\n\t\t\t\tthis.unregisterClient(Number(clientIdStr));\n\t\t\t});\n\t\t\tthis.unregisterClient(-1);\n\t\t\t_module._delete_fluid_sequencer(this._seq);\n\t\t\tthis._seq = INVALID_POINTER;\n\t\t}\n\t}\n\n\tpublic registerSynthesizer(synth: ISynthesizer | number): Promise<number> {\n\t\tif (this._seqId !== -1) {\n\t\t\t_module._fluid_sequencer_unregister_client(this._seq, this._seqId);\n\t\t\tthis._seqId = -1;\n\t\t}\n\t\tlet val: number;\n\t\tif (typeof synth === 'number') {\n\t\t\tval = synth;\n\t\t} else if (synth instanceof Synthesizer) {\n\t\t\tval = synth.getRawSynthesizer();\n\t\t} else {\n\t\t\treturn Promise.reject(new TypeError('\\'synth\\' is not a compatible type instance'));\n\t\t}\n\n\t\tthis._seqId = _module._fluid_sequencer_register_fluidsynth(this._seq, val);\n\t\treturn Promise.resolve(this._seqId);\n\t}\n\n\tpublic unregisterClient(clientId: number): void {\n\t\tif (clientId === -1) {\n\t\t\tclientId = this._seqId;\n\t\t\tif (clientId === -1) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\t// send 'unregistering' event\n\t\tconst ev = _module._new_fluid_event();\n\t\t_module._fluid_event_set_source(ev, -1);\n\t\t_module._fluid_event_set_dest(ev, clientId);\n\t\t_module._fluid_event_unregistering(ev);\n\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t_module._delete_fluid_event(ev);\n\n\t\t_module._fluid_sequencer_unregister_client(this._seq, clientId);\n\t\tif (this._seqId === clientId) {\n\t\t\tthis._seqId = -1;\n\t\t} else {\n\t\t\tconst map = this._clientFuncMap;\n\t\t\tif (map[clientId]) {\n\t\t\t\t_removeFunction(map[clientId]);\n\t\t\t\tdelete map[clientId];\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic getAllRegisteredClients(): Promise<ClientInfo[]> {\n\t\tconst c = _module._fluid_sequencer_count_clients(this._seq);\n\t\tconst r: ClientInfo[] = [];\n\t\tfor (let i = 0; i < c; ++i) {\n\t\t\tconst id = _module._fluid_sequencer_get_client_id(this._seq, i);\n\t\t\tconst name = fluid_sequencer_get_client_name(this._seq, id);\n\t\t\tr.push({ clientId: id, name: name });\n\t\t}\n\t\treturn Promise.resolve(r);\n\t}\n\n\tpublic getClientCount(): Promise<number> {\n\t\treturn Promise.resolve<number>(_module._fluid_sequencer_count_clients(this._seq));\n\t}\n\n\tpublic getClientInfo(index: number): Promise<ClientInfo> {\n\t\tconst id = _module._fluid_sequencer_get_client_id(this._seq, index);\n\t\tconst name = fluid_sequencer_get_client_name(this._seq, id);\n\t\treturn Promise.resolve<ClientInfo>({ clientId: id, name: name });\n\t}\n\n\tpublic setTimeScale(scale: number): void {\n\t\t_module._fluid_sequencer_set_time_scale(this._seq, scale);\n\t}\n\n\tpublic getTimeScale(): Promise<number> {\n\t\treturn Promise.resolve(_module._fluid_sequencer_get_time_scale(this._seq));\n\t}\n\n\tpublic getTick(): Promise<number> {\n\t\treturn Promise.resolve(_module._fluid_sequencer_get_tick(this._seq));\n\t}\n\n\tpublic sendEventAt(event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t// send to all clients\n\t\t\tconst count = _module._fluid_sequencer_count_clients(this._seq);\n\t\t\tfor (let i = 0; i < count; ++i) {\n\t\t\t\tconst id: number = _module._fluid_sequencer_get_client_id(this._seq, i);\n\t\t\t\t_module._fluid_event_set_dest(ev, id);\n\t\t\t\t_module._fluid_sequencer_send_at(this._seq, ev, tick, isAbsolute ? 1 : 0);\n\t\t\t}\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\tpublic sendEventToClientAt(clientId: number, event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_at(this._seq, ev, tick, isAbsolute ? 1 : 0);\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic sendEventToClientNow(clientId: number, event: SequencerEvent): void {\n\t\tconst ev = makeEvent(event);\n\t\tif (ev !== null) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t\t_module._delete_fluid_event(ev);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic sendEventNow(clientId: number, eventData: ISequencerEventData): void {\n\t\tif (!(eventData instanceof SequencerEventData)) {\n\t\t\treturn;\n\t\t}\n\t\tconst ev = eventData.getRaw();\n\t\tif (ev !== INVALID_POINTER) {\n\t\t\t_module._fluid_event_set_dest(ev, clientId === -1 ? this._seqId : clientId);\n\t\t\t_module._fluid_sequencer_send_now(this._seq, ev);\n\t\t}\n\t}\n\n\tpublic removeAllEvents(): void {\n\t\t_module._fluid_sequencer_remove_events(this._seq, -1, -1, -1);\n\t}\n\n\tpublic removeAllEventsFromClient(clientId: number): void {\n\t\t_module._fluid_sequencer_remove_events(this._seq, -1, clientId === -1 ? this._seqId : clientId, -1);\n\t}\n\n\tpublic processSequencer(msecToProcess: number) {\n\t\tif (this._seq !== INVALID_POINTER) {\n\t\t\t_module._fluid_sequencer_process(this._seq, msecToProcess);\n\t\t}\n\t}\n\n\t/** @internal */\n\tpublic setIntervalForSequencer(msec: number) {\n\t\treturn setInterval(() => this.processSequencer(msec), msec);\n\t}\n}\n", "import { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport Preset from './Preset';\nimport Synthesizer from './Synthesizer';\n\ntype SFontPointer = UniquePointerType<'sfont_ptr'>;\ntype PresetPointer = UniquePointerType<'preset_ptr'>;\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n}\n\nlet _module: any;\n\nlet fluid_sfont_get_name: (sfont: SFontPointer) => string;\nlet fluid_preset_get_name: (preset: PresetPointer) => string;\n\nfunction bindFunctions() {\n\tif (_module) {\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t} else {\n\t\t_module = Module;\n\t}\n\n\tfluid_sfont_get_name =\n\t\t_module.cwrap('fluid_sfont_get_name', 'string', ['number']);\n\tfluid_preset_get_name =\n\t\t_module.cwrap('fluid_preset_get_name', 'string', ['number']);\n}\n\nexport default class Soundfont {\n\tprivate readonly _ptr: SFontPointer;\n\n\t// @internal\n\tpublic constructor(sfontPtr: SFontPointer) {\n\t\tthis._ptr = sfontPtr;\n\t}\n\n\tpublic static getSoundfontById(synth: Synthesizer, id: number): Soundfont | null {\n\t\tbindFunctions();\n\n\t\tconst sfont = _module._fluid_synth_get_sfont_by_id(synth.getRawSynthesizer(), id);\n\t\tif (sfont === INVALID_POINTER) {\n\t\t\treturn null;\n\t\t}\n\t\treturn new Soundfont(sfont);\n\t}\n\n\tpublic getName(): string {\n\t\treturn fluid_sfont_get_name(this._ptr);\n\t}\n\n\tpublic getPreset(bank: number, presetNum: number): Preset | null {\n\t\tconst presetPtr: PresetPointer = _module._fluid_sfont_get_preset(this._ptr, bank, presetNum);\n\t\tif (presetPtr === INVALID_POINTER) {\n\t\t\treturn null;\n\t\t}\n\t\tconst name = fluid_preset_get_name(presetPtr);\n\t\tconst bankNum = _module._fluid_preset_get_banknum(presetPtr);\n\t\tconst num = _module._fluid_preset_get_num(presetPtr);\n\t\treturn {\n\t\t\tsoundfont: this,\n\t\t\tname,\n\t\t\tbankNum,\n\t\t\tnum\n\t\t};\n\t}\n\n\tpublic getPresetIterable(): Iterable<Preset> {\n\t\tconst reset = () => {\n\t\t\t_module._fluid_sfont_iteration_start(this._ptr);\n\t\t};\n\t\tconst next = (): IteratorResult<Preset, void> => {\n\t\t\tconst presetPtr = _module._fluid_sfont_iteration_next(this._ptr);\n\t\t\tif (presetPtr === 0) {\n\t\t\t\treturn {\n\t\t\t\t\tdone: true,\n\t\t\t\t\tvalue: undefined\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tconst name = fluid_preset_get_name(presetPtr);\n\t\t\t\tconst bankNum = _module._fluid_preset_get_banknum(presetPtr);\n\t\t\t\tconst num = _module._fluid_preset_get_num(presetPtr);\n\t\t\t\treturn {\n\t\t\t\t\tdone: false,\n\t\t\t\t\tvalue: {\n\t\t\t\t\t\tsoundfont: this,\n\t\t\t\t\t\tname,\n\t\t\t\t\t\tbankNum,\n\t\t\t\t\t\tnum\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\t\t};\n\t\tconst iterator = (): Iterator<Preset> => {\n\t\t\treset();\n\t\t\treturn {\n\t\t\t\tnext,\n\t\t\t};\n\t\t};\n\t\treturn {\n\t\t\t[Symbol.iterator]: iterator,\n\t\t};\n\t}\n}\n", "\nimport {\n\tSynthesizerDefaultValues,\n\tInterpolationValues,\n\tChorusModulation,\n\tGeneratorTypes,\n\tLegatoMode,\n\tPortamentoMode,\n\tPlayerSetTempoType,\n} from './Constants';\nimport IMIDIEvent from './IMIDIEvent';\nimport ISequencer from './ISequencer';\nimport ISequencerEventData from './ISequencerEventData';\nimport ISynthesizer from './ISynthesizer';\nimport PointerType, { INVALID_POINTER, UniquePointerType } from './PointerType';\nimport SynthesizerSettings from './SynthesizerSettings';\n\nimport MIDIEvent, { MIDIEventType } from './MIDIEvent';\nimport Sequencer from './Sequencer';\nimport SequencerEvent, { EventType as SequencerEventType } from './SequencerEvent';\nimport SequencerEventData from './SequencerEventData';\nimport Soundfont from './Soundfont';\n\n/** @internal */\ndeclare global {\n\tvar Module: any;\n\tfunction addFunction(func: Function, sig: string): number;\n\tfunction removeFunction(funcPtr: number): void;\n\tfunction addOnPostRun(cb: (Module: any) => void): void;\n}\n\ntype SettingsId = UniquePointerType<'settings_id'>;\ntype SynthId = UniquePointerType<'synth_id'>;\ntype PlayerId = UniquePointerType<'player_id'>;\n\nlet _module: any;\nlet _addFunction: (func: Function, sig: string) => number;\nlet _removeFunction: (funcPtr: number) => void;\nlet _fs: any;\n\n// wrapper to use String type\nlet fluid_settings_setint: (settings: SettingsId, name: string, val: number) => number;\nlet fluid_settings_setnum: (settings: SettingsId, name: string, val: number) => number;\nlet fluid_settings_setstr: (settings: SettingsId, name: string, str: string) => number;\nlet fluid_synth_error: undefined | ((synth: SynthId) => string);\nlet fluid_synth_sfload: (synth: SynthId, filename: string, reset_presets: number) => number;\nlet fluid_sequencer_register_client: (seq: PointerType, name: string, callback: number, data: number) => number;\n\nlet malloc: (size: number) => PointerType;\nlet free: (ptr: PointerType) => void;\n\nlet defaultMIDIEventCallback: (data: PointerType, event: MIDIEventType) => number;\n\nfunction bindFunctions() {\n\tif (fluid_synth_error) {\n\t\t// (already bound)\n\t\treturn;\n\t}\n\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t\t_addFunction = AudioWorkletGlobalScope.wasmAddFunction;\n\t\t_removeFunction = AudioWorkletGlobalScope.wasmRemoveFunction;\n\t} else if (typeof Module !== 'undefined') {\n\t\t_module = Module;\n\t\t_addFunction = addFunction;\n\t\t_removeFunction = removeFunction;\n\t} else {\n\t\tthrow new Error('wasm module is not available. libfluidsynth-*.js must be loaded.');\n\t}\n\t_fs = _module.FS;\n\n\t// wrapper to use String type\n\tfluid_settings_setint =\n\t\t_module.cwrap('fluid_settings_setint', 'number', ['number', 'string', 'number']);\n\tfluid_settings_setnum =\n\t\t_module.cwrap('fluid_settings_setnum', 'number', ['number', 'string', 'number']);\n\tfluid_settings_setstr =\n\t\t_module.cwrap('fluid_settings_setstr', 'number', ['number', 'string', 'string']);\n\tfluid_synth_error =\n\t\t_module.cwrap('fluid_synth_error', 'string', ['number']);\n\tfluid_synth_sfload =\n\t\t_module.cwrap('fluid_synth_sfload', 'number', ['number', 'string', 'number']);\n\tfluid_sequencer_register_client =\n\t\t_module.cwrap('fluid_sequencer_register_client', 'number', ['number', 'string', 'number', 'number']);\n\n\tmalloc = _module._malloc.bind(_module);\n\tfree = _module._free.bind(_module);\n\n\tdefaultMIDIEventCallback = _module._fluid_synth_handle_midi_event.bind(_module);\n}\n\nlet promiseWaitForInitialized: Promise<void> | undefined;\nfunction waitForInitialized() {\n\tif (promiseWaitForInitialized) {\n\t\treturn promiseWaitForInitialized;\n\t}\n\n\tlet mod: any;\n\tlet addOnPostRunFn: ((cb: (Module: any) => void) => void) | undefined;\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\tmod = AudioWorkletGlobalScope.wasmModule;\n\t\taddOnPostRunFn = AudioWorkletGlobalScope.addOnPostRun;\n\t} else if (typeof Module !== 'undefined') {\n\t\tmod = Module;\n\t\taddOnPostRunFn = typeof addOnPostRun !== 'undefined' ? addOnPostRun : undefined;\n\t} else {\n\t\treturn Promise.reject(new Error('wasm module is not available. libfluidsynth-*.js must be loaded.'));\n\t}\n\tif (mod.calledRun) {\n\t\tpromiseWaitForInitialized = Promise.resolve();\n\t\treturn promiseWaitForInitialized;\n\t}\n\tif (typeof addOnPostRunFn === 'undefined') {\n\t\tpromiseWaitForInitialized = new Promise((resolve) => {\n\t\t\tconst fn: (() => void) | undefined = _module.onRuntimeInitialized;\n\t\t\t_module.onRuntimeInitialized = () => {\n\t\t\t\tresolve();\n\t\t\t\tif (fn) {\n\t\t\t\t\tfn();\n\t\t\t\t}\n\t\t\t};\n\t\t});\n\t} else {\n\t\tpromiseWaitForInitialized = new Promise((resolve) => {\n\t\t\taddOnPostRunFn!(resolve);\n\t\t});\n\t}\n\treturn promiseWaitForInitialized;\n}\n\nfunction setBoolValueForSettings(settings: SettingsId, name: string, value: boolean | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setint(settings, name, value ? 1 : 0);\n\t}\n}\nfunction setIntValueForSettings(settings: SettingsId, name: string, value: number | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setint(settings, name, value);\n\t}\n}\nfunction setNumValueForSettings(settings: SettingsId, name: string, value: number | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setnum(settings, name, value);\n\t}\n}\nfunction setStrValueForSettings(settings: SettingsId, name: string, value: string | undefined) {\n\tif (typeof value !== 'undefined') {\n\t\tfluid_settings_setstr(settings, name, value);\n\t}\n}\n\nfunction getActiveVoiceCount(synth: SynthId): number {\n\tconst actualCount = _module._fluid_synth_get_active_voice_count(synth);\n\tif (!actualCount) {\n\t\treturn 0;\n\t}\n\n\t// FluidSynth may return incorrect value for active voice count,\n\t// so check internal data and correct it\n\n\t// check if the structure is not changed\n\t// for fluidsynth 2.0.x-2.1.x:\n\t//   140 === offset [synth->voice]\n\t//   144 === offset [synth->active_voice_count] for \n\t// for fluidsynth 2.2.x:\n\t//   144 === offset [synth->voice]\n\t//   148 === offset [synth->active_voice_count]\n\t// first check 2.1.x structure\n\tlet baseOffsetOfVoice = 140;\n\tlet offsetOfActiveVoiceCount = (synth + baseOffsetOfVoice + 4) >> 2;\n\tlet structActiveVoiceCount = _module.HEAPU32[offsetOfActiveVoiceCount];\n\tif (structActiveVoiceCount !== actualCount) {\n\t\t// add 4 for 2.2.x\n\t\tbaseOffsetOfVoice += 4;\n\t\toffsetOfActiveVoiceCount = (synth + baseOffsetOfVoice + 4) >> 2;\n\t\tstructActiveVoiceCount = _module.HEAPU32[offsetOfActiveVoiceCount];\n\t\tif (structActiveVoiceCount !== actualCount) {\n\t\t\t// unknown structure\n\t\t\tconst c = console;\n\t\t\tc.warn(\n\t\t\t\t'js-synthesizer: cannot check synthesizer internal data (may be changed)'\n\t\t\t);\n\t\t\treturn actualCount;\n\t\t}\n\t}\n\n\tconst voiceList = _module.HEAPU32[(synth + baseOffsetOfVoice) >> 2];\n\t// (voice should not be NULL)\n\tif (!voiceList || voiceList >= _module.HEAPU32.byteLength) {\n\t\t// unknown structure\n\t\tconst c = console;\n\t\tc.warn(\n\t\t\t'js-synthesizer: cannot check synthesizer internal data (may be changed)'\n\t\t);\n\t\treturn actualCount;\n\t}\n\n\t// count of internal voice data is restricted to polyphony value\n\tconst voiceCount = _module._fluid_synth_get_polyphony(synth);\n\tlet isRunning = false;\n\tfor (let i = 0; i < voiceCount; ++i) {\n\t\t// auto voice = voiceList[i]\n\t\tconst voice = _module.HEAPU32[(voiceList >> 2) + i];\n\t\tif (!voice) {\n\t\t\tcontinue;\n\t\t}\n\t\t// offset [voice->status]\n\t\tconst status = _module.HEAPU8[voice + 4];\n\t\t// 4: FLUID_VOICE_OFF\n\t\tif (status !== 4) {\n\t\t\tisRunning = true;\n\t\t\tbreak;\n\t\t}\n\t}\n\tif (!isRunning) {\n\t\tif (structActiveVoiceCount !== 0) {\n\t\t\tconst c = console;\n\t\t\tc.warn(\n\t\t\t\t'js-synthesizer: Active voice count is not zero, but all voices are off:',\n\t\t\t\tstructActiveVoiceCount,\n\t\t\t);\n\t\t}\n\t\t_module.HEAPU32[offsetOfActiveVoiceCount] = 0;\n\t\treturn 0;\n\t}\n\n\treturn actualCount;\n}\n\nfunction makeRandomFileName(type: string, ext: string) {\n\treturn `/${type}-r${Math.random() * 65535}-${Math.random() * 65535}${ext}`;\n}\n\n/** Hook callback function type */\nexport interface HookMIDIEventCallback {\n\t/**\n\t * Hook callback function type.\n\t * @param synth the base synthesizer instance\n\t * @param eventType MIDI event type (e.g. 0x90 is note-on event)\n\t * @param eventData detailed event data\n\t * @param param parameter data passed to the registration method\n\t * @return true if the event data is processed, or false if the default processing is necessary\n\t */\n\t(synth: Synthesizer, eventType: number, eventData: IMIDIEvent, param: any): boolean;\n}\n\n/** Client callback function type for sequencer object */\nexport interface SequencerClientCallback {\n\t/**\n\t * Client callback function type for sequencer object.\n\t * @param time the sequencer tick value\n\t * @param eventType sequencer event type\n\t * @param event actual event data (can only be used in this callback function)\n\t * @param sequencer the base sequencer object\n\t * @param param parameter data passed to the registration method\n\t */\n\t(time: number, eventType: SequencerEventType, event: ISequencerEventData, sequencer: ISequencer, param: number): void;\n}\n\nfunction makeMIDIEventCallback(synth: Synthesizer, cb: HookMIDIEventCallback, param: any) {\n\treturn (data: PointerType, event: MIDIEventType): number => {\n\t\tconst t = _module._fluid_midi_event_get_type(event);\n\t\tif (cb(synth, t, new MIDIEvent(event, _module), param)) {\n\t\t\treturn 0;\n\t\t}\n\t\treturn _module._fluid_synth_handle_midi_event(data, event);\n\t};\n}\n\n/** Default implementation of ISynthesizer */\nexport default class Synthesizer implements ISynthesizer {\n\t/** @internal */\n\tprivate _settings: SettingsId;\n\t/** @internal */\n\tprivate _synth: SynthId;\n\t/** @internal */\n\tprivate _player: PlayerId;\n\t/** @internal */\n\tprivate _playerPlaying: boolean;\n\t/** @internal */\n\tprivate _playerDefer:\n\t\t| undefined\n\t\t| {\n\t\t\t\tpromise: Promise<void>;\n\t\t\t\tresolve: () => void;\n\t\t  };\n\t/** @internal */\n\tprivate _playerCallbackPtr: number | null;\n\t/** @internal */\n\tprivate _fluidSynthCallback: PointerType | null;\n\n\t/** @internal */\n\tprivate _buffer: PointerType;\n\t/** @internal */\n\tprivate _bufferSize: number;\n\t/** @internal */\n\tprivate _numPtr: PointerType;\n\n\t/** @internal */\n\tprivate _gain: number;\n\n\tconstructor() {\n\t\tbindFunctions();\n\n\t\tthis._settings = INVALID_POINTER;\n\t\tthis._synth = INVALID_POINTER;\n\t\tthis._player = INVALID_POINTER;\n\t\tthis._playerPlaying = false;\n\t\tthis._playerCallbackPtr = null;\n\t\tthis._fluidSynthCallback = null;\n\n\t\tthis._buffer = INVALID_POINTER;\n\t\tthis._bufferSize = 0;\n\t\tthis._numPtr = INVALID_POINTER;\n\n\t\tthis._gain = SynthesizerDefaultValues.Gain;\n\t}\n\n\t/** Return the promise object that resolves when WebAssembly has been initialized */\n\tpublic static waitForWasmInitialized(): Promise<void> {\n\t\treturn waitForInitialized();\n\t}\n\n\tpublic isInitialized() {\n\t\treturn this._synth !== INVALID_POINTER;\n\t}\n\n\t/** Return the raw synthesizer instance value (pointer for libfluidsynth). */\n\tpublic getRawSynthesizer(): number {\n\t\treturn this._synth;\n\t}\n\n\tpublic createAudioNode(\n\t\tcontext: AudioContext,\n\t\tframeSize?: number\n\t): AudioNode {\n\t\tconst node = context.createScriptProcessor(frameSize, 0, 2);\n\t\tnode.addEventListener(\"audioprocess\", (ev) => {\n\t\t\tthis.render(ev.outputBuffer);\n\t\t});\n\t\treturn node;\n\t}\n\n\tpublic init(sampleRate: number, settings?: SynthesizerSettings) {\n\t\tthis.close();\n\n\t\tconst set = (this._settings = _module._new_fluid_settings());\n\t\tfluid_settings_setnum(set, \"synth.sample-rate\", sampleRate);\n\t\tif (settings) {\n\t\t\tif (typeof settings.initialGain !== \"undefined\") {\n\t\t\t\tthis._gain = settings.initialGain;\n\t\t\t}\n\t\t\tsetBoolValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.active\",\n\t\t\t\tsettings.chorusActive\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.depth\",\n\t\t\t\tsettings.chorusDepth\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.level\",\n\t\t\t\tsettings.chorusLevel\n\t\t\t);\n\t\t\tsetIntValueForSettings(set, \"synth.chorus.nr\", settings.chorusNr);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.chorus.speed\",\n\t\t\t\tsettings.chorusSpeed\n\t\t\t);\n\t\t\tsetIntValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.midi-channels\",\n\t\t\t\tsettings.midiChannelCount\n\t\t\t);\n\t\t\tsetStrValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.midi-bank-select\",\n\t\t\t\tsettings.midiBankSelect\n\t\t\t);\n\t\t\tsetIntValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.min-note-length\",\n\t\t\t\tsettings.minNoteLength\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.age\",\n\t\t\t\tsettings.overflowAge\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.important\",\n\t\t\t\tsettings.overflowImportantValue\n\t\t\t);\n\t\t\tif (typeof settings.overflowImportantChannels !== \"undefined\") {\n\t\t\t\tfluid_settings_setstr(\n\t\t\t\t\tset,\n\t\t\t\t\t\"synth.overflow.important-channels\",\n\t\t\t\t\tsettings.overflowImportantChannels.join(\",\")\n\t\t\t\t);\n\t\t\t}\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.percussion\",\n\t\t\t\tsettings.overflowPercussion\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.released\",\n\t\t\t\tsettings.overflowReleased\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.sustained\",\n\t\t\t\tsettings.overflowSustained\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.overflow.volume\",\n\t\t\t\tsettings.overflowVolume\n\t\t\t);\n\t\t\tsetIntValueForSettings(set, \"synth.polyphony\", settings.polyphony);\n\t\t\tsetBoolValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.active\",\n\t\t\t\tsettings.reverbActive\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.damp\",\n\t\t\t\tsettings.reverbDamp\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.level\",\n\t\t\t\tsettings.reverbLevel\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.room-size\",\n\t\t\t\tsettings.reverbRoomSize\n\t\t\t);\n\t\t\tsetNumValueForSettings(\n\t\t\t\tset,\n\t\t\t\t\"synth.reverb.width\",\n\t\t\t\tsettings.reverbWidth\n\t\t\t);\n\t\t}\n\t\tfluid_settings_setnum(set, \"synth.gain\", this._gain);\n\n\t\tthis._synth = _module._new_fluid_synth(this._settings);\n\n\t\tthis._numPtr = malloc(8);\n\t}\n\n\tpublic close() {\n\t\tif (this._synth === INVALID_POINTER) {\n\t\t\treturn;\n\t\t}\n\t\tthis._closePlayer();\n\t\t_module._delete_fluid_synth(this._synth);\n\t\tthis._synth = INVALID_POINTER;\n\t\t_module._delete_fluid_settings(this._settings);\n\t\tthis._settings = INVALID_POINTER;\n\t\tfree(this._numPtr);\n\t\tthis._numPtr = INVALID_POINTER;\n\t}\n\n\tpublic isPlaying() {\n\t\treturn (\n\t\t\tthis._synth !== INVALID_POINTER &&\n\t\t\tgetActiveVoiceCount(this._synth) > 0\n\t\t);\n\t}\n\n\tpublic setInterpolation(value: InterpolationValues, channel?: number) {\n\t\tthis.ensureInitialized();\n\t\tif (typeof channel === \"undefined\") {\n\t\t\tchannel = -1;\n\t\t}\n\t\t_module._fluid_synth_set_interp_method(this._synth, channel, value);\n\t}\n\n\tpublic getGain() {\n\t\treturn this._gain;\n\t}\n\n\tpublic setGain(gain: number) {\n\t\tthis.ensureInitialized();\n\t\t_module._fluid_synth_set_gain(this._synth, gain);\n\t\tthis._gain = _module._fluid_synth_get_gain(this._synth);\n\t}\n\n\tpublic setChannelType(channel: number, isDrum: boolean) {\n\t\tthis.ensureInitialized();\n\t\t// CHANNEL_TYPE_MELODIC = 0, CHANNEL_TYPE_DRUM = 1\n\t\t_module._fluid_synth_set_channel_type(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tisDrum ? 1 : 0\n\t\t);\n\t}\n\n\tpublic waitForVoicesStopped() {\n\t\treturn this.flushFramesAsync();\n\t}\n\n\tpublic loadSFont(bin: ArrayBuffer) {\n\t\tthis.ensureInitialized();\n\n\t\tconst name = makeRandomFileName(\"sfont\", \".sf2\");\n\t\tconst ub = new Uint8Array(bin);\n\n\t\t_fs.writeFile(name, ub);\n\t\tconst sfont = fluid_synth_sfload(this._synth, name, 1);\n\t\t_fs.unlink(name);\n\t\treturn sfont === -1\n\t\t\t? Promise.reject(new Error(fluid_synth_error!(this._synth)))\n\t\t\t: Promise.resolve(sfont);\n\t}\n\n\tpublic unloadSFont(id: number) {\n\t\tthis.ensureInitialized();\n\t\tthis.stopPlayer();\n\t\tthis.flushFramesSync();\n\n\t\t_module._fluid_synth_sfunload(this._synth, id, 1);\n\t}\n\n\tpublic unloadSFontAsync(id: number) {\n\t\t// not throw with Promise.reject\n\t\tthis.ensureInitialized();\n\t\tthis.stopPlayer();\n\t\treturn this.flushFramesAsync().then(() => {\n\t\t\t_module._fluid_synth_sfunload(this._synth, id, 1);\n\t\t});\n\t}\n\n\t/**\n\t * Returns the `Soundfont` instance for specified SoundFont.\n\t * @param sfontId loaded SoundFont id ({@link loadSFont} returns this)\n\t * @return `Soundfont` instance or `null` if `sfontId` is not valid or loaded\n\t */\n\tpublic getSFontObject(sfontId: number): Soundfont | null {\n\t\treturn Soundfont.getSoundfontById(this, sfontId);\n\t}\n\n\tpublic getSFontBankOffset(id: number) {\n\t\tthis.ensureInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_synth_get_bank_offset(this._synth, id) as number\n\t\t);\n\t}\n\tpublic setSFontBankOffset(id: number, offset: number) {\n\t\tthis.ensureInitialized();\n\t\t_module._fluid_synth_set_bank_offset(this._synth, id, offset);\n\t}\n\n\tpublic render(outBuffer: AudioBuffer | Float32Array[]) {\n\t\tconst frameCount =\n\t\t\t\"numberOfChannels\" in outBuffer\n\t\t\t\t? outBuffer.length\n\t\t\t\t: outBuffer[0].length;\n\t\tconst channels =\n\t\t\t\"numberOfChannels\" in outBuffer\n\t\t\t\t? outBuffer.numberOfChannels\n\t\t\t\t: outBuffer.length;\n\t\tconst sizePerChannel = 4 * frameCount;\n\t\tconst totalSize = sizePerChannel * 2;\n\t\tif (this._bufferSize < totalSize) {\n\t\t\tif (this._buffer !== INVALID_POINTER) {\n\t\t\t\tfree(this._buffer);\n\t\t\t}\n\t\t\tthis._buffer = malloc(totalSize);\n\t\t\tthis._bufferSize = totalSize;\n\t\t}\n\n\t\tconst memLeft = this._buffer;\n\t\tconst memRight = ((this._buffer as number) +\n\t\t\tsizePerChannel) as PointerType;\n\t\tthis.renderRaw(memLeft, memRight, frameCount);\n\n\t\tconst aLeft = new Float32Array(\n\t\t\t_module.HEAPU8.buffer,\n\t\t\tmemLeft,\n\t\t\tframeCount\n\t\t);\n\t\tconst aRight =\n\t\t\tchannels >= 2\n\t\t\t\t? new Float32Array(_module.HEAPU8.buffer, memRight, frameCount)\n\t\t\t\t: null;\n\t\tif (\"numberOfChannels\" in outBuffer) {\n\t\t\tif (outBuffer.copyToChannel) {\n\t\t\t\toutBuffer.copyToChannel(aLeft, 0, 0);\n\t\t\t\tif (aRight) {\n\t\t\t\t\toutBuffer.copyToChannel(aRight, 1, 0);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// copyToChannel API not exist in Safari AudioBuffer\n\t\t\t\tconst leftData = outBuffer.getChannelData(0);\n\t\t\t\taLeft.forEach((val, i) => (leftData[i] = val));\n\t\t\t\tif (aRight) {\n\t\t\t\t\tconst rightData = outBuffer.getChannelData(1);\n\t\t\t\t\taRight.forEach((val, i) => (rightData[i] = val));\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\toutBuffer[0].set(aLeft);\n\t\t\tif (aRight) {\n\t\t\t\toutBuffer[1].set(aRight);\n\t\t\t}\n\t\t}\n\n\t\t// check and update player status\n\t\tthis.isPlayerPlaying();\n\t}\n\n\tpublic midiNoteOn(chan: number, key: number, vel: number) {\n\t\t_module._fluid_synth_noteon(this._synth, chan, key, vel);\n\t}\n\tpublic midiNoteOff(chan: number, key: number) {\n\t\t_module._fluid_synth_noteoff(this._synth, chan, key);\n\t}\n\tpublic midiKeyPressure(chan: number, key: number, val: number) {\n\t\t_module._fluid_synth_key_pressure(this._synth, chan, key, val);\n\t}\n\tpublic midiControl(chan: number, ctrl: number, val: number) {\n\t\t_module._fluid_synth_cc(this._synth, chan, ctrl, val);\n\t}\n\tpublic midiProgramChange(chan: number, prognum: number) {\n\t\t_module._fluid_synth_program_change(this._synth, chan, prognum);\n\t}\n\tpublic midiChannelPressure(chan: number, val: number) {\n\t\t_module._fluid_synth_channel_pressure(this._synth, chan, val);\n\t}\n\tpublic midiPitchBend(chan: number, val: number) {\n\t\t_module._fluid_synth_pitch_bend(this._synth, chan, val);\n\t}\n\tpublic midiSysEx(data: Uint8Array) {\n\t\tconst len = data.byteLength;\n\t\tconst mem = malloc(len);\n\t\t_module.HEAPU8.set(data, mem);\n\t\t_module._fluid_synth_sysex(\n\t\t\tthis._synth,\n\t\t\tmem,\n\t\t\tlen,\n\t\t\tINVALID_POINTER,\n\t\t\tINVALID_POINTER,\n\t\t\tINVALID_POINTER,\n\t\t\t0\n\t\t);\n\t\tfree(mem);\n\t}\n\n\tpublic midiPitchWheelSensitivity(chan: number, val: number) {\n\t\t_module._fluid_synth_pitch_wheel_sens(this._synth, chan, val);\n\t}\n\tpublic midiBankSelect(chan: number, bank: number) {\n\t\t_module._fluid_synth_bank_select(this._synth, chan, bank);\n\t}\n\tpublic midiSFontSelect(chan: number, sfontId: number) {\n\t\t_module._fluid_synth_sfont_select(this._synth, chan, sfontId);\n\t}\n\tpublic midiProgramSelect(\n\t\tchan: number,\n\t\tsfontId: number,\n\t\tbank: number,\n\t\tpresetNum: number\n\t) {\n\t\t_module._fluid_synth_program_select(\n\t\t\tthis._synth,\n\t\t\tchan,\n\t\t\tsfontId,\n\t\t\tbank,\n\t\t\tpresetNum\n\t\t);\n\t}\n\tpublic midiUnsetProgram(chan: number) {\n\t\t_module._fluid_synth_unset_program(this._synth, chan);\n\t}\n\tpublic midiProgramReset() {\n\t\t_module._fluid_synth_program_reset(this._synth);\n\t}\n\tpublic midiSystemReset() {\n\t\t_module._fluid_synth_system_reset(this._synth);\n\t}\n\tpublic midiAllNotesOff(chan?: number) {\n\t\t_module._fluid_synth_all_notes_off(\n\t\t\tthis._synth,\n\t\t\ttypeof chan === \"undefined\" ? -1 : chan\n\t\t);\n\t}\n\tpublic midiAllSoundsOff(chan?: number) {\n\t\t_module._fluid_synth_all_sounds_off(\n\t\t\tthis._synth,\n\t\t\ttypeof chan === \"undefined\" ? -1 : chan\n\t\t);\n\t}\n\tpublic midiSetChannelType(chan: number, isDrum: boolean) {\n\t\t// CHANNEL_TYPE_MELODIC = 0\n\t\t// CHANNEL_TYPE_DRUM = 1\n\t\t_module._fluid_synth_set_channel_type(\n\t\t\tthis._synth,\n\t\t\tchan,\n\t\t\tisDrum ? 1 : 0\n\t\t);\n\t}\n\n\t/**\n\t * Set reverb parameters to the synthesizer.\n\t */\n\tpublic setReverb(\n\t\troomsize: number,\n\t\tdamping: number,\n\t\twidth: number,\n\t\tlevel: number\n\t) {\n\t\t_module._fluid_synth_set_reverb(\n\t\t\tthis._synth,\n\t\t\troomsize,\n\t\t\tdamping,\n\t\t\twidth,\n\t\t\tlevel\n\t\t);\n\t}\n\t/**\n\t * Set reverb roomsize parameter to the synthesizer.\n\t */\n\tpublic setReverbRoomsize(roomsize: number) {\n\t\t_module._fluid_synth_set_reverb_roomsize(this._synth, roomsize);\n\t}\n\t/**\n\t * Set reverb damping parameter to the synthesizer.\n\t */\n\tpublic setReverbDamp(damping: number) {\n\t\t_module._fluid_synth_set_reverb_damp(this._synth, damping);\n\t}\n\t/**\n\t * Set reverb width parameter to the synthesizer.\n\t */\n\tpublic setReverbWidth(width: number) {\n\t\t_module._fluid_synth_set_reverb_width(this._synth, width);\n\t}\n\t/**\n\t * Set reverb level to the synthesizer.\n\t */\n\tpublic setReverbLevel(level: number) {\n\t\t_module._fluid_synth_set_reverb_level(this._synth, level);\n\t}\n\t/**\n\t * Enable or disable reverb effect of the synthesizer.\n\t */\n\tpublic setReverbOn(on: boolean) {\n\t\t_module._fluid_synth_set_reverb_on(this._synth, on ? 1 : 0);\n\t}\n\t/**\n\t * Get reverb roomsize parameter of the synthesizer.\n\t */\n\tpublic getReverbRoomsize(): number {\n\t\treturn _module._fluid_synth_get_reverb_roomsize(this._synth);\n\t}\n\t/**\n\t * Get reverb damping parameter of the synthesizer.\n\t */\n\tpublic getReverbDamp(): number {\n\t\treturn _module._fluid_synth_get_reverb_damp(this._synth);\n\t}\n\t/**\n\t * Get reverb level of the synthesizer.\n\t */\n\tpublic getReverbLevel(): number {\n\t\treturn _module._fluid_synth_get_reverb_level(this._synth);\n\t}\n\t/**\n\t * Get reverb width parameter of the synthesizer.\n\t */\n\tpublic getReverbWidth(): number {\n\t\treturn _module._fluid_synth_get_reverb_width(this._synth);\n\t}\n\n\t/**\n\t * Set chorus parameters to the synthesizer.\n\t */\n\tpublic setChorus(\n\t\tvoiceCount: number,\n\t\tlevel: number,\n\t\tspeed: number,\n\t\tdepthMillisec: number,\n\t\ttype: ChorusModulation\n\t) {\n\t\t_module._fluid_synth_set_chorus(\n\t\t\tthis._synth,\n\t\t\tvoiceCount,\n\t\t\tlevel,\n\t\t\tspeed,\n\t\t\tdepthMillisec,\n\t\t\ttype\n\t\t);\n\t}\n\t/**\n\t * Set chorus voice count parameter to the synthesizer.\n\t */\n\tpublic setChorusVoiceCount(voiceCount: number) {\n\t\t_module._fluid_synth_set_chorus_nr(this._synth, voiceCount);\n\t}\n\t/**\n\t * Set chorus level parameter to the synthesizer.\n\t */\n\tpublic setChorusLevel(level: number) {\n\t\t_module._fluid_synth_set_chorus_level(this._synth, level);\n\t}\n\t/**\n\t * Set chorus speed parameter to the synthesizer.\n\t */\n\tpublic setChorusSpeed(speed: number) {\n\t\t_module._fluid_synth_set_chorus_speed(this._synth, speed);\n\t}\n\t/**\n\t * Set chorus depth parameter to the synthesizer.\n\t */\n\tpublic setChorusDepth(depthMillisec: number) {\n\t\t_module._fluid_synth_set_chorus_depth(this._synth, depthMillisec);\n\t}\n\t/**\n\t * Set chorus modulation type to the synthesizer.\n\t */\n\tpublic setChorusType(type: ChorusModulation) {\n\t\t_module._fluid_synth_set_chorus_type(this._synth, type);\n\t}\n\t/**\n\t * Enable or disable chorus effect of the synthesizer.\n\t */\n\tpublic setChorusOn(on: boolean) {\n\t\t_module._fluid_synth_set_chorus_on(this._synth, on ? 1 : 0);\n\t}\n\t/**\n\t * Get chorus voice count of the synthesizer.\n\t */\n\tpublic getChorusVoiceCount(): number {\n\t\treturn _module._fluid_synth_get_chorus_nr(this._synth);\n\t}\n\t/**\n\t * Get chorus level of the synthesizer.\n\t */\n\tpublic getChorusLevel(): number {\n\t\treturn _module._fluid_synth_get_chorus_level(this._synth);\n\t}\n\t/**\n\t * Get chorus speed of the synthesizer.\n\t */\n\tpublic getChorusSpeed(): number {\n\t\treturn _module._fluid_synth_get_chorus_speed(this._synth);\n\t}\n\t/**\n\t * Get chorus depth (in milliseconds) of the synthesizer.\n\t */\n\tpublic getChorusDepth(): number {\n\t\treturn _module._fluid_synth_get_chorus_depth(this._synth);\n\t}\n\t/**\n\t * Get chorus modulation type of the synthesizer.\n\t */\n\tpublic getChorusType(): ChorusModulation {\n\t\treturn _module._fluid_synth_get_chorus_type(this._synth);\n\t}\n\n\t/**\n\t * Get generator value assigned to the MIDI channel.\n\t * @param channel MIDI channel number\n\t * @param param generator ID\n\t * @return a value related to the generator\n\t */\n\tpublic getGenerator(channel: number, param: GeneratorTypes): number {\n\t\treturn _module._fluid_synth_get_gen(this._synth, channel, param);\n\t}\n\t/**\n\t * Set generator value assigned to the MIDI channel.\n\t * @param channel MIDI channel number\n\t * @param param generator ID\n\t * @param value a value related to the generator\n\t */\n\tpublic setGenerator(channel: number, param: GeneratorTypes, value: number) {\n\t\t_module._fluid_synth_set_gen(this._synth, channel, param, value);\n\t}\n\t/**\n\t * Return the current legato mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return legato mode\n\t */\n\tpublic getLegatoMode(channel: number) {\n\t\t_module._fluid_synth_get_legato_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as LegatoMode;\n\t}\n\t/**\n\t * Set the current legato mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param mode legato mode\n\t */\n\tpublic setLegatoMode(channel: number, mode: LegatoMode) {\n\t\t_module._fluid_synth_set_legato_mode(this._synth, channel, mode);\n\t}\n\t/**\n\t * Return the current portamento mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return portamento mode\n\t */\n\tpublic getPortamentoMode(channel: number) {\n\t\t_module._fluid_synth_get_portamento_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as PortamentoMode;\n\t}\n\t/**\n\t * Set the current portamento mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param mode portamento mode\n\t */\n\tpublic setPortamentoMode(channel: number, mode: PortamentoMode) {\n\t\t_module._fluid_synth_set_portamento_mode(this._synth, channel, mode);\n\t}\n\t/**\n\t * Return the current breath mode of the channel.\n\t * @param channel MIDI channel number\n\t * @return breath mode (BreathFlags)\n\t */\n\tpublic getBreathMode(channel: number) {\n\t\t_module._fluid_synth_get_breath_mode(\n\t\t\tthis._synth,\n\t\t\tchannel,\n\t\t\tthis._numPtr\n\t\t);\n\t\treturn _module.HEAP32[(this._numPtr as number) >> 2] as number;\n\t}\n\t/**\n\t * Set the current breath mode of the channel.\n\t * @param channel MIDI channel number\n\t * @param flags breath mode flags (BreathFlags)\n\t */\n\tpublic setBreathMode(channel: number, flags: number) {\n\t\t_module._fluid_synth_set_breath_mode(this._synth, channel, flags);\n\t}\n\n\t////////////////////////////////////////////////////////////////////////////\n\n\tpublic resetPlayer() {\n\t\treturn new Promise<void>((resolve) => {\n\t\t\tthis._initPlayer();\n\t\t\tresolve();\n\t\t});\n\t}\n\n\tpublic closePlayer() {\n\t\tthis._closePlayer();\n\t}\n\n\t/** @internal */\n\tprivate _initPlayer() {\n\t\tthis._closePlayer();\n\n\t\tconst player = _module._new_fluid_player(this._synth);\n\t\tthis._player = player;\n\t\tif (player !== INVALID_POINTER) {\n\t\t\tif (this._fluidSynthCallback === null) {\n\t\t\t\t// hacky retrieve 'fluid_synth_handle_midi_event' callback pointer\n\t\t\t\t// * 'playback_callback' is filled with 'fluid_synth_handle_midi_event' by default.\n\t\t\t\t// * 'playback_userdata' is filled with the synthesizer pointer by default\n\t\t\t\tconst funcPtr: PointerType =\n\t\t\t\t\t_module.HEAPU32[((player as number) + 588) >> 2]; // _fluid_player_t::playback_callback\n\t\t\t\tconst synthPtr: SynthId =\n\t\t\t\t\t_module.HEAPU32[((player as number) + 592) >> 2]; // _fluid_player_t::playback_userdata\n\t\t\t\tif (synthPtr === this._synth) {\n\t\t\t\t\tthis._fluidSynthCallback = funcPtr;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tthrow new Error(\"Out of memory\");\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate _closePlayer() {\n\t\tconst p = this._player;\n\t\tif (p === INVALID_POINTER) {\n\t\t\treturn;\n\t\t}\n\t\tthis.stopPlayer();\n\t\t_module._delete_fluid_player(p);\n\t\tthis._player = INVALID_POINTER;\n\t\tthis._playerCallbackPtr = null;\n\t}\n\n\tpublic isPlayerPlaying() {\n\t\tif (this._playerPlaying) {\n\t\t\tconst status = _module._fluid_player_get_status(this._player);\n\t\t\tif (status === 1 /*FLUID_PLAYER_PLAYING*/) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tthis.stopPlayer();\n\t\t}\n\t\treturn false;\n\t}\n\n\tpublic addSMFDataToPlayer(bin: ArrayBuffer) {\n\t\tthis.ensurePlayerInitialized();\n\t\tconst len = bin.byteLength;\n\t\tconst mem = malloc(len);\n\t\t_module.HEAPU8.set(new Uint8Array(bin), mem);\n\t\tconst r: number = _module._fluid_player_add_mem(this._player, mem, len);\n\t\tfree(mem);\n\t\treturn r !== -1\n\t\t\t? Promise.resolve()\n\t\t\t: Promise.reject(new Error(fluid_synth_error!(this._synth)));\n\t}\n\n\tpublic playPlayer() {\n\t\tthis.ensurePlayerInitialized();\n\t\tif (this._playerPlaying) {\n\t\t\tthis.stopPlayer();\n\t\t}\n\n\t\tif (_module._fluid_player_play(this._player) === -1) {\n\t\t\treturn Promise.reject(new Error(fluid_synth_error!(this._synth)));\n\t\t}\n\t\tthis._playerPlaying = true;\n\t\tlet resolver = () => {};\n\t\tconst p = new Promise<void>((resolve) => {\n\t\t\tresolver = resolve;\n\t\t});\n\t\tthis._playerDefer = {\n\t\t\tpromise: p,\n\t\t\tresolve: resolver,\n\t\t};\n\t\treturn Promise.resolve();\n\t}\n\n\tpublic stopPlayer() {\n\t\tconst p = this._player;\n\t\tif (p === INVALID_POINTER || !this._playerPlaying) {\n\t\t\treturn;\n\t\t}\n\t\t_module._fluid_player_stop(p);\n\t\t_module._fluid_player_join(p);\n\t\t_module._fluid_synth_all_sounds_off(this._synth, -1);\n\t\tif (this._playerDefer) {\n\t\t\tthis._playerDefer.resolve();\n\t\t\tthis._playerDefer = void 0;\n\t\t}\n\t\tthis._playerPlaying = false;\n\t}\n\n\tpublic retrievePlayerCurrentTick(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_current_tick(this._player)\n\t\t);\n\t}\n\tpublic retrievePlayerTotalTicks(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_total_ticks(this._player)\n\t\t);\n\t}\n\tpublic retrievePlayerBpm(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(_module._fluid_player_get_bpm(this._player));\n\t}\n\tpublic retrievePlayerMIDITempo(): Promise<number> {\n\t\tthis.ensurePlayerInitialized();\n\t\treturn Promise.resolve(\n\t\t\t_module._fluid_player_get_midi_tempo(this._player)\n\t\t);\n\t}\n\tpublic seekPlayer(ticks: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_seek(this._player, ticks);\n\t}\n\tpublic setPlayerLoop(loopTimes: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_set_loop(this._player, loopTimes);\n\t}\n\tpublic setPlayerTempo(tempoType: PlayerSetTempoType, tempo: number): void {\n\t\tthis.ensurePlayerInitialized();\n\t\t_module._fluid_player_set_tempo(this._player, tempoType, tempo);\n\t}\n\n\t/**\n\t * Hooks MIDI events sent by the player.\n\t * initPlayer() must be called before calling this method.\n\t * @param callback hook callback function, or null to unhook\n\t * @param param any additional data passed to the callback\n\t */\n\tpublic hookPlayerMIDIEvents(\n\t\tcallback: HookMIDIEventCallback | null,\n\t\tparam?: any\n\t) {\n\t\tthis.ensurePlayerInitialized();\n\n\t\tconst oldPtr = this._playerCallbackPtr;\n\t\tif (oldPtr === null && callback === null) {\n\t\t\treturn;\n\t\t}\n\t\tconst newPtr =\n\t\t\t// if callback is specified, add function\n\t\t\tcallback !== null\n\t\t\t\t? _addFunction(\n\t\t\t\t\t\tmakeMIDIEventCallback(this, callback, param),\n\t\t\t\t\t\t\"iii\"\n\t\t\t\t  )\n\t\t\t\t: // if _fluidSynthCallback is filled, set null to use it for reset callback\n\t\t\t\t// if not, add function defaultMIDIEventCallback for reset\n\t\t\t\tthis._fluidSynthCallback !== null\n\t\t\t\t? null\n\t\t\t\t: _addFunction(defaultMIDIEventCallback, \"iii\");\n\t\t// the third parameter of 'fluid_player_set_playback_callback' should be 'fluid_synth_t*'\n\t\tif (oldPtr !== null && newPtr !== null) {\n\t\t\t// (using defaultMIDIEventCallback also comes here)\n\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\tthis._player,\n\t\t\t\tnewPtr,\n\t\t\t\tthis._synth\n\t\t\t);\n\t\t\t_removeFunction(oldPtr);\n\t\t} else {\n\t\t\tif (newPtr === null) {\n\t\t\t\t// newPtr === null --> use _fluidSynthCallback\n\t\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\t\tthis._player,\n\t\t\t\t\tthis._fluidSynthCallback!,\n\t\t\t\t\tthis._synth\n\t\t\t\t);\n\t\t\t\t_removeFunction(oldPtr!);\n\t\t\t} else {\n\t\t\t\t_module._fluid_player_set_playback_callback(\n\t\t\t\t\tthis._player,\n\t\t\t\t\tnewPtr,\n\t\t\t\t\tthis._synth\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t\tthis._playerCallbackPtr = newPtr;\n\t}\n\n\t/** @internal */\n\tprivate ensureInitialized() {\n\t\tif (this._synth === INVALID_POINTER) {\n\t\t\tthrow new Error(\"Synthesizer is not initialized\");\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate ensurePlayerInitialized() {\n\t\tthis.ensureInitialized();\n\t\tif (this._player === INVALID_POINTER) {\n\t\t\tthis._initPlayer();\n\t\t}\n\t}\n\n\t/** @internal */\n\tprivate renderRaw(\n\t\tmemLeft: PointerType,\n\t\tmemRight: PointerType,\n\t\tframeCount: number\n\t) {\n\t\t_module._fluid_synth_write_float(\n\t\t\tthis._synth,\n\t\t\tframeCount,\n\t\t\tmemLeft,\n\t\t\t0,\n\t\t\t1,\n\t\t\tmemRight,\n\t\t\t0,\n\t\t\t1\n\t\t);\n\t}\n\n\t/** @internal */\n\tprivate flushFramesSync() {\n\t\tconst frameCount = 65536;\n\t\tconst size = 4 * frameCount;\n\t\tconst mem = malloc(size * 2);\n\t\tconst memLeft = mem;\n\t\tconst memRight = ((mem as number) + size) as PointerType;\n\t\twhile (this.isPlaying()) {\n\t\t\tthis.renderRaw(memLeft, memRight, frameCount);\n\t\t}\n\t\tfree(mem);\n\t}\n\n\t/** @internal */\n\tprivate flushFramesAsync() {\n\t\tif (!this.isPlaying()) {\n\t\t\treturn Promise.resolve();\n\t\t}\n\t\tconst frameCount = 65536;\n\t\tconst size = 4 * frameCount;\n\t\tconst mem = malloc(size * 2);\n\t\tconst memLeft = mem;\n\t\tconst memRight = ((mem as number) + size) as PointerType;\n\t\tconst nextFrame =\n\t\t\ttypeof setTimeout !== \"undefined\"\n\t\t\t\t? () => {\n\t\t\t\t\t\treturn new Promise<void>((resolve) =>\n\t\t\t\t\t\t\tsetTimeout(resolve, 0)\n\t\t\t\t\t\t);\n\t\t\t\t  }\n\t\t\t\t: () => {\n\t\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t  };\n\t\tfunction head(): Promise<void> {\n\t\t\treturn nextFrame().then(tail);\n\t\t}\n\t\tconst self = this;\n\t\tfunction tail(): Promise<void> {\n\t\t\tif (!self.isPlaying()) {\n\t\t\t\tfree(mem);\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\t\t\tself.renderRaw(memLeft, memRight, frameCount);\n\t\t\treturn head();\n\t\t}\n\t\treturn head();\n\t}\n\n\tpublic waitForPlayerStopped() {\n\t\treturn this._playerDefer\n\t\t\t? this._playerDefer.promise\n\t\t\t: Promise.resolve();\n\t}\n\n\t/**\n\t * Create the sequencer object for this class.\n\t */\n\tpublic static createSequencer(): Promise<ISequencer> {\n\t\tbindFunctions();\n\t\tconst seq = new Sequencer();\n\t\treturn seq._initialize().then(() => seq);\n\t}\n\n\t/**\n\t * Registers the user-defined client to the sequencer.\n\t * The client can receive events in the time from sequencer process.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param name the client name\n\t * @param callback the client callback function that processes event data\n\t * @param param additional parameter passed to the callback\n\t * @return registered sequencer client id (can be passed to seq.unregisterClient())\n\t */\n\tpublic static registerSequencerClient(\n\t\tseq: ISequencer,\n\t\tname: string,\n\t\tcallback: SequencerClientCallback,\n\t\tparam: number\n\t): number {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tconst ptr = _addFunction(\n\t\t\t(time: number, ev: PointerType, _seq: number, data: number) => {\n\t\t\t\tconst e = new SequencerEventData(ev, _module);\n\t\t\t\tconst type: SequencerEventType =\n\t\t\t\t\t_module._fluid_event_get_type(ev);\n\t\t\t\tcallback(time, type, e, seq, data);\n\t\t\t},\n\t\t\t\"viiii\"\n\t\t);\n\t\tconst r = fluid_sequencer_register_client(\n\t\t\tseq.getRaw(),\n\t\t\tname,\n\t\t\tptr,\n\t\t\tparam\n\t\t);\n\t\tif (r !== -1) {\n\t\t\tseq._clientFuncMap[r] = ptr;\n\t\t}\n\t\treturn r;\n\t}\n\n\t/**\n\t * Send sequencer event immediately to the specific client.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param clientId registered client id (-1 for registered synthesizer)\n\t * @param event event data\n\t */\n\tpublic static sendEventToClientNow(\n\t\tseq: ISequencer,\n\t\tclientId: number,\n\t\tevent: SequencerEvent\n\t): void {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tseq.sendEventToClientNow(clientId, event);\n\t}\n\t/**\n\t * (Re-)send event data immediately.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param clientId registered client id (-1 for registered synthesizer)\n\t * @param eventData event data which can be retrieved in SequencerClientCallback\n\t */\n\tpublic static sendEventNow(\n\t\tseq: ISequencer,\n\t\tclientId: number,\n\t\teventData: ISequencerEventData\n\t): void {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\tseq.sendEventNow(clientId, eventData);\n\t}\n\t/**\n\t * Set interval timer process to call processSequencer for this sequencer.\n\t * This method uses 'setInterval' global method to register timer.\n\t * @param seq the sequencer instance created by Synthesizer.createSequencer\n\t * @param msec time in milliseconds passed to both setInterval and processSequencer\n\t * @return return value of 'setInterval' (usually passing to 'clearInterval' will reset event)\n\t */\n\tpublic static setIntervalForSequencer(seq: ISequencer, msec: number) {\n\t\tif (!(seq instanceof Sequencer)) {\n\t\t\tthrow new TypeError(\"Invalid sequencer instance\");\n\t\t}\n\t\treturn seq.setIntervalForSequencer(msec);\n\t}\n}\n", "let _module: any;\nlet _ptrDefaultLogFunction: number | undefined;\nlet _disabledLoggingLevel: LogLevel | null = null;\nconst _handlers: Array<(level: LogLevel | null) => void> = [];\n\nconst LOG_LEVEL_COUNT = 5;\n/** Log level for libfluidsynth */\nconst LogLevel = {\n\tPanic: 0,\n\tError: 1,\n\tWarning: 2,\n\tInfo: 3,\n\tDebug: 4,\n} as const;\n/** Log level for libfluidsynth */\ntype LogLevel = (typeof LogLevel)[keyof typeof LogLevel];\nexport { LogLevel };\n\nfunction bindFunctions() {\n\tif (typeof AudioWorkletGlobalScope !== 'undefined') {\n\t\t_module = AudioWorkletGlobalScope.wasmModule;\n\t} else if (typeof Module !== 'undefined') {\n\t\t_module = Module;\n\t} else {\n\t\tthrow new Error(\n\t\t\t'wasm module is not available. libfluidsynth-*.js must be loaded.'\n\t\t);\n\t}\n}\n\n/**\n * Disable log output from libfluidsynth.\n * @param level disable log level (when `LogLevel.Warning` is specified, `Warning` `Info` `Debug` is disabled)\n * - If `null` is specified, log output feature is restored to the default.\n */\nexport function disableLogging(level: LogLevel | null = LogLevel.Panic): void {\n\tif (_disabledLoggingLevel === level) {\n\t\treturn;\n\t}\n\tbindFunctions();\n\tif (level == null) {\n\t\tif (_ptrDefaultLogFunction != null) {\n\t\t\t_module._fluid_set_log_function(0, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(1, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(2, _ptrDefaultLogFunction, 0);\n\t\t\t_module._fluid_set_log_function(3, _ptrDefaultLogFunction, 0);\n\t\t}\n\t\t_module._fluid_set_log_function(4, 0, 0);\n\t} else {\n\t\tlet ptr: number | undefined;\n\t\tfor (let l = level; l < LOG_LEVEL_COUNT; ++l) {\n\t\t\tconst p = _module._fluid_set_log_function(l, 0, 0);\n\t\t\tif (l !== LogLevel.Debug) {\n\t\t\t\tptr = p;\n\t\t\t}\n\t\t}\n\t\tif (ptr != null && _ptrDefaultLogFunction == null) {\n\t\t\t_ptrDefaultLogFunction = ptr;\n\t\t}\n\t}\n\t_disabledLoggingLevel = level;\n\tfor (const fn of _handlers) {\n\t\tfn(level);\n\t}\n}\n\n/**\n * Restores the log output from libfluidsynth. Same for calling `disableLogging(null)`.\n */\nexport function restoreLogging(): void {\n\tdisableLogging(null);\n}\n\n// @internal\nexport function getDisabledLoggingLevel(): LogLevel | null {\n\treturn _disabledLoggingLevel;\n}\n\n// @internal\nexport function addLoggingStatusChangedHandler(fn: (level: LogLevel | null) => void): void {\n\t_handlers.push(fn);\n}\n\n// @internal\nexport function removeLoggingStatusChangedHandler(fn: (level: LogLevel | null) => void): void {\n\tfor (let i = 0; i < _handlers.length; ++i) {\n\t\tif (_handlers[i] === fn) {\n\t\t\t_handlers.splice(i, 1);\n\t\t\treturn;\n\t\t}\n\t}\n}\n", "import Synthesizer from './Synthesizer';\n\n/**\n * Returns the Promise object which resolves when the synthesizer engine is ready.\n */\nexport default function waitForReady(): Promise<void> {\n\treturn Synthesizer.waitForWasmInitialized();\n}\n", "\nimport MessageError from './MessageError';\n\nexport interface MethodCallEventData {\n\tid: number;\n\tmethod: string;\n\targs: any[];\n}\n\nexport interface MethodReturnEventData {\n\tid: number;\n\tmethod: string;\n\tval: any;\n\terror?: MessageErrorData;\n}\n\nexport interface MessageErrorData {\n\tbaseName: string;\n\tmessage: string;\n\tdetail: any;\n}\n\n/** @internal */\nexport interface Defer<T> {\n\tresolve(value: T): void;\n\treject(reason: any): void;\n}\n\n/** @internal */\nexport interface DeferMap {\n\t[id: number]: Defer<any>;\n}\n\n/** @internal */\nexport type HookReturnMessageCallback = (data: MethodReturnEventData) => boolean;\n\n/** @internal */\nexport interface CallMessageInstance {\n\tport: MessagePort;\n\tdefers: DeferMap;\n\tdeferId: number;\n}\n\n/** @internal */\nexport function initializeCallPort(\n\tport: MessagePort,\n\thookMessage?: HookReturnMessageCallback | undefined\n): CallMessageInstance {\n\tconst instance: CallMessageInstance = {\n\t\tport: port,\n\t\tdefers: {},\n\t\tdeferId: 0\n\t};\n\tport.addEventListener('message', (e) => processReturnMessage(instance.defers, hookMessage, e));\n\tport.start();\n\treturn instance;\n}\n\nfunction convertErrorTransferable(err: Error): MessageErrorData {\n\tconst result: any = {};\n\tconst objList: any[] = [];\n\tlet obj: any = err;\n\twhile (obj && obj !== Object.prototype) {\n\t\tobjList.unshift(obj);\n\t\tobj = Object.getPrototypeOf(obj);\n\t}\n\tobjList.forEach((o) => {\n\t\tObject.getOwnPropertyNames(o).forEach((key) => {\n\t\t\ttry {\n\t\t\t\tconst data = (err as any)[key];\n\t\t\t\tif (typeof data !== 'function' && typeof data !== 'symbol') {\n\t\t\t\t\tresult[key] = data;\n\t\t\t\t}\n\t\t\t} catch (_e) { }\n\t\t});\n\t});\n\treturn {\n\t\tbaseName: err.name,\n\t\tmessage: err.message,\n\t\tdetail: result\n\t};\n}\n\nfunction convertAnyErrorTransferable(err: any): MessageErrorData {\n\treturn convertErrorTransferable((err && err instanceof Error) ? err : new Error(`${err}`));\n}\n\nfunction makeMessageError(error: MessageErrorData): MessageError {\n\treturn new MessageError(error.baseName, error.message, error.detail);\n}\n\nfunction processReturnMessage(defers: DeferMap, hook: HookReturnMessageCallback | undefined, e: MessageEvent) {\n\tconst data: MethodReturnEventData = e.data;\n\tif (!data) {\n\t\treturn;\n\t}\n\tif (hook && hook(data)) {\n\t\treturn;\n\t}\n\tconst defer = defers[data.id];\n\tif (defer) {\n\t\tdelete defers[data.id];\n\t\tif (data.error) {\n\t\t\tdefer.reject(makeMessageError(data.error));\n\t\t} else {\n\t\t\tdefer.resolve(data.val);\n\t\t}\n\t} else {\n\t\tif (data.error) {\n\t\t\tthrow makeMessageError(data.error);\n\t\t}\n\t}\n}\n\n/** @internal */\nexport function postCall(instance: CallMessageInstance, method: string, args: any[]): void;\n\n/** @internal */\nexport function postCall({ port }: CallMessageInstance, method: string, args: any[]) {\n\tport.postMessage({\n\t\tid: -1, method, args\n\t} as MethodCallEventData);\n}\n\n/** @internal */\nexport function postCallWithPromise<T>(instance: CallMessageInstance, method: string, args: any[]): Promise<T> {\n\tconst id = instance.deferId++;\n\tif (instance.deferId === Infinity || instance.deferId < 0) {\n\t\tinstance.deferId = 0;\n\t}\n\tconst promise = new Promise<T>((resolve, reject) => {\n\t\tinstance.defers[id] = { resolve, reject };\n\t});\n\tconst transfers: Transferable[] = [];\n\tif (args[0] instanceof MessagePort) {\n\t\ttransfers.push(args[0]);\n\t}\n\tinstance.port.postMessage({\n\t\tid, method, args\n\t} as MethodCallEventData, transfers);\n\treturn promise;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport type HookCallMessageCallback = (data: MethodCallEventData) => boolean;\n\n/** @internal */\nexport interface ReturnMessageInstance {\n\tport: MessagePort;\n}\n\n/** @internal */\nexport function initializeReturnPort(\n\tport: MessagePort,\n\tpromiseInitialized: Promise<void> | null,\n\ttargetObjectHolder: () => any,\n\thookMessage?: HookCallMessageCallback | undefined\n): ReturnMessageInstance {\n\tconst instance: ReturnMessageInstance = {\n\t\tport: port\n\t};\n\tif (promiseInitialized) {\n\t\tport.addEventListener('message', (e) => {\n\t\t\tconst data = e.data;\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tpromiseInitialized.then(() => processCallMessage(instance.port, data, targetObjectHolder, hookMessage));\n\t\t});\n\t} else {\n\t\tport.addEventListener('message', (e) => {\n\t\t\tconst data = e.data;\n\t\t\tif (!data) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tprocessCallMessage(instance.port, data, targetObjectHolder, hookMessage);\n\t\t});\n\t}\n\tport.start();\n\treturn instance;\n}\n\nfunction processCallMessage(\n\tport: MessagePort,\n\tdata: MethodCallEventData,\n\ttargetObjectHolder: () => any,\n\thook?: HookCallMessageCallback | undefined\n) {\n\tif (hook && hook(data)) {\n\t\treturn;\n\t}\n\tconst target = targetObjectHolder();\n\tif (!target[data.method]) {\n\t\tpostReturnErrorImpl(port, data.id, data.method, new Error('Not implemented'));\n\t} else {\n\t\ttry {\n\t\t\tpostReturnImpl(port, data.id, data.method, target[data.method].apply(target, data.args));\n\t\t} catch (e) {\n\t\t\tpostReturnErrorImpl(port, data.id, data.method, e);\n\t\t}\n\t}\n}\n\n/** @internal */\nexport function postReturn(instance: ReturnMessageInstance, id: number, method: string, value: any) {\n\tpostReturnImpl(instance.port, id, method, value);\n}\n\nfunction postReturnImpl(port: MessagePort, id: number, method: string, value: any) {\n\tif (value instanceof Promise) {\n\t\tvalue.then((v) => {\n\t\t\tif (id >= 0) {\n\t\t\t\tport.postMessage({\n\t\t\t\t\tid,\n\t\t\t\t\tmethod,\n\t\t\t\t\tval: v\n\t\t\t\t} as MethodReturnEventData);\n\t\t\t}\n\t\t}, (error) => {\n\t\t\tport.postMessage({\n\t\t\t\tid,\n\t\t\t\tmethod,\n\t\t\t\terror: convertAnyErrorTransferable(error)\n\t\t\t} as MethodReturnEventData);\n\t\t});\n\t} else {\n\t\tport.postMessage({\n\t\t\tid,\n\t\t\tmethod,\n\t\t\tval: value\n\t\t} as MethodReturnEventData);\n\t}\n}\n\n/** @internal */\nexport function postReturnError(instance: ReturnMessageInstance, id: number, method: string, error: any) {\n\tpostReturnErrorImpl(instance.port, id, method, error);\n}\n\nfunction postReturnErrorImpl(port: MessagePort, id: number, method: string, error: any) {\n\tport.postMessage({\n\t\tid,\n\t\tmethod,\n\t\terror: convertAnyErrorTransferable(error)\n\t} as MethodReturnEventData);\n}\n", "import Preset from './Preset';\n\nimport * as MethodMessaging from './MethodMessaging';\n\nexport default class WorkletSoundfont {\n\t// @internal\n\tprivate _messaging: MethodMessaging.CallMessageInstance;\n\n\t// @internal\n\tpublic constructor(port: MessagePort, private readonly name: string) {\n\t\tthis._messaging = MethodMessaging.initializeCallPort(port);\n\t}\n\n\tpublic getName(): string {\n\t\treturn this.name;\n\t}\n\n\tpublic getPreset(bank: number, presetNum: number): Promise<Preset | null> {\n\t\treturn MethodMessaging.postCallWithPromise(this._messaging, 'getPreset', [bank, presetNum]);\n\t}\n\n\tpublic getPresetIterable(): Promise<Iterable<Preset>> {\n\t\treturn MethodMessaging.postCallWithPromise<Preset[]>(this._messaging, 'getPresetIterable', []);\n\t}\n}\n", "\nimport ISequencer, { ClientInfo } from './ISequencer';\nimport ISynthesizer from './ISynthesizer';\nimport Sequencer<PERSON><PERSON> from './SequencerEvent';\n\nimport AudioWorkletNodeSynthesizer from './AudioWorkletNodeSynthesizer';\n\nimport * as MethodMessaging from './MethodMessaging';\n\n/** @internal */\nexport default class WorkletSequencer implements ISequencer {\n\t/** @internal */\n\tprivate _messaging: MethodMessaging.CallMessageInstance | null;\n\n\tconstructor(port: MessagePort) {\n\t\tthis._messaging = MethodMessaging.initializeCallPort(port);\n\t}\n\n\t/** @internal */\n\tpublic getRaw(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getRaw', []);\n\t}\n\t/** @internal */\n\tpublic registerSequencerClientByName(clientName: string, callbackName: string, param: number): Promise<number> {\n\t\treturn this.getRaw().then((seqPtr) => MethodMessaging.postCallWithPromise<number>(\n\t\t\tthis._messaging!,\n\t\t\t'registerSequencerClientByName',\n\t\t\t[seqPtr, clientName, callbackName, param]\n\t\t));\n\t}\n\n\tpublic close(): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'close', []);\n\t}\n\tpublic registerSynthesizer(synth: ISynthesizer | number): Promise<number> {\n\t\tlet val: Promise<number>;\n\t\tif (synth instanceof AudioWorkletNodeSynthesizer) {\n\t\t\tval = synth._getRawSynthesizer();\n\t\t} else {\n\t\t\treturn Promise.reject(new TypeError('\\'synth\\' is not a compatible type instance'));\n\t\t}\n\t\treturn val.then((v) => MethodMessaging.postCallWithPromise<number>(this._messaging!, 'registerSynthesizer', [v]));\n\t}\n\tpublic unregisterClient(clientId: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'unregisterClient', [clientId]);\n\t}\n\tpublic getAllRegisteredClients(): Promise<ClientInfo[]> {\n\t\treturn MethodMessaging.postCallWithPromise<ClientInfo[]>(this._messaging!, 'getAllRegisteredClients', []);\n\t}\n\tpublic getClientCount(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getClientCount', []);\n\t}\n\tpublic getClientInfo(index: number): Promise<ClientInfo> {\n\t\treturn MethodMessaging.postCallWithPromise<ClientInfo>(this._messaging!, 'getClientInfo', [index]);\n\t}\n\tpublic setTimeScale(scale: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'setTimeScale', [scale]);\n\t}\n\tpublic getTimeScale(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getTimeScale', []);\n\t}\n\tpublic getTick(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getTick', []);\n\t}\n\tpublic sendEventAt(event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'sendEventAt', [event, tick, isAbsolute]);\n\t}\n\tpublic sendEventToClientAt(clientId: number, event: SequencerEvent, tick: number, isAbsolute: boolean): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'sendEventToClientAt', [clientId, event, tick, isAbsolute]);\n\t}\n\tpublic removeAllEvents(): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'removeAllEvents', []);\n\t}\n\tpublic removeAllEventsFromClient(clientId: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'removeAllEventsFromClient', [clientId]);\n\t}\n\n\tpublic processSequencer(msecToProcess: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'processSequencer', [msecToProcess]);\n\t}\n}\n", "\nimport { SynthesizerDefaultValues, InterpolationValues, PlayerSetTempoType } from './Constants';\nimport ISequencer from './ISequencer';\nimport ISynthesizer from './ISynthesizer';\nimport SynthesizerSettings from './SynthesizerSettings';\nimport WorkletSoundfont from './WorkletSoundfont';\nimport WorkletSequencer from './WorkletSequencer';\nimport * as MethodMessaging from './MethodMessaging';\nimport { addLoggingStatusChangedHandler, getDisabledLoggingLevel, LogLevel } from './logging';\n\n/** @internal */\nexport const enum Constants {\n\tProcessorName = 'fluid-js',\n\tUpdateStatus = 'updateStatus',\n}\n/** @internal */\nexport interface SynthesizerStatus {\n\tplaying: boolean;\n\tplayerPlaying: boolean;\n}\n/** @internal */\nexport interface ProcessorOptions {\n\tsettings?: SynthesizerSettings;\n\tdisabledLoggingLevel?: LogLevel | null;\n}\n\n/** An synthesizer object with AudioWorkletNode */\nexport default class AudioWorkletNodeSynthesizer implements ISynthesizer {\n\n\t/** @internal */\n\tprivate _status: SynthesizerStatus;\n\t/** @internal */\n\tprivate _messaging: MethodMessaging.CallMessageInstance | null;\n\t/** @internal */\n\tprivate _node: AudioWorkletNode | null;\n\t/** @internal */\n\tprivate _gain: number;\n\n\t/** @internal */\n\tprivate handleLoggingChanged: (level: LogLevel | null) => void;\n\n\tconstructor() {\n\t\tthis._status = {\n\t\t\tplaying: false,\n\t\t\tplayerPlaying: false\n\t\t};\n\t\tthis._messaging = null;\n\t\tthis._node = null;\n\t\tthis._gain = SynthesizerDefaultValues.Gain;\n\t\tthis.handleLoggingChanged = this._handleLoggingChanged.bind(this);\n\t\taddLoggingStatusChangedHandler(this.handleLoggingChanged);\n\t}\n\n\t/** Audio node for this synthesizer */\n\tpublic get node(): AudioWorkletNode | null {\n\t\treturn this._node;\n\t}\n\n\t/**\n\t * Create AudiWorkletNode instance\n\t */\n\tpublic createAudioNode(context: AudioContext, settings?: SynthesizerSettings) {\n\t\tconst processorOptions: ProcessorOptions = {\n\t\t\tsettings: settings,\n\t\t\tdisabledLoggingLevel: getDisabledLoggingLevel(),\n\t\t};\n\t\tconst node = new AudioWorkletNode(context, Constants.ProcessorName, {\n\t\t\tnumberOfInputs: 0,\n\t\t\tnumberOfOutputs: 1,\n\t\t\tchannelCount: 2,\n\t\t\toutputChannelCount: [2],\n\t\t\tprocessorOptions: processorOptions,\n\t\t});\n\t\tthis._node = node;\n\n\t\tthis._messaging = MethodMessaging.initializeCallPort(node.port, (data) => {\n\t\t\tif (data.method === Constants.UpdateStatus) {\n\t\t\t\tthis._status = data.val;\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t});\n\t\treturn node;\n\t}\n\n\tpublic isInitialized() {\n\t\treturn this._messaging !== null;\n\t}\n\n\tpublic init(_sampleRate: number, _settings?: SynthesizerSettings) {\n\t}\n\n\tpublic close() {\n\t\t// call init instead of close\n\t\tMethodMessaging.postCall(this._messaging!, 'init', [0]);\n\t}\n\n\tpublic isPlaying() {\n\t\treturn this._status.playing;\n\t}\n\n\tpublic setInterpolation(value: InterpolationValues, channel?: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'setInterpolation', [value, channel]);\n\t}\n\n\tpublic getGain() {\n\t\treturn this._gain;\n\t}\n\n\tpublic setGain(gain: number) {\n\t\tthis._gain = gain;\n\t\tMethodMessaging.postCallWithPromise<void>(this._messaging!, 'setGain', [gain]).then(() => {\n\t\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getGain', []);\n\t\t}).then((value) => {\n\t\t\tthis._gain = value;\n\t\t});\n\t}\n\n\tpublic setChannelType(channel: number, isDrum: boolean) {\n\t\tMethodMessaging.postCall(this._messaging!, 'setChannelType', [channel, isDrum]);\n\t}\n\n\tpublic waitForVoicesStopped() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'waitForVoicesStopped', []);\n\t}\n\n\tpublic loadSFont(bin: ArrayBuffer) {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'loadSFont', [bin]);\n\t}\n\n\tpublic unloadSFont(id: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'unloadSFont', [id]);\n\t}\n\n\tpublic unloadSFontAsync(id: number) {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'unloadSFont', [id]);\n\t}\n\n\t/**\n\t * Returns the `Soundfont` instance for specified SoundFont.\n\t * @param sfontId loaded SoundFont id ({@link loadSFont} returns this)\n\t * @return resolve with `Soundfont` instance (rejected if `sfontId` is not valid or loaded)\n\t */\n\tpublic getSFontObject(sfontId: number): Promise<WorkletSoundfont> {\n\t\tconst channel = new MessageChannel();\n\t\treturn MethodMessaging.postCallWithPromise<string>(this._messaging!, 'getSFontObject', [channel.port2, sfontId]).then((name) => {\n\t\t\treturn new WorkletSoundfont(channel.port1, name);\n\t\t});\n\t}\n\n\tpublic getSFontBankOffset(id: number) {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getSFontBankOffset', [id]);\n\t}\n\tpublic setSFontBankOffset(id: number, offset: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'setSFontBankOffset', [id, offset]);\n\t}\n\n\tpublic render() {\n\t\tthrow new Error('Unexpected call');\n\t}\n\n\tpublic midiNoteOn(chan: number, key: number, vel: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiNoteOn', [chan, key, vel]);\n\t}\n\tpublic midiNoteOff(chan: number, key: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiNoteOff', [chan, key]);\n\t}\n\tpublic midiKeyPressure(chan: number, key: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiKeyPressure', [chan, key, val]);\n\t}\n\tpublic midiControl(chan: number, ctrl: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiControl', [chan, ctrl, val]);\n\t}\n\tpublic midiProgramChange(chan: number, prognum: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiProgramChange', [chan, prognum]);\n\t}\n\tpublic midiChannelPressure(chan: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiChannelPressure', [chan, val]);\n\t}\n\tpublic midiPitchBend(chan: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiPitchBend', [chan, val]);\n\t}\n\tpublic midiSysEx(data: Uint8Array) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSysEx', [data]);\n\t}\n\n\tpublic midiPitchWheelSensitivity(chan: number, val: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiPitchWheelSensitivity', [chan, val]);\n\t}\n\tpublic midiBankSelect(chan: number, bank: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiBankSelect', [chan, bank]);\n\t}\n\tpublic midiSFontSelect(chan: number, sfontId: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSFontSelect', [chan, sfontId]);\n\t}\n\tpublic midiProgramSelect(chan: number, sfontId: number, bank: number, presetNum: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiProgramSelect', [chan, sfontId, bank, presetNum]);\n\t}\n\tpublic midiUnsetProgram(chan: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiUnsetProgram', [chan]);\n\t}\n\tpublic midiProgramReset() {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiProgramReset', []);\n\t}\n\tpublic midiSystemReset() {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSystemReset', []);\n\t}\n\tpublic midiAllNotesOff(chan?: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiAllNotesOff', [chan]);\n\t}\n\tpublic midiAllSoundsOff(chan?: number) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiAllSoundsOff', [chan]);\n\t}\n\tpublic midiSetChannelType(chan: number, isDrum: boolean) {\n\t\tMethodMessaging.postCall(this._messaging!, 'midiSetChannelType', [chan, isDrum]);\n\t}\n\n\tpublic resetPlayer() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'resetPlayer', []);\n\t}\n\n\tpublic closePlayer() {\n\t\tMethodMessaging.postCall(this._messaging!, 'closePlayer', []);\n\t}\n\n\tpublic isPlayerPlaying() {\n\t\treturn this._status.playerPlaying;\n\t}\n\n\tpublic addSMFDataToPlayer(bin: ArrayBuffer) {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'addSMFDataToPlayer', [bin]);\n\t}\n\n\tpublic playPlayer() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'playPlayer', []);\n\t}\n\n\tpublic stopPlayer() {\n\t\tMethodMessaging.postCall(this._messaging!, 'stopPlayer', []);\n\t}\n\n\tpublic retrievePlayerCurrentTick(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerCurrentTick', []);\n\t}\n\tpublic retrievePlayerTotalTicks(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerTotalTicks', []);\n\t}\n\tpublic retrievePlayerBpm(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerBpm', []);\n\t}\n\tpublic retrievePlayerMIDITempo(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'retrievePlayerMIDITempo', []);\n\t}\n\tpublic seekPlayer(ticks: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'seekPlayer', [ticks]);\n\t}\n\tpublic setPlayerLoop(loopTimes: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'setPlayerLoop', [loopTimes]);\n\t}\n\tpublic setPlayerTempo(tempoType: PlayerSetTempoType, tempo: number): void {\n\t\tMethodMessaging.postCall(this._messaging!, 'setPlayerTempo', [tempoType, tempo]);\n\t}\n\n\tpublic waitForPlayerStopped() {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'waitForPlayerStopped', []);\n\t}\n\n\t/**\n\t * Creates a sequencer instance associated with this worklet node.\n\t */\n\tpublic createSequencer(): Promise<ISequencer> {\n\t\tconst channel = new MessageChannel();\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'createSequencer', [channel.port2]).then(() => {\n\t\t\treturn new WorkletSequencer(channel.port1);\n\t\t});\n\t}\n\n\t/**\n\t * Hooks MIDI events sent by the player. The hook callback function defined on\n\t * AudioWorkletGlobalScope object available in the worklet is used.\n\t * @param callbackName hook callback function name available as 'AudioWorkletGlobalScope[callbackName]',\n\t *     or falsy value ('', null, or undefined) to unhook.\n\t *     The type of 'AudioWorkletGlobalScope[callbackName]' must be HookMIDIEventCallback.\n\t * @param param any additional data passed to the callback.\n\t *     This data must be 'Transferable' data.\n\t * @return Promise object that resolves when succeeded, or rejects when failed\n\t */\n\tpublic hookPlayerMIDIEventsByName(callbackName: string | null | undefined, param?: any): Promise<void> {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'hookPlayerMIDIEventsByName', [callbackName, param]);\n\t}\n\n\t/**\n\t * Registers the user-defined client to the sequencer.\n\t * The client callback function defined on AudioWorkletGlobalScope\n\t * object available in the worklet is used.\n\t * The client can receive events in the time from sequencer process.\n\t * @param seq the sequencer instance created by AudioWorkletNodeSynthesizer.createSequencer\n\t * @param clientName the client name\n\t * @param callbackName callback function name available as 'AudioWorkletGlobalScope[callbackName]',\n\t *     or falsy value ('', null, or undefined) to unhook.\n\t *     The type of 'AudioWorkletGlobalScope[callbackName]' must be SequencerClientCallback.\n\t * @param param additional parameter passed to the callback\n\t * @return Promise object that resolves with registered client id when succeeded, or rejects when failed\n\t */\n\tpublic registerSequencerClientByName(seq: ISequencer, clientName: string, callbackName: string, param: number): Promise<number> {\n\t\tif (!(seq instanceof WorkletSequencer)) {\n\t\t\treturn Promise.reject(new TypeError('Invalid sequencer object'));\n\t\t}\n\t\treturn seq.registerSequencerClientByName(clientName, callbackName, param);\n\t}\n\n\t/**\n\t * Call a function defined in the AudioWorklet.\n\t *\n\t * The function will receive two parameters; the first parameter is a Synthesizer instance\n\t * (not AudioWorkletNodeSynthesizer instance), and the second is the data passed to 'param'.\n\t * This method is useful when the script loaded in AudioWorklet wants to\n\t * retrieve Synthesizer instance.\n\t *\n\t * @param name a function name (must be retrieved from AudioWorkletGlobalScope[name])\n\t * @param param any parameter (must be Transferable)\n\t * @return Promise object that resolves when the function process has done, or rejects when failed\n\t */\n\tpublic callFunction(name: string, param: any) {\n\t\treturn MethodMessaging.postCallWithPromise<void>(this._messaging!, 'callFunction', [name, param]);\n\t}\n\n\t/** @internal */\n\tpublic _getRawSynthesizer(): Promise<number> {\n\t\treturn MethodMessaging.postCallWithPromise<number>(this._messaging!, 'getRawSynthesizer', []);\n\t}\n\n\t/** @internal */\n\tprivate _handleLoggingChanged(level: LogLevel | null) {\n\t\tif (this._messaging == null) {\n\t\t\treturn;\n\t\t}\n\t\tMethodMessaging.postCall(this._messaging, 'loggingChanged', [level]);\n\t}\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "PlayerSetTempoType", "Internal", "ExternalBpm", "<PERSON><PERSON><PERSON><PERSON>", "INVALID_POINTER", "SequencerEventData", "constructor", "_ptr", "_module", "getRaw", "dispose", "getType", "_fluid_event_get_type", "getSource", "_fluid_event_get_source", "getDest", "_fluid_event_get_dest", "getChannel", "_fluid_event_get_channel", "<PERSON><PERSON><PERSON>", "_fluid_event_get_key", "getVelocity", "_fluid_event_get_velocity", "getControl", "_fluid_event_get_control", "getValue", "_fluid_event_get_value", "getProgram", "_fluid_event_get_program", "getData", "_fluid_event_get_data", "getDuration", "_fluid_event_get_duration", "getBank", "_fluid_event_get_bank", "get<PERSON><PERSON>", "_fluid_event_get_pitch", "getSFontId", "_fluid_event_get_sfont_id", "AudioWorkletGlobalScope", "wasmModule", "<PERSON><PERSON><PERSON>", "rewriteEventDataImpl", "ev", "event", "type", "_fluid_event_note", "channel", "vel", "duration", "_fluid_event_noteon", "_fluid_event_noteoff", "_fluid_event_all_sounds_off", "_fluid_event_all_notes_off", "_fluid_event_bank_select", "bank", "_fluid_event_program_change", "preset", "_fluid_event_program_select", "sfontId", "_fluid_event_control_change", "control", "_fluid_event_pitch_bend", "_fluid_event_pitch_wheelsens", "_fluid_event_modulation", "_fluid_event_sustain", "_fluid_event_pan", "_fluid_event_volume", "_fluid_event_reverb_send", "_fluid_event_chorus_send", "_fluid_event_key_pressure", "_fluid_event_channel_pressure", "_fluid_event_system_reset", "_fluid_event_timer", "data", "rewriteEventData", "MessageError", "Error", "baseName", "message", "detail", "super", "stack", "MIDIEvent", "_fluid_midi_event_get_type", "setType", "_fluid_midi_event_set_type", "_fluid_midi_event_get_channel", "setChannel", "_fluid_midi_event_set_channel", "_fluid_midi_event_get_key", "<PERSON><PERSON><PERSON>", "_fluid_midi_event_set_key", "_fluid_midi_event_get_velocity", "setVelocity", "_fluid_midi_event_set_velocity", "_fluid_midi_event_get_control", "setControl", "_fluid_midi_event_set_control", "_fluid_midi_event_get_value", "setValue", "_fluid_midi_event_set_value", "_fluid_midi_event_get_program", "setProgram", "_fluid_midi_event_set_program", "_fluid_midi_event_get_pitch", "<PERSON><PERSON><PERSON>", "_fluid_midi_event_set_pitch", "setSysEx", "size", "byteLength", "ptr", "_malloc", "Uint8Array", "HEAPU8", "buffer", "set", "_fluid_midi_event_set_sysex", "setText", "_fluid_midi_event_set_text", "setLyrics", "_fluid_midi_event_set_lyrics", "_removeFunction", "fluid_sequencer_get_client_name", "fluid_sfont_get_name", "fluid_preset_get_name", "_addFunction", "_fs", "fluid_settings_setint", "fluid_settings_setnum", "fluid_settings_setstr", "fluid_synth_error", "fluid_synth_sfload", "fluid_sequencer_register_client", "malloc", "free", "defaultMIDIEventCallback", "promiseWaitForInitialized", "_ptrDefaultLogFunction", "makeEvent", "_new_fluid_event", "_delete_fluid_event", "Sequencer", "wasmRemoveFunction", "removeFunction", "cwrap", "_seq", "_seqId", "_clientFuncMap", "_initialize", "close", "_new_fluid_sequencer2", "Promise", "resolve", "keys", "for<PERSON>ach", "clientIdStr", "unregisterClient", "Number", "_delete_fluid_sequencer", "registerSynthesizer", "synth", "val", "_fluid_sequencer_unregister_client", "Synthesizer", "reject", "TypeError", "getRawSynthesizer", "_fluid_sequencer_register_fluidsynth", "clientId", "_fluid_event_set_source", "_fluid_event_set_dest", "_fluid_event_unregistering", "_fluid_sequencer_send_now", "map", "getAllRegisteredClients", "c", "_fluid_sequencer_count_clients", "r", "i", "id", "_fluid_sequencer_get_client_id", "name", "push", "getClientCount", "getClientInfo", "index", "setTimeScale", "scale", "_fluid_sequencer_set_time_scale", "getTimeScale", "_fluid_sequencer_get_time_scale", "getTick", "_fluid_sequencer_get_tick", "sendEventAt", "tick", "isAbsolute", "count", "_fluid_sequencer_send_at", "sendEventToClientAt", "sendEventToClientNow", "sendEventNow", "eventData", "removeAllEvents", "_fluid_sequencer_remove_events", "removeAllEventsFromClient", "processSequencer", "msecToProcess", "_fluid_sequencer_process", "setIntervalForSequencer", "msec", "setInterval", "Soundfont", "sfontPtr", "getSoundfontById", "sfont", "_fluid_synth_get_sfont_by_id", "getName", "getPreset", "presetNum", "presetPtr", "_fluid_sfont_get_preset", "soundfont", "bankNum", "_fluid_preset_get_banknum", "num", "_fluid_preset_get_num", "getPresetIterable", "reset", "_fluid_sfont_iteration_start", "next", "_fluid_sfont_iteration_next", "done", "undefined", "iterator", "wasmAddFunction", "addFunction", "FS", "bind", "_free", "_fluid_synth_handle_midi_event", "setBoolValueForSettings", "settings", "setIntValueForSettings", "setNumValueForSettings", "_settings", "_synth", "_player", "_playerPlaying", "_playerCallbackPtr", "_fluidSynthCallback", "_buffer", "_bufferSize", "_numPtr", "_gain", "waitForWasmInitialized", "mod", "addOnPostRunFn", "addOnPostRun", "calledRun", "fn", "onRuntimeInitialized", "waitForInitialized", "isInitialized", "createAudioNode", "context", "frameSize", "node", "createScriptProcessor", "addEventListener", "render", "outputBuffer", "init", "sampleRate", "_new_fluid_settings", "initialGain", "chorusActive", "chorusD<PERSON>h", "chorusLevel", "chorusNr", "chorusSpeed", "midiChannelCount", "setStrValueForSettings", "midiBankSelect", "minNote<PERSON>ength", "overflowAge", "overflowImportantValue", "overflowImportantChannels", "join", "overflowPercussion", "overflowReleased", "overflowSustained", "overflowVolume", "polyphony", "reverbActive", "reverbDamp", "reverbLevel", "reverbRoomSize", "reverbWidth", "_new_fluid_synth", "_closePlayer", "_delete_fluid_synth", "_delete_fluid_settings", "isPlaying", "actualCount", "_fluid_synth_get_active_voice_count", "baseOffsetOfVoice", "offsetOfActiveVoiceCount", "structActiveVoiceCount", "HEAPU32", "console", "warn", "voiceList", "voiceCount", "_fluid_synth_get_polyphony", "isRunning", "voice", "getActiveVoiceCount", "setInterpolation", "ensureInitialized", "_fluid_synth_set_interp_method", "get<PERSON>ain", "setGain", "gain", "_fluid_synth_set_gain", "_fluid_synth_get_gain", "setChannelType", "isDrum", "_fluid_synth_set_channel_type", "waitForVoicesStopped", "flushFramesAsync", "loadSFont", "bin", "ext", "Math", "random", "ub", "writeFile", "unlink", "unloadSFont", "stopPlayer", "flushFramesSync", "_fluid_synth_sfunload", "unloadSFontAsync", "then", "getSFontObject", "getSFontBankOffset", "_fluid_synth_get_bank_offset", "setSFontBankOffset", "offset", "_fluid_synth_set_bank_offset", "outBuffer", "frameCount", "length", "channels", "numberOfChannels", "sizePerChannel", "totalSize", "memLeft", "memRight", "renderRaw", "aLeft", "Float32Array", "aRight", "copyToChannel", "leftData", "getChannelData", "rightData", "isPlayerPlaying", "midiNoteOn", "chan", "_fluid_synth_noteon", "midiNoteOff", "_fluid_synth_noteoff", "midiKeyPressure", "_fluid_synth_key_pressure", "midiControl", "ctrl", "_fluid_synth_cc", "midiProgramChange", "prognum", "_fluid_synth_program_change", "midiChannelPressure", "_fluid_synth_channel_pressure", "midiPitchBend", "_fluid_synth_pitch_bend", "midiSysEx", "len", "mem", "_fluid_synth_sysex", "midiPitchWheelSensitivity", "_fluid_synth_pitch_wheel_sens", "_fluid_synth_bank_select", "midiSFontSelect", "_fluid_synth_sfont_select", "midiProgramSelect", "_fluid_synth_program_select", "midiUnsetProgram", "_fluid_synth_unset_program", "midiProgramReset", "_fluid_synth_program_reset", "midiSystemReset", "_fluid_synth_system_reset", "midiAllNotesOff", "_fluid_synth_all_notes_off", "midiAllSoundsOff", "_fluid_synth_all_sounds_off", "midiSetChannelType", "setReverb", "roomsize", "damping", "width", "level", "_fluid_synth_set_reverb", "setReverbRoomsize", "_fluid_synth_set_reverb_roomsize", "setReverbDamp", "_fluid_synth_set_reverb_damp", "setReverbWidth", "_fluid_synth_set_reverb_width", "setReverbLevel", "_fluid_synth_set_reverb_level", "setReverbOn", "on", "_fluid_synth_set_reverb_on", "getReverbRoomsize", "_fluid_synth_get_reverb_roomsize", "getReverbDamp", "_fluid_synth_get_reverb_damp", "getReverbLevel", "_fluid_synth_get_reverb_level", "getReverbWidth", "_fluid_synth_get_reverb_width", "<PERSON><PERSON><PERSON><PERSON>", "speed", "depthMillisec", "_fluid_synth_set_chorus", "setChorusVoiceCount", "_fluid_synth_set_chorus_nr", "setChorusLevel", "_fluid_synth_set_chorus_level", "setChorusSpeed", "_fluid_synth_set_chorus_speed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_fluid_synth_set_chorus_depth", "setChorusType", "_fluid_synth_set_chorus_type", "setChorusOn", "_fluid_synth_set_chorus_on", "getChorusVoiceCount", "_fluid_synth_get_chorus_nr", "getChorusLevel", "_fluid_synth_get_chorus_level", "getChorusSpeed", "_fluid_synth_get_chorus_speed", "getChorusDepth", "_fluid_synth_get_chorus_depth", "getChorusType", "_fluid_synth_get_chorus_type", "getGenerator", "param", "_fluid_synth_get_gen", "setGenerator", "_fluid_synth_set_gen", "getLegatoMode", "_fluid_synth_get_legato_mode", "HEAP32", "setLegatoMode", "mode", "_fluid_synth_set_legato_mode", "getPortamentoMode", "_fluid_synth_get_portamento_mode", "setPortamentoMode", "_fluid_synth_set_portamento_mode", "getBreathMode", "_fluid_synth_get_breath_mode", "setBreathMode", "flags", "_fluid_synth_set_breath_mode", "resetPlayer", "_initPlayer", "closePlayer", "player", "_new_fluid_player", "funcPtr", "p", "_delete_fluid_player", "_fluid_player_get_status", "addSMFDataToPlayer", "ensurePlayerInitialized", "_fluid_player_add_mem", "playPlayer", "_fluid_player_play", "resolver", "_player<PERSON><PERSON><PERSON>", "promise", "_fluid_player_stop", "_fluid_player_join", "retrievePlayerCurrentTick", "_fluid_player_get_current_tick", "retrievePlayerTotalTicks", "_fluid_player_get_total_ticks", "retrievePlayerBpm", "_fluid_player_get_bpm", "retrievePlayerMIDITempo", "_fluid_player_get_midi_tempo", "seekPlayer", "ticks", "_fluid_player_seek", "setPlayerLoop", "loopTimes", "_fluid_player_set_loop", "setPlayerTempo", "tempoType", "tempo", "_fluid_player_set_tempo", "hookPlayerMIDIEvents", "callback", "oldPtr", "newPtr", "cb", "t", "makeMIDIEventCallback", "_fluid_player_set_playback_callback", "_fluid_synth_write_float", "next<PERSON><PERSON><PERSON>", "setTimeout", "head", "tail", "self", "waitForPlayerStopped", "createSequencer", "seq", "registerSequencerClient", "time", "e", "waitFor<PERSON><PERSON>y", "initializeCallPort", "port", "hookMessage", "instance", "defers", "deferId", "hook", "defer", "error", "makeMessageError", "processReturnMessage", "start", "postCall", "method", "args", "postMessage", "postCallWithPromise", "Infinity", "transfers", "MessagePort", "WorkletSoundfont", "_messaging", "WorkletSequencer", "registerSequencerClientByName", "clientName", "callback<PERSON><PERSON>", "seqPtr", "AudioWorkletNodeSynthesizer", "_getRawSynthesizer", "v", "_disabledLoggingLevel", "_handlers", "LOG_LEVEL_COUNT", "LogLevel", "Panic", "Warning", "Info", "Debug", "disableLogging", "_fluid_set_log_function", "l", "restoreLogging", "_status", "playing", "playerPlaying", "_node", "handleLoggingChanged", "_handleLoggingChanged", "AudioWorkletNode", "numberOfInputs", "numberOfOutputs", "channelCount", "outputChannelCount", "processorOptions", "disabledLoggingLevel", "_sampleRate", "MessageChannel", "port2", "port1", "hookPlayerMIDIEventsByName", "callFunction"], "sourceRoot": ""}