{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,

    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": false,
    
    /* Path aliases */
    "baseUrl": ".",
    "paths": {
      "@src/*": ["../old_code/*"],
      "@core/*": ["../old_code/core/*"],
      "@components/*": ["../old_code/components/*"],
      "@constants/*": ["../old_code/constants/*"],
      "@utils/*": ["../old_code/utils/*"],
      "@api/*": ["../old_code/api/*"]
    }
  },
  "include": ["vite.config.ts"]
}