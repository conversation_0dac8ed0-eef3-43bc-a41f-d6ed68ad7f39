{"name": "studio-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.6", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/supabase-js": "^2.49.3", "@tabler/icons-react": "^3.31.0", "@tailwindcss/vite": "^4.1.6", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "@tanstack/react-router": "^1.114.27", "@tanstack/router-devtools": "^1.114.27", "@tanstack/router-vite-plugin": "^1.114.27", "@tonejs/midi": "^2.0.28", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dexie": "^4.0.11", "event-source-polyfill": "^1.0.31", "immer": "^10.1.1", "js-synthesizer": "^1.10.0", "konva": "^9.3.20", "lucide-react": "^0.510.0", "posthog-js": "^1.257.0", "primeicons": "^7.0.0", "primereact": "^10.9.3", "react": "19.0.0", "react-dom": "19.0.0", "react-konva": "^19.0.3", "react-markdown": "^10.1.0", "react-rnd": "^10.5.2", "react-router-dom": "^7.4.0", "spessasynth_lib": "^3.25.3", "styled-components": "^6.1.16", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "tone": "^15.0.4", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.3.0", "@types/event-source-polyfill": "^1.0.5", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "http-proxy-middleware": "^3.0.3", "jsdom": "^23.0.1", "prettier": "3.5.3", "tw-animate-css": "^1.2.9", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vitest": "^1.6.1"}}