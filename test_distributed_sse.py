#!/usr/bin/env python3
"""
Test script to verify distributed SSE system works across multiple server instances.

This script:
1. Starts 2 backend servers on different ports
2. Creates a request on Server A
3. Connects to SSE stream on Server B 
4. Verifies events flow from A to B via Redis

Usage: python test_distributed_sse.py
"""

import asyncio
import subprocess
import time
import requests
import json
import sys
from typing import Optional
import signal
import os

class DistributedSSETest:
    def __init__(self):
        self.server_processes = []
        self.server_a_port = 8000
        self.server_b_port = 8001
        self.base_dir = "/Users/<USER>/Developer/beatgen/backend"
        
    def start_servers(self):
        """Start two backend servers on different ports"""
        print("🚀 Starting Server A on port 8000...")
        
        # Server A environment
        env_a = os.environ.copy()
        env_a.update({
            "USE_DISTRIBUTED_REQUESTS": "true",
            "PORT": "8000"
        })
        
        process_a = subprocess.Popen([
            "uvicorn", "app2.main:app", 
            "--host", "127.0.0.1",
            "--port", str(self.server_a_port),
            "--reload"
        ], cwd=self.base_dir, env=env_a)
        
        self.server_processes.append(process_a)
        
        print("🚀 Starting Server B on port 8001...")
        
        # Server B environment  
        env_b = os.environ.copy()
        env_b.update({
            "USE_DISTRIBUTED_REQUESTS": "true",
            "PORT": "8001"
        })
        
        process_b = subprocess.Popen([
            "uvicorn", "app2.main:app",
            "--host", "127.0.0.1", 
            "--port", str(self.server_b_port),
            "--reload"
        ], cwd=self.base_dir, env=env_b)
        
        self.server_processes.append(process_b)
        
        # Wait for servers to start
        print("⏳ Waiting for servers to start...")
        self.wait_for_servers()
        
    def wait_for_servers(self):
        """Wait for both servers to be responsive"""
        for port in [self.server_a_port, self.server_b_port]:
            while True:
                try:
                    response = requests.get(f"http://127.0.0.1:{port}/health", timeout=2)
                    if response.status_code == 200:
                        print(f"✅ Server on port {port} is ready")
                        break
                except:
                    print(f"⏳ Waiting for server on port {port}...")
                    time.sleep(1)
    
    def cleanup_servers(self):
        """Stop all server processes"""
        print("🛑 Stopping servers...")
        for process in self.server_processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        self.server_processes = []
        
    def get_auth_token(self, server_port: int) -> Optional[str]:
        """Get authentication token by logging in"""
        print(f"🔐 Getting auth token from server on port {server_port}...")
        
        try:
            # Try to login with existing confirmed users first
            existing_token = self.try_existing_user_login(server_port)
            if existing_token:
                return existing_token
            
            # If that failed, try the test user
            login_payload = {
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
            
            # Try to login first
            response = requests.post(
                f"http://127.0.0.1:{server_port}/auth/login",
                json=login_payload,
                timeout=10
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                token = auth_data.get("access_token")
                if token:
                    print(f"✅ Got auth token from test user")
                    return token
            
            # If login failed, try to signup  
            signup_payload = {
                "email": "<EMAIL>", 
                "password": "testpassword123",
                "username": "testuser"
            }
            
            response = requests.post(
                f"http://127.0.0.1:{server_port}/auth/signup",
                json=signup_payload,
                timeout=10
            )
            
            if response.status_code == 201:
                auth_data = response.json()
                token = auth_data.get("access_token")
                # Check if token is not empty (signup might require email confirmation)
                if token and token.strip():
                    print(f"✅ Created new user and got auth token")
                    return token
                
                print("❌ User created but email confirmation required")
                # Let's try a different approach - use an existing confirmed user
                return self.try_existing_user_login(server_port)
            
            print(f"❌ Failed to get auth token: {response.status_code} - {response.text}")
            return None
            
        except Exception as e:
            print(f"❌ Error getting auth token: {str(e)}")
            return None
    
    def try_existing_user_login(self, server_port: int) -> Optional[str]:
        """Try to login with a real existing user account"""
        print("🔐 Trying to login with existing user...")
        
        # You can update these credentials to match a real confirmed user
        existing_users = [
            {"email": "pranav", "password": "gelbowRease9"},
            {"email": "pranav.beatgen", "password": "gelbowRease9"},
            # Add more existing confirmed users here
        ]
        
        for user in existing_users:
            try:
                # Use form data instead of JSON (OAuth2PasswordRequestForm)
                form_data = {
                    "username": user["email"],  # username field contains email
                    "password": user["password"]
                }
                
                response = requests.post(
                    f"http://127.0.0.1:{server_port}/auth/login",
                    data=form_data,  # Use data for form data, not json
                    timeout=10
                )
                
                if response.status_code == 200:
                    auth_data = response.json()
                    token = auth_data.get("access_token")
                    if token and token.strip():
                        print(f"✅ Logged in with existing user: {user['email']}")
                        return token
                else:
                    print(f"❌ Login failed for {user['email']}: {response.status_code} - {response.text}")
                        
            except Exception as e:
                print(f"❌ Exception logging in with {user['email']}: {str(e)}")
                continue
        
        print("❌ Could not login with any existing users")
        print("💡 To fix this: update the existing_users list with real confirmed user credentials")
        return None

    def create_request_on_server_a(self) -> Optional[str]:
        """Create an assistant request on Server A"""
        print("📝 Creating request on Server A...")
        
        # Get auth token first
        token = self.get_auth_token(self.server_a_port)
        if not token:
            return None
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            
            payload = {
                "mode": "generate",
                "prompt": "Create a simple test beat",
                "model": "claude-3-7-sonnet-latest"  # Use a valid model name
            }
            
            response = requests.post(
                f"http://127.0.0.1:{self.server_a_port}/assistant/request",
                json=payload,
                headers=headers,
                timeout=10
            )
            
            if response.status_code in [200, 201]:  # Accept both 200 OK and 201 Created
                request_data = response.json()
                request_id = request_data.get("request_id")
                print(f"✅ Request created on Server A: {request_id}")
                return request_id
            else:
                print(f"❌ Failed to create request: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error creating request: {str(e)}")
            return None
    
    async def connect_sse_on_server_b(self, request_id: str) -> bool:
        """Connect to SSE stream on Server B and verify events"""
        print(f"📡 Connecting to SSE stream on Server B for request: {request_id}")
        
        # Get auth token for Server B
        token = self.get_auth_token(self.server_b_port)
        if not token:
            print("❌ Could not get auth token for Server B")
            return False
        
        try:
            import aiohttp
            
            headers = {
                "Authorization": f"Bearer {token}"
            }
            
            timeout = aiohttp.ClientTimeout(total=30)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                url = f"http://127.0.0.1:{self.server_b_port}/assistant/stream/{request_id}"
                
                async with session.get(url, headers=headers) as response:
                    if response.status != 200:
                        print(f"❌ SSE connection failed: {response.status}")
                        return False
                    
                    print("✅ SSE connection established on Server B")
                    
                    # Read SSE events
                    events_received = 0
                    current_event_type = None
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        
                        # Parse SSE format properly
                        if line.startswith('event: '):
                            current_event_type = line[7:]  # Remove 'event: ' prefix
                        elif line.startswith('data: '):
                            # We have both event type and data
                            if current_event_type:
                                try:
                                    event_data = json.loads(line[6:])  # Remove 'data: ' prefix
                                    print(f"📨 Received event on Server B: {current_event_type}")
                                    events_received += 1
                                    
                                    # Stop after receiving a few events
                                    if events_received >= 3:
                                        print(f"✅ Successfully received {events_received} events via Redis!")
                                        return True
                                        
                                except json.JSONDecodeError:
                                    print(f"📨 Received event with non-JSON data: {current_event_type}")
                                    events_received += 1
                                    
                                    # Stop after receiving a few events
                                    if events_received >= 3:
                                        print(f"✅ Successfully received {events_received} events via Redis!")
                                        return True
                            else:
                                print(f"📨 Received data without event type: {line}")
                        
                        # Timeout after 20 seconds
                        if events_received == 0:
                            await asyncio.sleep(0.1)
                    
                    if events_received > 0:
                        print(f"✅ Test passed! Received {events_received} events")
                        return True
                    else:
                        print("❌ No events received within timeout")
                        return False
                        
        except Exception as e:
            print(f"❌ Error connecting to SSE: {str(e)}")
            return False
    
    async def run_test(self):
        """Run the complete distributed SSE test"""
        print("🧪 Starting Distributed SSE Test")
        print("=" * 50)
        
        try:
            # Start servers
            self.start_servers()
            
            # Create request on Server A
            request_id = self.create_request_on_server_a()
            if not request_id:
                print("❌ Test failed: Could not create request")
                return False
            
            # Small delay to ensure request is processed
            await asyncio.sleep(2)
            
            # Connect to SSE on Server B
            success = await self.connect_sse_on_server_b(request_id)
            
            if success:
                print("\n🎉 DISTRIBUTED SSE TEST PASSED!")
                print("✅ Events successfully flowed from Server A to Server B via Redis")
                return True
            else:
                print("\n❌ DISTRIBUTED SSE TEST FAILED!")
                print("❌ Events did not flow between servers")
                return False
                
        except KeyboardInterrupt:
            print("\n⏹️  Test interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Test failed with error: {str(e)}")
            return False
        finally:
            self.cleanup_servers()

def main():
    """Main test function"""
    
    # Check if aiohttp is installed
    try:
        import aiohttp
    except ImportError:
        print("❌ aiohttp is required for this test. Install with: pip install aiohttp")
        sys.exit(1)
    
    test = DistributedSSETest()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n⏹️  Stopping test...")
        test.cleanup_servers()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # Run the test
    success = asyncio.run(test.run_test())
    
    if success:
        print("\n✅ All tests passed! Your distributed SSE system is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Tests failed. Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()