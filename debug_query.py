#!/usr/bin/env python3
"""
Debug script to check the actual SQL being generated
"""
import sys
import os
sys.path.append('/Users/<USER>/Developer/beatgen/backend')

from sqlmodel import select
from app2.models.user import User
from sqlalchemy.dialects import postgresql

# Get the compiled SQL
statement = select(User).where(User.id == "b9c0588e-cd7c-4a3f-9bd9-a6f6dcc321ff")
compiled = statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True})

print("=== Generated SQL Query ===")
print(compiled)
print()

# Check what tables are being joined
print("=== Query Analysis ===")
print(f"Tables referenced: {statement.froms}")
print(f"Column clauses: {statement.column_clauses}")
print(f"Selected columns: {statement.selected_columns}")