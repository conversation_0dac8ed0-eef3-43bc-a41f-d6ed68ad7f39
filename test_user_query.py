#!/usr/bin/env python3
"""
Test script to isolate the User query performance issue
"""

import asyncio import time
import sys
import os
sys.path.append('/Users/<USER>/Developer/beatgen/backend')

from sqlmodel import select
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from app2.models.user import User

# Replace with your actual database URL
DATABASE_URL = "your_database_url_here"

async def test_user_query():
    """Test User query performance with different approaches"""
    engine = create_async_engine(DATABASE_URL)
    session_maker = async_sessionmaker(engine)
    
    user_id = "b9c0588e-cd7c-4a3f-9bd9-a6f6dcc321ff"
    
    async with session_maker() as session:
        print("Testing basic User query...")
        
        # Test 1: Basic query (current approach)
        start = time.time()
        statement = select(User).where(User.id == user_id)
        print(f"Query statement: {statement}")
        
        result = await session.execute(statement)
        query_time = time.time() - start
        print(f"Query execution: {query_time:.3f}s")
        
        user = result.scalars().first()
        total_time = time.time() - start
        print(f"Total time: {total_time:.3f}s")
        
        if user:
            print(f"Found user: {user.email}")
            
            # Test accessing relationships (this might trigger lazy loading)
            print("Testing relationship access...")
            rel_start = time.time()
            projects_count = len(user.projects) if user.projects else 0
            rel_time = time.time() - rel_start
            print(f"Projects count: {projects_count} (took {rel_time:.3f}s)")
        else:
            print("User not found")

if __name__ == "__main__":
    asyncio.run(test_user_query())