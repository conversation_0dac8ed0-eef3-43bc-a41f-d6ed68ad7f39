# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*
!.env.example
frontend/.env
backend/.env

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python cache files
__pycache__/
*.py[cod]
*$py.class

# Music related files
*.sf2
*.sf3
*.mid
*.wav
*.mp3
*.ogg
*.m4a
*.aac
*.flac
.aider*

# SSL certificates
.cert/

# Google Cloud service account keys
*-key.json
vertex-ai-*.json

assets/

dns-records-app.beatgen.com.txt