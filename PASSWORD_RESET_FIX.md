# Password Reset Fix

## The Issue
When testing password reset from localhost, the email contains a link to `https://app.beatgen.com/reset-password` (production). When you click it, the production app doesn't have the code to handle the password reset token.

## Solutions

### Option 1: Test with the same environment
**For local testing:**
1. Request password reset from `https://localhost:5173/login`
2. The email will contain a link to `https://localhost:5173/reset-password`
3. Click the link to reset your password locally

**For production testing:**
1. Request password reset from `https://app.beatgen.com/login`
2. The email will contain a link to `https://app.beatgen.com/reset-password`
3. Click the link to reset your password on production

### Option 2: Deploy the updated code
The code I just added to handle password reset tokens needs to be deployed to production:
- The `__root.tsx` update that redirects password reset tokens to `/reset-password`
- The `/reset-password` route itself

### Option 3: Manual testing
For now, you can manually test by:
1. Request a new password reset
2. When you get the email, copy the link
3. Replace `https://app.beatgen.com` with `https://localhost:5173`
4. Navigate to the modified link

## Next Steps
1. Deploy the updated frontend code to production
2. Test password reset from the production site
3. For local development, always test from localhost to get localhost links

The "otp_expired" error is because the link was already used or expired (1 hour limit).