# Password Reset Flow Test Plan

## Overview
This document outlines the steps to test the complete password reset functionality.

## Prerequisites
- Backend server running at https://localhost:8000
- Frontend server running at https://localhost:5173
- Valid email account for testing

## Test Steps

### 1. Request Password Reset
1. Navigate to https://localhost:5173/login
2. Click "Forgot password?" link below the password field
3. Enter a valid email address in the modal
4. Click "Send Reset Instructions"
5. Verify success message appears

### 2. Check Email
1. Check the email inbox for the password reset email
2. Verify the email contains a reset link
3. Verify the link points to: https://localhost:5173/reset-password#access_token=...&type=recovery

### 3. Reset Password
1. Click the reset link in the email
2. Verify you're redirected to the reset password page
3. Enter a new password that meets requirements:
   - At least 8 characters
   - Contains at least 3 of: uppercase, lowercase, numbers, special characters
4. Confirm the password
5. Click "Reset Password"
6. Verify success message appears
7. Verify automatic redirect to login page after 3 seconds

### 4. Verify New Password
1. On the login page, enter your email/username
2. Enter the new password
3. Click "Sign In"
4. Verify successful login and redirect to home page

## Expected API Calls

1. **POST /api/auth/forgot-password**
   - Request: `{ "email": "<EMAIL>", "redirect_to": "https://localhost:5173/reset-password" }`
   - Response: `{ "message": "If the email exists, a password reset link will be sent" }`

2. **POST /api/auth/reset-password**
   - Headers: `Authorization: Bearer <recovery_token>`
   - Request: `{ "password": "NewPassword123!" }`
   - Response: `{ "message": "Password has been reset successfully" }`

## Troubleshooting

### Common Issues:
1. **No email received**: Check Supabase dashboard for email logs
2. **Invalid token error**: Token may have expired (usually 1 hour)
3. **Password validation errors**: Ensure password meets complexity requirements
4. **CORS errors**: Check backend CORS configuration for frontend URL

### Debug Steps:
1. Check browser console for errors
2. Check network tab for API responses
3. Check backend logs: `docker logs beatgen-backend-1`
4. Check Supabase Auth logs in dashboard